{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\n// fix ssr render\nvar defaultContainer = canUseDom() ? window : null;\n\n/** Sticky header hooks */\nexport default function useSticky(sticky, prefixCls) {\n  var _ref = _typeof(sticky) === 'object' ? sticky : {},\n    _ref$offsetHeader = _ref.offsetHeader,\n    offsetHeader = _ref$offsetHeader === void 0 ? 0 : _ref$offsetHeader,\n    _ref$offsetSummary = _ref.offsetSummary,\n    offsetSummary = _ref$offsetSummary === void 0 ? 0 : _ref$offsetSummary,\n    _ref$offsetScroll = _ref.offsetScroll,\n    offsetScroll = _ref$offsetScroll === void 0 ? 0 : _ref$offsetScroll,\n    _ref$getContainer = _ref.getContainer,\n    getContainer = _ref$getContainer === void 0 ? function () {\n      return defaultContainer;\n    } : _ref$getContainer;\n  var container = getContainer() || defaultContainer;\n  var isSticky = !!sticky;\n  return React.useMemo(function () {\n    return {\n      isSticky: isSticky,\n      stickyClassName: isSticky ? \"\".concat(prefixCls, \"-sticky-holder\") : '',\n      offsetHeader: offsetHeader,\n      offsetSummary: offsetSummary,\n      offsetScroll: offsetScroll,\n      container: container\n    };\n  }, [isSticky, offsetScroll, offsetHeader, offsetSummary, prefixCls, container]);\n}", "map": {"version": 3, "names": ["_typeof", "React", "canUseDom", "defaultContainer", "window", "useSticky", "sticky", "prefixCls", "_ref", "_ref$offsetHeader", "offsetHeader", "_ref$offsetSummary", "offsetSummary", "_ref$offsetScroll", "offsetScroll", "_ref$getContainer", "getContainer", "container", "isSticky", "useMemo", "stickyClassName", "concat"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/rc-table/es/hooks/useSticky.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport canUseDom from \"rc-util/es/Dom/canUseDom\";\n// fix ssr render\nvar defaultContainer = canUseDom() ? window : null;\n\n/** Sticky header hooks */\nexport default function useSticky(sticky, prefixCls) {\n  var _ref = _typeof(sticky) === 'object' ? sticky : {},\n    _ref$offsetHeader = _ref.offsetHeader,\n    offsetHeader = _ref$offsetHeader === void 0 ? 0 : _ref$offsetHeader,\n    _ref$offsetSummary = _ref.offsetSummary,\n    offsetSummary = _ref$offsetSummary === void 0 ? 0 : _ref$offsetSummary,\n    _ref$offsetScroll = _ref.offsetScroll,\n    offsetScroll = _ref$offsetScroll === void 0 ? 0 : _ref$offsetScroll,\n    _ref$getContainer = _ref.getContainer,\n    getContainer = _ref$getContainer === void 0 ? function () {\n      return defaultContainer;\n    } : _ref$getContainer;\n  var container = getContainer() || defaultContainer;\n  var isSticky = !!sticky;\n  return React.useMemo(function () {\n    return {\n      isSticky: isSticky,\n      stickyClassName: isSticky ? \"\".concat(prefixCls, \"-sticky-holder\") : '',\n      offsetHeader: offsetHeader,\n      offsetSummary: offsetSummary,\n      offsetScroll: offsetScroll,\n      container: container\n    };\n  }, [isSticky, offsetScroll, offsetHeader, offsetSummary, prefixCls, container]);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,0BAA0B;AAChD;AACA,IAAIC,gBAAgB,GAAGD,SAAS,CAAC,CAAC,GAAGE,MAAM,GAAG,IAAI;;AAElD;AACA,eAAe,SAASC,SAASA,CAACC,MAAM,EAAEC,SAAS,EAAE;EACnD,IAAIC,IAAI,GAAGR,OAAO,CAACM,MAAM,CAAC,KAAK,QAAQ,GAAGA,MAAM,GAAG,CAAC,CAAC;IACnDG,iBAAiB,GAAGD,IAAI,CAACE,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;IACnEE,kBAAkB,GAAGH,IAAI,CAACI,aAAa;IACvCA,aAAa,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,kBAAkB;IACtEE,iBAAiB,GAAGL,IAAI,CAACM,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;IACnEE,iBAAiB,GAAGP,IAAI,CAACQ,YAAY;IACrCA,YAAY,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,YAAY;MACxD,OAAOZ,gBAAgB;IACzB,CAAC,GAAGY,iBAAiB;EACvB,IAAIE,SAAS,GAAGD,YAAY,CAAC,CAAC,IAAIb,gBAAgB;EAClD,IAAIe,QAAQ,GAAG,CAAC,CAACZ,MAAM;EACvB,OAAOL,KAAK,CAACkB,OAAO,CAAC,YAAY;IAC/B,OAAO;MACLD,QAAQ,EAAEA,QAAQ;MAClBE,eAAe,EAAEF,QAAQ,GAAG,EAAE,CAACG,MAAM,CAACd,SAAS,EAAE,gBAAgB,CAAC,GAAG,EAAE;MACvEG,YAAY,EAAEA,YAAY;MAC1BE,aAAa,EAAEA,aAAa;MAC5BE,YAAY,EAAEA,YAAY;MAC1BG,SAAS,EAAEA;IACb,CAAC;EACH,CAAC,EAAE,CAACC,QAAQ,EAAEJ,YAAY,EAAEJ,YAAY,EAAEE,aAAa,EAAEL,SAAS,EAAEU,SAAS,CAAC,CAAC;AACjF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}