{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\PlotList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Tooltip, Row, Col, Statistic, Descriptions, Progress, Timeline } from 'antd';\nimport { PlusOutlined, BookOutlined, EditOutlined, DeleteOutlined, EyeOutlined, RobotOutlined, BranchesOutlined, ClockCircleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst PlotList = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const [plots, setPlots] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingPlot, setEditingPlot] = useState(null);\n  const [viewingPlot, setViewingPlot] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟剧情数据\n  const mockPlots = [{\n    id: 1,\n    title: '主线：修仙之路',\n    type: 'main',\n    status: 'active',\n    priority: 'high',\n    progress: 65,\n    description: '主角从凡人踏上修仙之路的主要故事线',\n    outline: '凡人觉醒 -> 入门修炼 -> 宗门试炼 -> 历练成长 -> 面临挑战',\n    characters: ['林天', '云长老', '苏雪儿'],\n    chapters: ['第一章', '第二章', '第三章'],\n    conflicts: ['内心挣扎', '外部阻力'],\n    themes: ['成长', '坚持', '友情'],\n    startChapter: 1,\n    endChapter: 10,\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-20'\n  }, {\n    id: 2,\n    title: '支线：师门恩怨',\n    type: 'subplot',\n    status: 'planning',\n    priority: 'medium',\n    progress: 20,\n    description: '青云宗内部的恩怨情仇',\n    outline: '历史恩怨 -> 矛盾激化 -> 真相揭露 -> 和解或决裂',\n    characters: ['云长老', '苏长老', '王峰'],\n    chapters: ['第五章', '第八章'],\n    conflicts: ['师门内斗', '利益冲突'],\n    themes: ['恩怨', '正义', '选择'],\n    startChapter: 5,\n    endChapter: 12,\n    createdAt: '2024-01-16',\n    updatedAt: '2024-01-18'\n  }, {\n    id: 3,\n    title: '支线：情感线索',\n    type: 'subplot',\n    status: 'completed',\n    priority: 'low',\n    progress: 100,\n    description: '主角与苏雪儿的感情发展',\n    outline: '初遇 -> 相识 -> 相知 -> 相恋',\n    characters: ['林天', '苏雪儿'],\n    chapters: ['第二章', '第四章', '第六章'],\n    conflicts: ['身份差距', '外界阻力'],\n    themes: ['爱情', '成长', '坚持'],\n    startChapter: 2,\n    endChapter: 8,\n    createdAt: '2024-01-10',\n    updatedAt: '2024-01-19'\n  }];\n  useEffect(() => {\n    setPlots(mockPlots);\n  }, []);\n\n  // 剧情类型配置\n  const typeConfig = {\n    main: {\n      color: 'red',\n      text: '主线',\n      icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 45\n      }, this)\n    },\n    subplot: {\n      color: 'blue',\n      text: '支线',\n      icon: /*#__PURE__*/_jsxDEV(BranchesOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 49\n      }, this)\n    },\n    side: {\n      color: 'green',\n      text: '番外',\n      icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 47\n      }, this)\n    }\n  };\n\n  // 状态配置\n  const statusConfig = {\n    planning: {\n      color: 'blue',\n      text: '规划中',\n      icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 51\n      }, this)\n    },\n    active: {\n      color: 'orange',\n      text: '进行中',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 51\n      }, this)\n    },\n    completed: {\n      color: 'green',\n      text: '已完成',\n      icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 53\n      }, this)\n    },\n    paused: {\n      color: 'default',\n      text: '暂停',\n      icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 51\n      }, this)\n    }\n  };\n\n  // 优先级配置\n  const priorityConfig = {\n    high: {\n      color: 'red',\n      text: '高'\n    },\n    medium: {\n      color: 'orange',\n      text: '中'\n    },\n    low: {\n      color: 'default',\n      text: '低'\n    }\n  };\n\n  // 表格列配置\n  const columns = [{\n    title: '剧情标题',\n    dataIndex: 'title',\n    key: 'title',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [typeConfig[record.type].icon, /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '类型',\n    dataIndex: 'type',\n    key: 'type',\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: typeConfig[type].color,\n      children: typeConfig[type].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 154,\n      columnNumber: 9\n    }, this),\n    filters: [{\n      text: '主线',\n      value: 'main'\n    }, {\n      text: '支线',\n      value: 'subplot'\n    }, {\n      text: '番外',\n      value: 'side'\n    }],\n    onFilter: (value, record) => record.type === value\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: statusConfig[status].color,\n      icon: statusConfig[status].icon,\n      children: statusConfig[status].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 9\n    }, this),\n    filters: [{\n      text: '规划中',\n      value: 'planning'\n    }, {\n      text: '进行中',\n      value: 'active'\n    }, {\n      text: '已完成',\n      value: 'completed'\n    }, {\n      text: '暂停',\n      value: 'paused'\n    }],\n    onFilter: (value, record) => record.status === value\n  }, {\n    title: '优先级',\n    dataIndex: 'priority',\n    key: 'priority',\n    render: priority => /*#__PURE__*/_jsxDEV(Tag, {\n      color: priorityConfig[priority].color,\n      children: priorityConfig[priority].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '进度',\n    dataIndex: 'progress',\n    key: 'progress',\n    render: progress => /*#__PURE__*/_jsxDEV(Progress, {\n      percent: progress,\n      size: \"small\",\n      status: progress === 100 ? 'success' : 'active'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.progress - b.progress\n  }, {\n    title: '章节范围',\n    key: 'chapterRange',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Text, {\n      type: \"secondary\",\n      children: [\"\\u7B2C\", record.startChapter, \"\\u7AE0 - \\u7B2C\", record.endChapter, \"\\u7AE0\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '更新时间',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt',\n    sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleView(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"AI\\u751F\\u6210\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAIGenerate(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u5267\\u60C5\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理新建/编辑剧情\n  const handleCreateOrEdit = () => {\n    setEditingPlot(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = plot => {\n    setEditingPlot(plot);\n    form.setFieldsValue({\n      ...plot,\n      characters: plot.characters.join('\\n'),\n      chapters: plot.chapters.join('\\n'),\n      conflicts: plot.conflicts.join('\\n'),\n      themes: plot.themes.join('\\n')\n    });\n    setModalVisible(true);\n  };\n  const handleView = plot => {\n    setViewingPlot(plot);\n    setDetailModalVisible(true);\n  };\n  const handleAIGenerate = plot => {\n    message.info(`AI生成剧情详情：${plot.title}`);\n  };\n  const handleDelete = async id => {\n    try {\n      // 调用后端API删除数据\n      await axios.delete(`/api/project-data/projects/${projectId}/data/plot/${id}`);\n\n      // 删除成功后从列表中移除\n      setPlots(plots.filter(p => p.id !== id));\n      message.success('剧情删除成功');\n    } catch (error) {\n      console.error('删除剧情失败:', error);\n      message.error('删除剧情失败');\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 处理数组字段\n      const processedValues = {\n        ...values,\n        characters: values.characters ? values.characters.split('\\n').filter(item => item.trim()) : [],\n        chapters: values.chapters ? values.chapters.split('\\n').filter(item => item.trim()) : [],\n        conflicts: values.conflicts ? values.conflicts.split('\\n').filter(item => item.trim()) : [],\n        themes: values.themes ? values.themes.split('\\n').filter(item => item.trim()) : []\n      };\n      if (editingPlot) {\n        // 编辑剧情\n        setPlots(plots.map(p => p.id === editingPlot.id ? {\n          ...p,\n          ...processedValues,\n          updatedAt: new Date().toISOString().split('T')[0]\n        } : p));\n        message.success('剧情更新成功');\n      } else {\n        // 新建剧情\n        const newPlot = {\n          id: Date.now(),\n          ...processedValues,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setPlots([...plots, newPlot]);\n        message.success('剧情创建成功');\n      }\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalPlots = plots.length;\n  const mainPlots = plots.filter(p => p.type === 'main').length;\n  const subPlots = plots.filter(p => p.type === 'subplot').length;\n  const activePlots = plots.filter(p => p.status === 'active').length;\n  const completedPlots = plots.filter(p => p.status === 'completed').length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u5267\\u60C5\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5267\\u60C5\\u6570\",\n            value: totalPlots,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 371,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4E3B\\u7EBF\\u5267\\u60C5\",\n            value: mainPlots,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {\n              style: {\n                color: '#f5222d'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u652F\\u7EBF\\u5267\\u60C5\",\n            value: subPlots,\n            prefix: /*#__PURE__*/_jsxDEV(BranchesOutlined, {\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FDB\\u884C\\u4E2D\",\n            value: activePlots,\n            prefix: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n              style: {\n                color: '#fa8c16'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 393,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-left\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 21\n            }, this),\n            onClick: handleCreateOrEdit,\n            children: \"\\u6DFB\\u52A0\\u5267\\u60C5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: plots,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个剧情`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingPlot ? '编辑剧情' : '新建剧情',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => {\n        setModalVisible(false);\n        form.resetFields();\n      },\n      confirmLoading: loading,\n      width: 900,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          type: 'subplot',\n          status: 'planning',\n          priority: 'medium'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 16,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u5267\\u60C5\\u6807\\u9898\",\n              rules: [{\n                required: true,\n                message: '请输入剧情标题'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5267\\u60C5\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u5267\\u60C5\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择剧情类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"main\",\n                  children: \"\\u4E3B\\u7EBF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"subplot\",\n                  children: \"\\u652F\\u7EBF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"side\",\n                  children: \"\\u756A\\u5916\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"planning\",\n                  children: \"\\u89C4\\u5212\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"active\",\n                  children: \"\\u8FDB\\u884C\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"completed\",\n                  children: \"\\u5DF2\\u5B8C\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"paused\",\n                  children: \"\\u6682\\u505C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"priority\",\n              label: \"\\u4F18\\u5148\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请选择优先级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"high\",\n                  children: \"\\u9AD8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"medium\",\n                  children: \"\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"low\",\n                  children: \"\\u4F4E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"progress\",\n              label: \"\\u8FDB\\u5EA6 (%)\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                min: 0,\n                max: 100,\n                placeholder: \"0-100\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 476,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"startChapter\",\n              label: \"\\u8D77\\u59CB\\u7AE0\\u8282\",\n              rules: [{\n                required: true,\n                message: '请输入起始章节'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                min: 1,\n                placeholder: \"\\u5982\\uFF1A1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"endChapter\",\n              label: \"\\u7ED3\\u675F\\u7AE0\\u8282\",\n              rules: [{\n                required: true,\n                message: '请输入结束章节'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                min: 1,\n                placeholder: \"\\u5982\\uFF1A10\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u5267\\u60C5\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入剧情描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u7B80\\u8981\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u5267\\u60C5\\u7684\\u4E3B\\u8981\\u5185\\u5BB9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 535,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"outline\",\n          label: \"\\u5267\\u60C5\\u5927\\u7EB2\",\n          rules: [{\n            required: true,\n            message: '请输入剧情大纲'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u5267\\u60C5\\u7684\\u53D1\\u5C55\\u8109\\u7EDC\\uFF0C\\u5982\\uFF1A\\u5F00\\u7AEF -> \\u53D1\\u5C55 -> \\u9AD8\\u6F6E -> \\u7ED3\\u5C40\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"characters\",\n              label: \"\\u76F8\\u5173\\u4EBA\\u7269\",\n              tooltip: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u4EBA\\u7269\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u5982\\uFF1A\\u6797\\u5929\\n\\u82CF\\u96EA\\u513F\\n\\u4E91\\u957F\\u8001\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 564,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"chapters\",\n              label: \"\\u6D89\\u53CA\\u7AE0\\u8282\",\n              tooltip: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u7AE0\\u8282\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u5982\\uFF1A\\u7B2C\\u4E00\\u7AE0\\n\\u7B2C\\u4E8C\\u7AE0\\n\\u7B2C\\u4E09\\u7AE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 576,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"conflicts\",\n              label: \"\\u51B2\\u7A81\\u8981\\u7D20\",\n              tooltip: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u51B2\\u7A81\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 2,\n                placeholder: \"\\u5982\\uFF1A\\u5185\\u5FC3\\u6323\\u624E\\n\\u5916\\u90E8\\u963B\\u529B\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 591,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 586,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"themes\",\n              label: \"\\u4E3B\\u9898\\u5143\\u7D20\",\n              tooltip: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u4E3B\\u9898\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 2,\n                placeholder: \"\\u5982\\uFF1A\\u6210\\u957F\\n\\u53CB\\u60C5\\n\\u575A\\u6301\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 431,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5267\\u60C5\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 11\n      }, this)],\n      width: 900,\n      children: viewingPlot && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n          bordered: true,\n          column: 2,\n          style: {\n            marginBottom: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5267\\u60C5\\u6807\\u9898\",\n            span: 2,\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              children: [typeConfig[viewingPlot.type].icon, /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: viewingPlot.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: typeConfig[viewingPlot.type].color,\n                children: typeConfig[viewingPlot.type].text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 632,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: statusConfig[viewingPlot.status].color,\n                children: statusConfig[viewingPlot.status].text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 635,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: priorityConfig[viewingPlot.priority].color,\n                children: [\"\\u4F18\\u5148\\u7EA7\\uFF1A\", priorityConfig[viewingPlot.priority].text]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 629,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u7AE0\\u8282\\u8303\\u56F4\",\n            children: [\"\\u7B2C\", viewingPlot.startChapter, \"\\u7AE0 - \\u7B2C\", viewingPlot.endChapter, \"\\u7AE0\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u8FDB\\u5EA6\",\n            children: /*#__PURE__*/_jsxDEV(Progress, {\n              percent: viewingPlot.progress,\n              size: \"small\",\n              status: viewingPlot.progress === 100 ? 'success' : 'active'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n            children: viewingPlot.createdAt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n            children: viewingPlot.updatedAt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5267\\u60C5\\u63CF\\u8FF0\",\n            span: 2,\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingPlot.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5267\\u60C5\\u5927\\u7EB2\",\n            span: 2,\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingPlot.outline\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 627,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u76F8\\u5173\\u4EBA\\u7269\",\n              size: \"small\",\n              children: viewingPlot.characters && viewingPlot.characters.length > 0 ? /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: viewingPlot.characters.map((character, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: character\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 673,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u6682\\u65E0\\u76F8\\u5173\\u4EBA\\u7269\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 677,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 668,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u6D89\\u53CA\\u7AE0\\u8282\",\n              size: \"small\",\n              children: viewingPlot.chapters && viewingPlot.chapters.length > 0 ? /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: viewingPlot.chapters.map((chapter, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"green\",\n                  children: chapter\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 686,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 684,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u6682\\u65E0\\u6D89\\u53CA\\u7AE0\\u8282\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 690,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 682,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 681,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 667,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          style: {\n            marginTop: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u51B2\\u7A81\\u8981\\u7D20\",\n              size: \"small\",\n              children: viewingPlot.conflicts && viewingPlot.conflicts.length > 0 ? /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: viewingPlot.conflicts.map((conflict, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"orange\",\n                  children: conflict\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 702,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 700,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u6682\\u65E0\\u51B2\\u7A81\\u8981\\u7D20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 706,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 698,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 697,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u4E3B\\u9898\\u5143\\u7D20\",\n              size: \"small\",\n              children: viewingPlot.themes && viewingPlot.themes.length > 0 ? /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: viewingPlot.themes.map((theme, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"purple\",\n                  children: theme\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 713,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u6682\\u65E0\\u4E3B\\u9898\\u5143\\u7D20\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 719,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 696,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5267\\u60C5\\u53D1\\u5C55\\u65F6\\u95F4\\u7EBF\",\n          size: \"small\",\n          style: {\n            marginTop: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Timeline, {\n            children: [/*#__PURE__*/_jsxDEV(Timeline.Item, {\n              color: \"blue\",\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5267\\u60C5\\u521B\\u5EFA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 730,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: viewingPlot.createdAt\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 731,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 17\n            }, this), viewingPlot.status === 'active' && /*#__PURE__*/_jsxDEV(Timeline.Item, {\n              color: \"orange\",\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5267\\u60C5\\u8FDB\\u884C\\u4E2D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 735,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 736,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: [\"\\u8FDB\\u5EA6\\uFF1A\", viewingPlot.progress, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 737,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 734,\n              columnNumber: 19\n            }, this), viewingPlot.status === 'completed' && /*#__PURE__*/_jsxDEV(Timeline.Item, {\n              color: \"green\",\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5267\\u60C5\\u5B8C\\u6210\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 742,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 743,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: viewingPlot.updatedAt\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 744,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 741,\n              columnNumber: 19\n            }, this), viewingPlot.status === 'planning' && /*#__PURE__*/_jsxDEV(Timeline.Item, {\n              color: \"gray\",\n              children: [/*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u89C4\\u5212\\u9636\\u6BB5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 750,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u7B49\\u5F85\\u5F00\\u59CB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 727,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 614,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 359,\n    columnNumber: 5\n  }, this);\n};\n_s(PlotList, \"BXJgYenYQ9aH8svqmvypidrtuwE=\", false, function () {\n  return [useParams, Form.useForm];\n});\n_c = PlotList;\nexport default PlotList;\nvar _c;\n$RefreshReg$(_c, \"PlotList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "axios", "Card", "Typography", "<PERSON><PERSON>", "Table", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "<PERSON><PERSON><PERSON>", "Row", "Col", "Statistic", "Descriptions", "Progress", "Timeline", "PlusOutlined", "BookOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "RobotOutlined", "BranchesOutlined", "ClockCircleOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "TextArea", "Option", "PlotList", "_s", "id", "projectId", "plots", "setPlots", "loading", "setLoading", "modalVisible", "setModalVisible", "detailModalVisible", "setDetailModalVisible", "editingPlot", "setEditingPlot", "viewingPlot", "setViewingPlot", "form", "useForm", "mockPlots", "title", "type", "status", "priority", "progress", "description", "outline", "characters", "chapters", "conflicts", "themes", "startChapter", "endChapter", "createdAt", "updatedAt", "typeConfig", "main", "color", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "subplot", "side", "statusConfig", "planning", "active", "completed", "paused", "priorityConfig", "high", "medium", "low", "columns", "dataIndex", "key", "render", "record", "children", "strong", "filters", "value", "onFilter", "percent", "size", "sorter", "a", "b", "_", "Date", "onClick", "handleView", "handleEdit", "handleAIGenerate", "onConfirm", "handleDelete", "okText", "cancelText", "danger", "handleCreateOrEdit", "resetFields", "plot", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "info", "delete", "filter", "p", "success", "error", "console", "handleModalOk", "values", "validateFields", "processedValues", "split", "item", "trim", "map", "toISOString", "newPlot", "now", "totalPlots", "length", "mainPlots", "subPlots", "activePlots", "completedPlots", "className", "level", "gutter", "style", "marginBottom", "span", "prefix", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onOk", "onCancel", "confirmLoading", "width", "layout", "initialValues", "<PERSON><PERSON>", "name", "label", "rules", "required", "placeholder", "min", "max", "rows", "tooltip", "footer", "bordered", "column", "wrap", "character", "index", "chapter", "marginTop", "conflict", "theme", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/PlotList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  Card,\n  Typography,\n  Button,\n  Table,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Tooltip,\n  Row,\n  Col,\n  Statistic,\n  Descriptions,\n  Progress,\n  Timeline\n} from 'antd';\nimport {\n  PlusOutlined,\n  BookOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  RobotOutlined,\n  BranchesOutlined,\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst PlotList = () => {\n  const { id: projectId } = useParams();\n  const [plots, setPlots] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingPlot, setEditingPlot] = useState(null);\n  const [viewingPlot, setViewingPlot] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟剧情数据\n  const mockPlots = [\n    {\n      id: 1,\n      title: '主线：修仙之路',\n      type: 'main',\n      status: 'active',\n      priority: 'high',\n      progress: 65,\n      description: '主角从凡人踏上修仙之路的主要故事线',\n      outline: '凡人觉醒 -> 入门修炼 -> 宗门试炼 -> 历练成长 -> 面临挑战',\n      characters: ['林天', '云长老', '苏雪儿'],\n      chapters: ['第一章', '第二章', '第三章'],\n      conflicts: ['内心挣扎', '外部阻力'],\n      themes: ['成长', '坚持', '友情'],\n      startChapter: 1,\n      endChapter: 10,\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-20'\n    },\n    {\n      id: 2,\n      title: '支线：师门恩怨',\n      type: 'subplot',\n      status: 'planning',\n      priority: 'medium',\n      progress: 20,\n      description: '青云宗内部的恩怨情仇',\n      outline: '历史恩怨 -> 矛盾激化 -> 真相揭露 -> 和解或决裂',\n      characters: ['云长老', '苏长老', '王峰'],\n      chapters: ['第五章', '第八章'],\n      conflicts: ['师门内斗', '利益冲突'],\n      themes: ['恩怨', '正义', '选择'],\n      startChapter: 5,\n      endChapter: 12,\n      createdAt: '2024-01-16',\n      updatedAt: '2024-01-18'\n    },\n    {\n      id: 3,\n      title: '支线：情感线索',\n      type: 'subplot',\n      status: 'completed',\n      priority: 'low',\n      progress: 100,\n      description: '主角与苏雪儿的感情发展',\n      outline: '初遇 -> 相识 -> 相知 -> 相恋',\n      characters: ['林天', '苏雪儿'],\n      chapters: ['第二章', '第四章', '第六章'],\n      conflicts: ['身份差距', '外界阻力'],\n      themes: ['爱情', '成长', '坚持'],\n      startChapter: 2,\n      endChapter: 8,\n      createdAt: '2024-01-10',\n      updatedAt: '2024-01-19'\n    }\n  ];\n\n  useEffect(() => {\n    setPlots(mockPlots);\n  }, []);\n\n  // 剧情类型配置\n  const typeConfig = {\n    main: { color: 'red', text: '主线', icon: <BookOutlined /> },\n    subplot: { color: 'blue', text: '支线', icon: <BranchesOutlined /> },\n    side: { color: 'green', text: '番外', icon: <BookOutlined /> }\n  };\n\n  // 状态配置\n  const statusConfig = {\n    planning: { color: 'blue', text: '规划中', icon: <ClockCircleOutlined /> },\n    active: { color: 'orange', text: '进行中', icon: <ExclamationCircleOutlined /> },\n    completed: { color: 'green', text: '已完成', icon: <CheckCircleOutlined /> },\n    paused: { color: 'default', text: '暂停', icon: <ClockCircleOutlined /> }\n  };\n\n  // 优先级配置\n  const priorityConfig = {\n    high: { color: 'red', text: '高' },\n    medium: { color: 'orange', text: '中' },\n    low: { color: 'default', text: '低' }\n  };\n\n  // 表格列配置\n  const columns = [\n    {\n      title: '剧情标题',\n      dataIndex: 'title',\n      key: 'title',\n      render: (text, record) => (\n        <Space>\n          {typeConfig[record.type].icon}\n          <Text strong>{text}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type) => (\n        <Tag color={typeConfig[type].color}>\n          {typeConfig[type].text}\n        </Tag>\n      ),\n      filters: [\n        { text: '主线', value: 'main' },\n        { text: '支线', value: 'subplot' },\n        { text: '番外', value: 'side' }\n      ],\n      onFilter: (value, record) => record.type === value\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={statusConfig[status].color} icon={statusConfig[status].icon}>\n          {statusConfig[status].text}\n        </Tag>\n      ),\n      filters: [\n        { text: '规划中', value: 'planning' },\n        { text: '进行中', value: 'active' },\n        { text: '已完成', value: 'completed' },\n        { text: '暂停', value: 'paused' }\n      ],\n      onFilter: (value, record) => record.status === value\n    },\n    {\n      title: '优先级',\n      dataIndex: 'priority',\n      key: 'priority',\n      render: (priority) => (\n        <Tag color={priorityConfig[priority].color}>\n          {priorityConfig[priority].text}\n        </Tag>\n      )\n    },\n    {\n      title: '进度',\n      dataIndex: 'progress',\n      key: 'progress',\n      render: (progress) => (\n        <Progress\n          percent={progress}\n          size=\"small\"\n          status={progress === 100 ? 'success' : 'active'}\n        />\n      ),\n      sorter: (a, b) => a.progress - b.progress\n    },\n    {\n      title: '章节范围',\n      key: 'chapterRange',\n      render: (_, record) => (\n        <Text type=\"secondary\">\n          第{record.startChapter}章 - 第{record.endChapter}章\n        </Text>\n      )\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"AI生成\">\n            <Button\n              type=\"text\"\n              icon={<RobotOutlined />}\n              onClick={() => handleAIGenerate(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个剧情吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  // 处理新建/编辑剧情\n  const handleCreateOrEdit = () => {\n    setEditingPlot(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (plot) => {\n    setEditingPlot(plot);\n    form.setFieldsValue({\n      ...plot,\n      characters: plot.characters.join('\\n'),\n      chapters: plot.chapters.join('\\n'),\n      conflicts: plot.conflicts.join('\\n'),\n      themes: plot.themes.join('\\n')\n    });\n    setModalVisible(true);\n  };\n\n  const handleView = (plot) => {\n    setViewingPlot(plot);\n    setDetailModalVisible(true);\n  };\n\n  const handleAIGenerate = (plot) => {\n    message.info(`AI生成剧情详情：${plot.title}`);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 调用后端API删除数据\n      await axios.delete(`/api/project-data/projects/${projectId}/data/plot/${id}`);\n\n      // 删除成功后从列表中移除\n      setPlots(plots.filter(p => p.id !== id));\n      message.success('剧情删除成功');\n    } catch (error) {\n      console.error('删除剧情失败:', error);\n      message.error('删除剧情失败');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 处理数组字段\n      const processedValues = {\n        ...values,\n        characters: values.characters ? values.characters.split('\\n').filter(item => item.trim()) : [],\n        chapters: values.chapters ? values.chapters.split('\\n').filter(item => item.trim()) : [],\n        conflicts: values.conflicts ? values.conflicts.split('\\n').filter(item => item.trim()) : [],\n        themes: values.themes ? values.themes.split('\\n').filter(item => item.trim()) : []\n      };\n\n      if (editingPlot) {\n        // 编辑剧情\n        setPlots(plots.map(p =>\n          p.id === editingPlot.id\n            ? { ...p, ...processedValues, updatedAt: new Date().toISOString().split('T')[0] }\n            : p\n        ));\n        message.success('剧情更新成功');\n      } else {\n        // 新建剧情\n        const newPlot = {\n          id: Date.now(),\n          ...processedValues,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setPlots([...plots, newPlot]);\n        message.success('剧情创建成功');\n      }\n\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalPlots = plots.length;\n  const mainPlots = plots.filter(p => p.type === 'main').length;\n  const subPlots = plots.filter(p => p.type === 'subplot').length;\n  const activePlots = plots.filter(p => p.status === 'active').length;\n  const completedPlots = plots.filter(p => p.status === 'completed').length;\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">剧情管理</Title>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总剧情数\"\n              value={totalPlots}\n              prefix={<BookOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"主线剧情\"\n              value={mainPlots}\n              prefix={<BookOutlined style={{ color: '#f5222d' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"支线剧情\"\n              value={subPlots}\n              prefix={<BranchesOutlined style={{ color: '#1890ff' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"进行中\"\n              value={activePlots}\n              prefix={<ExclamationCircleOutlined style={{ color: '#fa8c16' }} />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreateOrEdit}\n            >\n              添加剧情\n            </Button>\n          </div>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={plots}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个剧情`\n          }}\n        />\n      </Card>\n\n      {/* 新建/编辑剧情模态框 */}\n      <Modal\n        title={editingPlot ? '编辑剧情' : '新建剧情'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => {\n          setModalVisible(false);\n          form.resetFields();\n        }}\n        confirmLoading={loading}\n        width={900}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            type: 'subplot',\n            status: 'planning',\n            priority: 'medium'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"title\"\n                label=\"剧情标题\"\n                rules={[{ required: true, message: '请输入剧情标题' }]}\n              >\n                <Input placeholder=\"请输入剧情标题\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"type\"\n                label=\"剧情类型\"\n                rules={[{ required: true, message: '请选择剧情类型' }]}\n              >\n                <Select>\n                  <Option value=\"main\">主线</Option>\n                  <Option value=\"subplot\">支线</Option>\n                  <Option value=\"side\">番外</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择状态' }]}\n              >\n                <Select>\n                  <Option value=\"planning\">规划中</Option>\n                  <Option value=\"active\">进行中</Option>\n                  <Option value=\"completed\">已完成</Option>\n                  <Option value=\"paused\">暂停</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"priority\"\n                label=\"优先级\"\n                rules={[{ required: true, message: '请选择优先级' }]}\n              >\n                <Select>\n                  <Option value=\"high\">高</Option>\n                  <Option value=\"medium\">中</Option>\n                  <Option value=\"low\">低</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"progress\"\n                label=\"进度 (%)\"\n              >\n                <Input type=\"number\" min={0} max={100} placeholder=\"0-100\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"startChapter\"\n                label=\"起始章节\"\n                rules={[{ required: true, message: '请输入起始章节' }]}\n              >\n                <Input type=\"number\" min={1} placeholder=\"如：1\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"endChapter\"\n                label=\"结束章节\"\n                rules={[{ required: true, message: '请输入结束章节' }]}\n              >\n                <Input type=\"number\" min={1} placeholder=\"如：10\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"剧情描述\"\n            rules={[{ required: true, message: '请输入剧情描述' }]}\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"请简要描述这个剧情的主要内容\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"outline\"\n            label=\"剧情大纲\"\n            rules={[{ required: true, message: '请输入剧情大纲' }]}\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"请描述剧情的发展脉络，如：开端 -> 发展 -> 高潮 -> 结局\"\n            />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"characters\"\n                label=\"相关人物\"\n                tooltip=\"每行一个人物名称\"\n              >\n                <TextArea\n                  rows={3}\n                  placeholder=\"如：林天&#10;苏雪儿&#10;云长老\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"chapters\"\n                label=\"涉及章节\"\n                tooltip=\"每行一个章节\"\n              >\n                <TextArea\n                  rows={3}\n                  placeholder=\"如：第一章&#10;第二章&#10;第三章\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"conflicts\"\n                label=\"冲突要素\"\n                tooltip=\"每行一个冲突\"\n              >\n                <TextArea\n                  rows={2}\n                  placeholder=\"如：内心挣扎&#10;外部阻力\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"themes\"\n                label=\"主题元素\"\n                tooltip=\"每行一个主题\"\n              >\n                <TextArea\n                  rows={2}\n                  placeholder=\"如：成长&#10;友情&#10;坚持\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n\n      {/* 剧情详情查看模态框 */}\n      <Modal\n        title=\"剧情详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={900}\n      >\n        {viewingPlot && (\n          <div>\n            <Descriptions bordered column={2} style={{ marginBottom: 24 }}>\n              <Descriptions.Item label=\"剧情标题\" span={2}>\n                <Space>\n                  {typeConfig[viewingPlot.type].icon}\n                  <Text strong>{viewingPlot.title}</Text>\n                  <Tag color={typeConfig[viewingPlot.type].color}>\n                    {typeConfig[viewingPlot.type].text}\n                  </Tag>\n                  <Tag color={statusConfig[viewingPlot.status].color}>\n                    {statusConfig[viewingPlot.status].text}\n                  </Tag>\n                  <Tag color={priorityConfig[viewingPlot.priority].color}>\n                    优先级：{priorityConfig[viewingPlot.priority].text}\n                  </Tag>\n                </Space>\n              </Descriptions.Item>\n\n              <Descriptions.Item label=\"章节范围\">\n                第{viewingPlot.startChapter}章 - 第{viewingPlot.endChapter}章\n              </Descriptions.Item>\n              <Descriptions.Item label=\"进度\">\n                <Progress\n                  percent={viewingPlot.progress}\n                  size=\"small\"\n                  status={viewingPlot.progress === 100 ? 'success' : 'active'}\n                />\n              </Descriptions.Item>\n\n              <Descriptions.Item label=\"创建时间\">{viewingPlot.createdAt}</Descriptions.Item>\n              <Descriptions.Item label=\"更新时间\">{viewingPlot.updatedAt}</Descriptions.Item>\n\n              <Descriptions.Item label=\"剧情描述\" span={2}>\n                <Paragraph>{viewingPlot.description}</Paragraph>\n              </Descriptions.Item>\n\n              <Descriptions.Item label=\"剧情大纲\" span={2}>\n                <Paragraph>{viewingPlot.outline}</Paragraph>\n              </Descriptions.Item>\n            </Descriptions>\n\n            <Row gutter={16}>\n              <Col span={12}>\n                <Card title=\"相关人物\" size=\"small\">\n                  {viewingPlot.characters && viewingPlot.characters.length > 0 ? (\n                    <Space wrap>\n                      {viewingPlot.characters.map((character, index) => (\n                        <Tag key={index} color=\"blue\">{character}</Tag>\n                      ))}\n                    </Space>\n                  ) : (\n                    <Text type=\"secondary\">暂无相关人物</Text>\n                  )}\n                </Card>\n              </Col>\n              <Col span={12}>\n                <Card title=\"涉及章节\" size=\"small\">\n                  {viewingPlot.chapters && viewingPlot.chapters.length > 0 ? (\n                    <Space wrap>\n                      {viewingPlot.chapters.map((chapter, index) => (\n                        <Tag key={index} color=\"green\">{chapter}</Tag>\n                      ))}\n                    </Space>\n                  ) : (\n                    <Text type=\"secondary\">暂无涉及章节</Text>\n                  )}\n                </Card>\n              </Col>\n            </Row>\n\n            <Row gutter={16} style={{ marginTop: 16 }}>\n              <Col span={12}>\n                <Card title=\"冲突要素\" size=\"small\">\n                  {viewingPlot.conflicts && viewingPlot.conflicts.length > 0 ? (\n                    <Space wrap>\n                      {viewingPlot.conflicts.map((conflict, index) => (\n                        <Tag key={index} color=\"orange\">{conflict}</Tag>\n                      ))}\n                    </Space>\n                  ) : (\n                    <Text type=\"secondary\">暂无冲突要素</Text>\n                  )}\n                </Card>\n              </Col>\n              <Col span={12}>\n                <Card title=\"主题元素\" size=\"small\">\n                  {viewingPlot.themes && viewingPlot.themes.length > 0 ? (\n                    <Space wrap>\n                      {viewingPlot.themes.map((theme, index) => (\n                        <Tag key={index} color=\"purple\">{theme}</Tag>\n                      ))}\n                    </Space>\n                  ) : (\n                    <Text type=\"secondary\">暂无主题元素</Text>\n                  )}\n                </Card>\n              </Col>\n            </Row>\n\n            {/* 剧情发展时间线 */}\n            <Card title=\"剧情发展时间线\" size=\"small\" style={{ marginTop: 16 }}>\n              <Timeline>\n                <Timeline.Item color=\"blue\">\n                  <Text strong>剧情创建</Text>\n                  <br />\n                  <Text type=\"secondary\">{viewingPlot.createdAt}</Text>\n                </Timeline.Item>\n                {viewingPlot.status === 'active' && (\n                  <Timeline.Item color=\"orange\">\n                    <Text strong>剧情进行中</Text>\n                    <br />\n                    <Text type=\"secondary\">进度：{viewingPlot.progress}%</Text>\n                  </Timeline.Item>\n                )}\n                {viewingPlot.status === 'completed' && (\n                  <Timeline.Item color=\"green\">\n                    <Text strong>剧情完成</Text>\n                    <br />\n                    <Text type=\"secondary\">{viewingPlot.updatedAt}</Text>\n                  </Timeline.Item>\n                )}\n                {viewingPlot.status === 'planning' && (\n                  <Timeline.Item color=\"gray\">\n                    <Text strong>规划阶段</Text>\n                    <br />\n                    <Text type=\"secondary\">等待开始</Text>\n                  </Timeline.Item>\n                )}\n              </Timeline>\n            </Card>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default PlotList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACRC,QAAQ,QACH,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,gBAAgB,EAChBC,mBAAmB,EACnBC,mBAAmB,EACnBC,yBAAyB,QACpB,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGhC,UAAU;AAC7C,MAAM;EAAEiC;AAAS,CAAC,GAAG1B,KAAK;AAC1B,MAAM;EAAE2B;AAAO,CAAC,GAAG1B,MAAM;AAEzB,MAAM2B,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAGzC,SAAS,CAAC,CAAC;EACrC,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8C,OAAO,EAAEC,UAAU,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACkD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACwD,IAAI,CAAC,GAAG7C,IAAI,CAAC8C,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,SAAS,GAAG,CAChB;IACEhB,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,mBAAmB;IAChCC,OAAO,EAAE,sCAAsC;IAC/CC,UAAU,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;IAChCC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC/BC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAC3BC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1BC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,UAAU;IAClBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,EAAE;IACZC,WAAW,EAAE,YAAY;IACzBC,OAAO,EAAE,+BAA+B;IACxCC,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;IAChCC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;IACxBC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAC3BC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1BC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,GAAG;IACbC,WAAW,EAAE,aAAa;IAC1BC,OAAO,EAAE,sBAAsB;IAC/BC,UAAU,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;IACzBC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC/BC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAC3BC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC1BC,YAAY,EAAE,CAAC;IACfC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EAEDxE,SAAS,CAAC,MAAM;IACd4C,QAAQ,CAACa,SAAS,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMgB,UAAU,GAAG;IACjBC,IAAI,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE5C,OAAA,CAACV,YAAY;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC1DC,OAAO,EAAE;MAAEP,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE5C,OAAA,CAACL,gBAAgB;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAClEE,IAAI,EAAE;MAAER,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE5C,OAAA,CAACV,YAAY;QAAAuD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;EAC7D,CAAC;;EAED;EACA,MAAMG,YAAY,GAAG;IACnBC,QAAQ,EAAE;MAAEV,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,eAAE5C,OAAA,CAACJ,mBAAmB;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACvEK,MAAM,EAAE;MAAEX,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,eAAE5C,OAAA,CAACF,yBAAyB;QAAA+C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC7EM,SAAS,EAAE;MAAEZ,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,eAAE5C,OAAA,CAACH,mBAAmB;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACzEO,MAAM,EAAE;MAAEb,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE5C,OAAA,CAACJ,mBAAmB;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;EACxE,CAAC;;EAED;EACA,MAAMQ,cAAc,GAAG;IACrBC,IAAI,EAAE;MAAEf,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAI,CAAC;IACjCe,MAAM,EAAE;MAAEhB,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAC;IACtCgB,GAAG,EAAE;MAAEjB,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI;EACrC,CAAC;;EAED;EACA,MAAMiB,OAAO,GAAG,CACd;IACEnC,KAAK,EAAE,MAAM;IACboC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACpB,IAAI,EAAEqB,MAAM,kBACnBhE,OAAA,CAAC1B,KAAK;MAAA2F,QAAA,GACHzB,UAAU,CAACwB,MAAM,CAACtC,IAAI,CAAC,CAACkB,IAAI,eAC7B5C,OAAA,CAACE,IAAI;QAACgE,MAAM;QAAAD,QAAA,EAAEtB;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAEX,CAAC,EACD;IACEvB,KAAK,EAAE,IAAI;IACXoC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGrC,IAAI,iBACX1B,OAAA,CAACzB,GAAG;MAACmE,KAAK,EAAEF,UAAU,CAACd,IAAI,CAAC,CAACgB,KAAM;MAAAuB,QAAA,EAChCzB,UAAU,CAACd,IAAI,CAAC,CAACiB;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACN;IACDmB,OAAO,EAAE,CACP;MAAExB,IAAI,EAAE,IAAI;MAAEyB,KAAK,EAAE;IAAO,CAAC,EAC7B;MAAEzB,IAAI,EAAE,IAAI;MAAEyB,KAAK,EAAE;IAAU,CAAC,EAChC;MAAEzB,IAAI,EAAE,IAAI;MAAEyB,KAAK,EAAE;IAAO,CAAC,CAC9B;IACDC,QAAQ,EAAEA,CAACD,KAAK,EAAEJ,MAAM,KAAKA,MAAM,CAACtC,IAAI,KAAK0C;EAC/C,CAAC,EACD;IACE3C,KAAK,EAAE,IAAI;IACXoC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGpC,MAAM,iBACb3B,OAAA,CAACzB,GAAG;MAACmE,KAAK,EAAES,YAAY,CAACxB,MAAM,CAAC,CAACe,KAAM;MAACE,IAAI,EAAEO,YAAY,CAACxB,MAAM,CAAC,CAACiB,IAAK;MAAAqB,QAAA,EACrEd,YAAY,CAACxB,MAAM,CAAC,CAACgB;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CACN;IACDmB,OAAO,EAAE,CACP;MAAExB,IAAI,EAAE,KAAK;MAAEyB,KAAK,EAAE;IAAW,CAAC,EAClC;MAAEzB,IAAI,EAAE,KAAK;MAAEyB,KAAK,EAAE;IAAS,CAAC,EAChC;MAAEzB,IAAI,EAAE,KAAK;MAAEyB,KAAK,EAAE;IAAY,CAAC,EACnC;MAAEzB,IAAI,EAAE,IAAI;MAAEyB,KAAK,EAAE;IAAS,CAAC,CAChC;IACDC,QAAQ,EAAEA,CAACD,KAAK,EAAEJ,MAAM,KAAKA,MAAM,CAACrC,MAAM,KAAKyC;EACjD,CAAC,EACD;IACE3C,KAAK,EAAE,KAAK;IACZoC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGnC,QAAQ,iBACf5B,OAAA,CAACzB,GAAG;MAACmE,KAAK,EAAEc,cAAc,CAAC5B,QAAQ,CAAC,CAACc,KAAM;MAAAuB,QAAA,EACxCT,cAAc,CAAC5B,QAAQ,CAAC,CAACe;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B;EAET,CAAC,EACD;IACEvB,KAAK,EAAE,IAAI;IACXoC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGlC,QAAQ,iBACf7B,OAAA,CAACb,QAAQ;MACPmF,OAAO,EAAEzC,QAAS;MAClB0C,IAAI,EAAC,OAAO;MACZ5C,MAAM,EAAEE,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG;IAAS;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CACF;IACDwB,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5C,QAAQ,GAAG6C,CAAC,CAAC7C;EACnC,CAAC,EACD;IACEJ,KAAK,EAAE,MAAM;IACbqC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAEA,CAACY,CAAC,EAAEX,MAAM,kBAChBhE,OAAA,CAACE,IAAI;MAACwB,IAAI,EAAC,WAAW;MAAAuC,QAAA,GAAC,QACpB,EAACD,MAAM,CAAC5B,YAAY,EAAC,iBAAK,EAAC4B,MAAM,CAAC3B,UAAU,EAAC,QAChD;IAAA;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEV,CAAC,EACD;IACEvB,KAAK,EAAE,MAAM;IACboC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBU,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIE,IAAI,CAACH,CAAC,CAAClC,SAAS,CAAC,GAAG,IAAIqC,IAAI,CAACF,CAAC,CAACnC,SAAS;EAChE,CAAC,EACD;IACEd,KAAK,EAAE,IAAI;IACXqC,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACY,CAAC,EAAEX,MAAM,kBAChBhE,OAAA,CAAC1B,KAAK;MAAA2F,QAAA,gBACJjE,OAAA,CAAClB,OAAO;QAAC2C,KAAK,EAAC,0BAAM;QAAAwC,QAAA,eACnBjE,OAAA,CAAC5B,MAAM;UACLsD,IAAI,EAAC,MAAM;UACXkB,IAAI,eAAE5C,OAAA,CAACP,WAAW;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtB6B,OAAO,EAAEA,CAAA,KAAMC,UAAU,CAACd,MAAM;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVhD,OAAA,CAAClB,OAAO;QAAC2C,KAAK,EAAC,cAAI;QAAAwC,QAAA,eACjBjE,OAAA,CAAC5B,MAAM;UACLsD,IAAI,EAAC,MAAM;UACXkB,IAAI,eAAE5C,OAAA,CAACT,YAAY;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB6B,OAAO,EAAEA,CAAA,KAAME,UAAU,CAACf,MAAM;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVhD,OAAA,CAAClB,OAAO;QAAC2C,KAAK,EAAC,gBAAM;QAAAwC,QAAA,eACnBjE,OAAA,CAAC5B,MAAM;UACLsD,IAAI,EAAC,MAAM;UACXkB,IAAI,eAAE5C,OAAA,CAACN,aAAa;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxB6B,OAAO,EAAEA,CAAA,KAAMG,gBAAgB,CAAChB,MAAM;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVhD,OAAA,CAACnB,UAAU;QACT4C,KAAK,EAAC,8DAAY;QAClBwD,SAAS,EAAEA,CAAA,KAAMC,YAAY,CAAClB,MAAM,CAACxD,EAAE,CAAE;QACzC2E,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAnB,QAAA,eAEfjE,OAAA,CAAClB,OAAO;UAAC2C,KAAK,EAAC,cAAI;UAAAwC,QAAA,eACjBjE,OAAA,CAAC5B,MAAM;YACLsD,IAAI,EAAC,MAAM;YACX2D,MAAM;YACNzC,IAAI,eAAE5C,OAAA,CAACR,cAAc;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnE,cAAc,CAAC,IAAI,CAAC;IACpBG,IAAI,CAACiE,WAAW,CAAC,CAAC;IAClBxE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMgE,UAAU,GAAIS,IAAI,IAAK;IAC3BrE,cAAc,CAACqE,IAAI,CAAC;IACpBlE,IAAI,CAACmE,cAAc,CAAC;MAClB,GAAGD,IAAI;MACPxD,UAAU,EAAEwD,IAAI,CAACxD,UAAU,CAAC0D,IAAI,CAAC,IAAI,CAAC;MACtCzD,QAAQ,EAAEuD,IAAI,CAACvD,QAAQ,CAACyD,IAAI,CAAC,IAAI,CAAC;MAClCxD,SAAS,EAAEsD,IAAI,CAACtD,SAAS,CAACwD,IAAI,CAAC,IAAI,CAAC;MACpCvD,MAAM,EAAEqD,IAAI,CAACrD,MAAM,CAACuD,IAAI,CAAC,IAAI;IAC/B,CAAC,CAAC;IACF3E,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+D,UAAU,GAAIU,IAAI,IAAK;IAC3BnE,cAAc,CAACmE,IAAI,CAAC;IACpBvE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM+D,gBAAgB,GAAIQ,IAAI,IAAK;IACjC5G,OAAO,CAAC+G,IAAI,CAAC,YAAYH,IAAI,CAAC/D,KAAK,EAAE,CAAC;EACxC,CAAC;EAED,MAAMyD,YAAY,GAAG,MAAO1E,EAAE,IAAK;IACjC,IAAI;MACF;MACA,MAAMvC,KAAK,CAAC2H,MAAM,CAAC,8BAA8BnF,SAAS,cAAcD,EAAE,EAAE,CAAC;;MAE7E;MACAG,QAAQ,CAACD,KAAK,CAACmF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtF,EAAE,KAAKA,EAAE,CAAC,CAAC;MACxC5B,OAAO,CAACmH,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpH,OAAO,CAACoH,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM7E,IAAI,CAAC8E,cAAc,CAAC,CAAC;MAC1CvF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMwF,eAAe,GAAG;QACtB,GAAGF,MAAM;QACTnE,UAAU,EAAEmE,MAAM,CAACnE,UAAU,GAAGmE,MAAM,CAACnE,UAAU,CAACsE,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC9FvE,QAAQ,EAAEkE,MAAM,CAAClE,QAAQ,GAAGkE,MAAM,CAAClE,QAAQ,CAACqE,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QACxFtE,SAAS,EAAEiE,MAAM,CAACjE,SAAS,GAAGiE,MAAM,CAACjE,SAAS,CAACoE,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC3FrE,MAAM,EAAEgE,MAAM,CAAChE,MAAM,GAAGgE,MAAM,CAAChE,MAAM,CAACmE,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG;MAClF,CAAC;MAED,IAAItF,WAAW,EAAE;QACf;QACAP,QAAQ,CAACD,KAAK,CAAC+F,GAAG,CAACX,CAAC,IAClBA,CAAC,CAACtF,EAAE,KAAKU,WAAW,CAACV,EAAE,GACnB;UAAE,GAAGsF,CAAC;UAAE,GAAGO,eAAe;UAAE9D,SAAS,EAAE,IAAIqC,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAAE,CAAC,GAC/ER,CACN,CAAC,CAAC;QACFlH,OAAO,CAACmH,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMY,OAAO,GAAG;UACdnG,EAAE,EAAEoE,IAAI,CAACgC,GAAG,CAAC,CAAC;UACd,GAAGP,eAAe;UAClBxE,QAAQ,EAAE,CAAC;UACXS,SAAS,EAAE,IAAIsC,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjD/D,SAAS,EAAE,IAAIqC,IAAI,CAAC,CAAC,CAAC8B,WAAW,CAAC,CAAC,CAACJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACD3F,QAAQ,CAAC,CAAC,GAAGD,KAAK,EAAEiG,OAAO,CAAC,CAAC;QAC7B/H,OAAO,CAACmH,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEAhF,eAAe,CAAC,KAAK,CAAC;MACtBO,IAAI,CAACiE,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRnF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgG,UAAU,GAAGnG,KAAK,CAACoG,MAAM;EAC/B,MAAMC,SAAS,GAAGrG,KAAK,CAACmF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpE,IAAI,KAAK,MAAM,CAAC,CAACoF,MAAM;EAC7D,MAAME,QAAQ,GAAGtG,KAAK,CAACmF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpE,IAAI,KAAK,SAAS,CAAC,CAACoF,MAAM;EAC/D,MAAMG,WAAW,GAAGvG,KAAK,CAACmF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnE,MAAM,KAAK,QAAQ,CAAC,CAACmF,MAAM;EACnE,MAAMI,cAAc,GAAGxG,KAAK,CAACmF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnE,MAAM,KAAK,WAAW,CAAC,CAACmF,MAAM;EAEzE,oBACE9G,OAAA;IAAKmH,SAAS,EAAC,SAAS;IAAAlD,QAAA,gBACtBjE,OAAA;MAAKmH,SAAS,EAAC,aAAa;MAAAlD,QAAA,eAC1BjE,OAAA,CAACC,KAAK;QAACmH,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAAlD,QAAA,EAAC;MAAI;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGNhD,OAAA,CAACjB,GAAG;MAACsI,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAtD,QAAA,gBAC3CjE,OAAA,CAAChB,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAAvD,QAAA,eACXjE,OAAA,CAAC9B,IAAI;UAAA+F,QAAA,eACHjE,OAAA,CAACf,SAAS;YACRwC,KAAK,EAAC,0BAAM;YACZ2C,KAAK,EAAEyC,UAAW;YAClBY,MAAM,eAAEzH,OAAA,CAACV,YAAY;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNhD,OAAA,CAAChB,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAAvD,QAAA,eACXjE,OAAA,CAAC9B,IAAI;UAAA+F,QAAA,eACHjE,OAAA,CAACf,SAAS;YACRwC,KAAK,EAAC,0BAAM;YACZ2C,KAAK,EAAE2C,SAAU;YACjBU,MAAM,eAAEzH,OAAA,CAACV,YAAY;cAACgI,KAAK,EAAE;gBAAE5E,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNhD,OAAA,CAAChB,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAAvD,QAAA,eACXjE,OAAA,CAAC9B,IAAI;UAAA+F,QAAA,eACHjE,OAAA,CAACf,SAAS;YACRwC,KAAK,EAAC,0BAAM;YACZ2C,KAAK,EAAE4C,QAAS;YAChBS,MAAM,eAAEzH,OAAA,CAACL,gBAAgB;cAAC2H,KAAK,EAAE;gBAAE5E,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNhD,OAAA,CAAChB,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAAvD,QAAA,eACXjE,OAAA,CAAC9B,IAAI;UAAA+F,QAAA,eACHjE,OAAA,CAACf,SAAS;YACRwC,KAAK,EAAC,oBAAK;YACX2C,KAAK,EAAE6C,WAAY;YACnBQ,MAAM,eAAEzH,OAAA,CAACF,yBAAyB;cAACwH,KAAK,EAAE;gBAAE5E,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENhD,OAAA,CAAC9B,IAAI;MAAA+F,QAAA,gBACHjE,OAAA;QAAKmH,SAAS,EAAC,SAAS;QAAAlD,QAAA,eACtBjE,OAAA;UAAKmH,SAAS,EAAC,cAAc;UAAAlD,QAAA,eAC3BjE,OAAA,CAAC5B,MAAM;YACLsD,IAAI,EAAC,SAAS;YACdkB,IAAI,eAAE5C,OAAA,CAACX,YAAY;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB6B,OAAO,EAAES,kBAAmB;YAAArB,QAAA,EAC7B;UAED;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENhD,OAAA,CAAC3B,KAAK;QACJuF,OAAO,EAAEA,OAAQ;QACjB8D,UAAU,EAAEhH,KAAM;QAClBiH,MAAM,EAAC,IAAI;QACX/G,OAAO,EAAEA,OAAQ;QACjBgH,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAnF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPhD,OAAA,CAACxB,KAAK;MACJiD,KAAK,EAAEP,WAAW,GAAG,MAAM,GAAG,MAAO;MACrC+G,IAAI,EAAEnH,YAAa;MACnBoH,IAAI,EAAEhC,aAAc;MACpBiC,QAAQ,EAAEA,CAAA,KAAM;QACdpH,eAAe,CAAC,KAAK,CAAC;QACtBO,IAAI,CAACiE,WAAW,CAAC,CAAC;MACpB,CAAE;MACF6C,cAAc,EAAExH,OAAQ;MACxByH,KAAK,EAAE,GAAI;MAAApE,QAAA,eAEXjE,OAAA,CAACvB,IAAI;QACH6C,IAAI,EAAEA,IAAK;QACXgH,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACb7G,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE,UAAU;UAClBC,QAAQ,EAAE;QACZ,CAAE;QAAAqC,QAAA,gBAEFjE,OAAA,CAACjB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAApD,QAAA,gBACdjE,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAqF,QAAA,eAEhDjE,OAAA,CAACtB,KAAK;gBAACmK,WAAW,EAAC;cAAS;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNhD,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACXjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAqF,QAAA,eAEhDjE,OAAA,CAACrB,MAAM;gBAAAsF,QAAA,gBACLjE,OAAA,CAACK,MAAM;kBAAC+D,KAAK,EAAC,MAAM;kBAAAH,QAAA,EAAC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChChD,OAAA,CAACK,MAAM;kBAAC+D,KAAK,EAAC,SAAS;kBAAAH,QAAA,EAAC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnChD,OAAA,CAACK,MAAM;kBAAC+D,KAAK,EAAC,MAAM;kBAAAH,QAAA,EAAC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhD,OAAA,CAACjB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAApD,QAAA,gBACdjE,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACXjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAqF,QAAA,eAE9CjE,OAAA,CAACrB,MAAM;gBAAAsF,QAAA,gBACLjE,OAAA,CAACK,MAAM;kBAAC+D,KAAK,EAAC,UAAU;kBAAAH,QAAA,EAAC;gBAAG;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrChD,OAAA,CAACK,MAAM;kBAAC+D,KAAK,EAAC,QAAQ;kBAAAH,QAAA,EAAC;gBAAG;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnChD,OAAA,CAACK,MAAM;kBAAC+D,KAAK,EAAC,WAAW;kBAAAH,QAAA,EAAC;gBAAG;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtChD,OAAA,CAACK,MAAM;kBAAC+D,KAAK,EAAC,QAAQ;kBAAAH,QAAA,EAAC;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNhD,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACXjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAC,oBAAK;cACXC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAqF,QAAA,eAE/CjE,OAAA,CAACrB,MAAM;gBAAAsF,QAAA,gBACLjE,OAAA,CAACK,MAAM;kBAAC+D,KAAK,EAAC,MAAM;kBAAAH,QAAA,EAAC;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/BhD,OAAA,CAACK,MAAM;kBAAC+D,KAAK,EAAC,QAAQ;kBAAAH,QAAA,EAAC;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjChD,OAAA,CAACK,MAAM;kBAAC+D,KAAK,EAAC,KAAK;kBAAAH,QAAA,EAAC;gBAAC;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNhD,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACXjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAC,kBAAQ;cAAAzE,QAAA,eAEdjE,OAAA,CAACtB,KAAK;gBAACgD,IAAI,EAAC,QAAQ;gBAACoH,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,GAAI;gBAACF,WAAW,EAAC;cAAO;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhD,OAAA,CAACjB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAApD,QAAA,gBACdjE,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAqF,QAAA,eAEhDjE,OAAA,CAACtB,KAAK;gBAACgD,IAAI,EAAC,QAAQ;gBAACoH,GAAG,EAAE,CAAE;gBAACD,WAAW,EAAC;cAAK;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNhD,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAqF,QAAA,eAEhDjE,OAAA,CAACtB,KAAK;gBAACgD,IAAI,EAAC,QAAQ;gBAACoH,GAAG,EAAE,CAAE;gBAACD,WAAW,EAAC;cAAM;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhD,OAAA,CAACvB,IAAI,CAAC+J,IAAI;UACRC,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEhK,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqF,QAAA,eAEhDjE,OAAA,CAACI,QAAQ;YACP4I,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAgB;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZhD,OAAA,CAACvB,IAAI,CAAC+J,IAAI;UACRC,IAAI,EAAC,SAAS;UACdC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEhK,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAqF,QAAA,eAEhDjE,OAAA,CAACI,QAAQ;YACP4I,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAmC;YAAAhG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZhD,OAAA,CAACjB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAApD,QAAA,gBACdjE,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAC,0BAAM;cACZO,OAAO,EAAC,kDAAU;cAAAhF,QAAA,eAElBjE,OAAA,CAACI,QAAQ;gBACP4I,IAAI,EAAE,CAAE;gBACRH,WAAW,EAAC;cAAsB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNhD,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,UAAU;cACfC,KAAK,EAAC,0BAAM;cACZO,OAAO,EAAC,sCAAQ;cAAAhF,QAAA,eAEhBjE,OAAA,CAACI,QAAQ;gBACP4I,IAAI,EAAE,CAAE;gBACRH,WAAW,EAAC;cAAuB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhD,OAAA,CAACjB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAApD,QAAA,gBACdjE,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,0BAAM;cACZO,OAAO,EAAC,sCAAQ;cAAAhF,QAAA,eAEhBjE,OAAA,CAACI,QAAQ;gBACP4I,IAAI,EAAE,CAAE;gBACRH,WAAW,EAAC;cAAiB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNhD,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAACvB,IAAI,CAAC+J,IAAI;cACRC,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAC,0BAAM;cACZO,OAAO,EAAC,sCAAQ;cAAAhF,QAAA,eAEhBjE,OAAA,CAACI,QAAQ;gBACP4I,IAAI,EAAE,CAAE;gBACRH,WAAW,EAAC;cAAoB;gBAAAhG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRhD,OAAA,CAACxB,KAAK;MACJiD,KAAK,EAAC,0BAAM;MACZwG,IAAI,EAAEjH,kBAAmB;MACzBmH,QAAQ,EAAEA,CAAA,KAAMlH,qBAAqB,CAAC,KAAK,CAAE;MAC7CiI,MAAM,EAAE,cACNlJ,OAAA,CAAC5B,MAAM;QAAayG,OAAO,EAAEA,CAAA,KAAM5D,qBAAqB,CAAC,KAAK,CAAE;QAAAgD,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFqF,KAAK,EAAE,GAAI;MAAApE,QAAA,EAEV7C,WAAW,iBACVpB,OAAA;QAAAiE,QAAA,gBACEjE,OAAA,CAACd,YAAY;UAACiK,QAAQ;UAACC,MAAM,EAAE,CAAE;UAAC9B,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAtD,QAAA,gBAC5DjE,OAAA,CAACd,YAAY,CAACsJ,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAClB,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACtCjE,OAAA,CAAC1B,KAAK;cAAA2F,QAAA,GACHzB,UAAU,CAACpB,WAAW,CAACM,IAAI,CAAC,CAACkB,IAAI,eAClC5C,OAAA,CAACE,IAAI;gBAACgE,MAAM;gBAAAD,QAAA,EAAE7C,WAAW,CAACK;cAAK;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACvChD,OAAA,CAACzB,GAAG;gBAACmE,KAAK,EAAEF,UAAU,CAACpB,WAAW,CAACM,IAAI,CAAC,CAACgB,KAAM;gBAAAuB,QAAA,EAC5CzB,UAAU,CAACpB,WAAW,CAACM,IAAI,CAAC,CAACiB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACNhD,OAAA,CAACzB,GAAG;gBAACmE,KAAK,EAAES,YAAY,CAAC/B,WAAW,CAACO,MAAM,CAAC,CAACe,KAAM;gBAAAuB,QAAA,EAChDd,YAAY,CAAC/B,WAAW,CAACO,MAAM,CAAC,CAACgB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eACNhD,OAAA,CAACzB,GAAG;gBAACmE,KAAK,EAAEc,cAAc,CAACpC,WAAW,CAACQ,QAAQ,CAAC,CAACc,KAAM;gBAAAuB,QAAA,GAAC,0BAClD,EAACT,cAAc,CAACpC,WAAW,CAACQ,QAAQ,CAAC,CAACe,IAAI;cAAA;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eAEpBhD,OAAA,CAACd,YAAY,CAACsJ,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAzE,QAAA,GAAC,QAC7B,EAAC7C,WAAW,CAACgB,YAAY,EAAC,iBAAK,EAAChB,WAAW,CAACiB,UAAU,EAAC,QAC1D;UAAA;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAAC,eACpBhD,OAAA,CAACd,YAAY,CAACsJ,IAAI;YAACE,KAAK,EAAC,cAAI;YAAAzE,QAAA,eAC3BjE,OAAA,CAACb,QAAQ;cACPmF,OAAO,EAAElD,WAAW,CAACS,QAAS;cAC9B0C,IAAI,EAAC,OAAO;cACZ5C,MAAM,EAAEP,WAAW,CAACS,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG;YAAS;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACe,CAAC,eAEpBhD,OAAA,CAACd,YAAY,CAACsJ,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAzE,QAAA,EAAE7C,WAAW,CAACkB;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAC3EhD,OAAA,CAACd,YAAY,CAACsJ,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAAzE,QAAA,EAAE7C,WAAW,CAACmB;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAE3EhD,OAAA,CAACd,YAAY,CAACsJ,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAClB,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACtCjE,OAAA,CAACG,SAAS;cAAA8D,QAAA,EAAE7C,WAAW,CAACU;YAAW;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eAEpBhD,OAAA,CAACd,YAAY,CAACsJ,IAAI;YAACE,KAAK,EAAC,0BAAM;YAAClB,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACtCjE,OAAA,CAACG,SAAS;cAAA8D,QAAA,EAAE7C,WAAW,CAACW;YAAO;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAEfhD,OAAA,CAACjB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAAApD,QAAA,gBACdjE,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAAC9B,IAAI;cAACuD,KAAK,EAAC,0BAAM;cAAC8C,IAAI,EAAC,OAAO;cAAAN,QAAA,EAC5B7C,WAAW,CAACY,UAAU,IAAIZ,WAAW,CAACY,UAAU,CAAC8E,MAAM,GAAG,CAAC,gBAC1D9G,OAAA,CAAC1B,KAAK;gBAAC+K,IAAI;gBAAApF,QAAA,EACR7C,WAAW,CAACY,UAAU,CAACyE,GAAG,CAAC,CAAC6C,SAAS,EAAEC,KAAK,kBAC3CvJ,OAAA,CAACzB,GAAG;kBAAamE,KAAK,EAAC,MAAM;kBAAAuB,QAAA,EAAEqF;gBAAS,GAA9BC,KAAK;kBAAA1G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA+B,CAC/C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,gBAERhD,OAAA,CAACE,IAAI;gBAACwB,IAAI,EAAC,WAAW;gBAAAuC,QAAA,EAAC;cAAM;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACpC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNhD,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAAC9B,IAAI;cAACuD,KAAK,EAAC,0BAAM;cAAC8C,IAAI,EAAC,OAAO;cAAAN,QAAA,EAC5B7C,WAAW,CAACa,QAAQ,IAAIb,WAAW,CAACa,QAAQ,CAAC6E,MAAM,GAAG,CAAC,gBACtD9G,OAAA,CAAC1B,KAAK;gBAAC+K,IAAI;gBAAApF,QAAA,EACR7C,WAAW,CAACa,QAAQ,CAACwE,GAAG,CAAC,CAAC+C,OAAO,EAAED,KAAK,kBACvCvJ,OAAA,CAACzB,GAAG;kBAAamE,KAAK,EAAC,OAAO;kBAAAuB,QAAA,EAAEuF;gBAAO,GAA7BD,KAAK;kBAAA1G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA8B,CAC9C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,gBAERhD,OAAA,CAACE,IAAI;gBAACwB,IAAI,EAAC,WAAW;gBAAAuC,QAAA,EAAC;cAAM;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACpC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENhD,OAAA,CAACjB,GAAG;UAACsI,MAAM,EAAE,EAAG;UAACC,KAAK,EAAE;YAAEmC,SAAS,EAAE;UAAG,CAAE;UAAAxF,QAAA,gBACxCjE,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAAC9B,IAAI;cAACuD,KAAK,EAAC,0BAAM;cAAC8C,IAAI,EAAC,OAAO;cAAAN,QAAA,EAC5B7C,WAAW,CAACc,SAAS,IAAId,WAAW,CAACc,SAAS,CAAC4E,MAAM,GAAG,CAAC,gBACxD9G,OAAA,CAAC1B,KAAK;gBAAC+K,IAAI;gBAAApF,QAAA,EACR7C,WAAW,CAACc,SAAS,CAACuE,GAAG,CAAC,CAACiD,QAAQ,EAAEH,KAAK,kBACzCvJ,OAAA,CAACzB,GAAG;kBAAamE,KAAK,EAAC,QAAQ;kBAAAuB,QAAA,EAAEyF;gBAAQ,GAA/BH,KAAK;kBAAA1G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAgC,CAChD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,gBAERhD,OAAA,CAACE,IAAI;gBAACwB,IAAI,EAAC,WAAW;gBAAAuC,QAAA,EAAC;cAAM;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACpC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNhD,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZjE,OAAA,CAAC9B,IAAI;cAACuD,KAAK,EAAC,0BAAM;cAAC8C,IAAI,EAAC,OAAO;cAAAN,QAAA,EAC5B7C,WAAW,CAACe,MAAM,IAAIf,WAAW,CAACe,MAAM,CAAC2E,MAAM,GAAG,CAAC,gBAClD9G,OAAA,CAAC1B,KAAK;gBAAC+K,IAAI;gBAAApF,QAAA,EACR7C,WAAW,CAACe,MAAM,CAACsE,GAAG,CAAC,CAACkD,KAAK,EAAEJ,KAAK,kBACnCvJ,OAAA,CAACzB,GAAG;kBAAamE,KAAK,EAAC,QAAQ;kBAAAuB,QAAA,EAAE0F;gBAAK,GAA5BJ,KAAK;kBAAA1G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA6B,CAC7C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,gBAERhD,OAAA,CAACE,IAAI;gBAACwB,IAAI,EAAC,WAAW;gBAAAuC,QAAA,EAAC;cAAM;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACpC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNhD,OAAA,CAAC9B,IAAI;UAACuD,KAAK,EAAC,4CAAS;UAAC8C,IAAI,EAAC,OAAO;UAAC+C,KAAK,EAAE;YAAEmC,SAAS,EAAE;UAAG,CAAE;UAAAxF,QAAA,eAC1DjE,OAAA,CAACZ,QAAQ;YAAA6E,QAAA,gBACPjE,OAAA,CAACZ,QAAQ,CAACoJ,IAAI;cAAC9F,KAAK,EAAC,MAAM;cAAAuB,QAAA,gBACzBjE,OAAA,CAACE,IAAI;gBAACgE,MAAM;gBAAAD,QAAA,EAAC;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxBhD,OAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhD,OAAA,CAACE,IAAI;gBAACwB,IAAI,EAAC,WAAW;gBAAAuC,QAAA,EAAE7C,WAAW,CAACkB;cAAS;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,EACf5B,WAAW,CAACO,MAAM,KAAK,QAAQ,iBAC9B3B,OAAA,CAACZ,QAAQ,CAACoJ,IAAI;cAAC9F,KAAK,EAAC,QAAQ;cAAAuB,QAAA,gBAC3BjE,OAAA,CAACE,IAAI;gBAACgE,MAAM;gBAAAD,QAAA,EAAC;cAAK;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACzBhD,OAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhD,OAAA,CAACE,IAAI;gBAACwB,IAAI,EAAC,WAAW;gBAAAuC,QAAA,GAAC,oBAAG,EAAC7C,WAAW,CAACS,QAAQ,EAAC,GAAC;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAChB,EACA5B,WAAW,CAACO,MAAM,KAAK,WAAW,iBACjC3B,OAAA,CAACZ,QAAQ,CAACoJ,IAAI;cAAC9F,KAAK,EAAC,OAAO;cAAAuB,QAAA,gBAC1BjE,OAAA,CAACE,IAAI;gBAACgE,MAAM;gBAAAD,QAAA,EAAC;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxBhD,OAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhD,OAAA,CAACE,IAAI;gBAACwB,IAAI,EAAC,WAAW;gBAAAuC,QAAA,EAAE7C,WAAW,CAACmB;cAAS;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAChB,EACA5B,WAAW,CAACO,MAAM,KAAK,UAAU,iBAChC3B,OAAA,CAACZ,QAAQ,CAACoJ,IAAI;cAAC9F,KAAK,EAAC,MAAM;cAAAuB,QAAA,gBACzBjE,OAAA,CAACE,IAAI;gBAACgE,MAAM;gBAAAD,QAAA,EAAC;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxBhD,OAAA;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNhD,OAAA,CAACE,IAAI;gBAACwB,IAAI,EAAC,WAAW;gBAAAuC,QAAA,EAAC;cAAI;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAChB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACzC,EAAA,CA/sBID,QAAQ;EAAA,QACctC,SAAS,EAOpBS,IAAI,CAAC8C,OAAO;AAAA;AAAAqI,EAAA,GARvBtJ,QAAQ;AAitBd,eAAeA,QAAQ;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}