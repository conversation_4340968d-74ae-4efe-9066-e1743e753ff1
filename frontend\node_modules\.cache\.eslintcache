[{"D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js": "1", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js": "2", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js": "3", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js": "4", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js": "5", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js": "6", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js": "7", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js": "8", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js": "9", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js": "10", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js": "11", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js": "12", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js": "13", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js": "14", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js": "15", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js": "16", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js": "17", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js": "18", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\EquipmentSystems.js": "19", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PetSystems.js": "20", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\RaceDistribution.js": "21", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ResourceDistribution.js": "22", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SecretRealms.js": "23", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\DimensionStructure.js": "24", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SpiritualTreasureSystems.js": "25", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\MapStructure.js": "26", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterDetail.js": "27", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeList.js": "28", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CivilianSystems.js": "29", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\JudicialSystems.js": "30", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProfessionSystems.js": "31", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SettingsManagement.js": "32", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeManagement.js": "33", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ContentManagement.js": "34"}, {"size": 254, "mtime": 1748329246695, "results": "35", "hashOfConfig": "36"}, {"size": 5097, "mtime": 1748510171456, "results": "37", "hashOfConfig": "36"}, {"size": 5421, "mtime": 1748510158840, "results": "38", "hashOfConfig": "36"}, {"size": 9142, "mtime": 1748488507886, "results": "39", "hashOfConfig": "36"}, {"size": 19188, "mtime": 1748508012050, "results": "40", "hashOfConfig": "36"}, {"size": 12225, "mtime": 1748508699122, "results": "41", "hashOfConfig": "36"}, {"size": 8623, "mtime": 1748511734497, "results": "42", "hashOfConfig": "36"}, {"size": 20876, "mtime": 1748508046738, "results": "43", "hashOfConfig": "36"}, {"size": 29827, "mtime": 1748483630000, "results": "44", "hashOfConfig": "36"}, {"size": 24215, "mtime": 1748352877767, "results": "45", "hashOfConfig": "36"}, {"size": 23678, "mtime": 1748508088969, "results": "46", "hashOfConfig": "36"}, {"size": 23306, "mtime": 1748508119220, "results": "47", "hashOfConfig": "36"}, {"size": 44589, "mtime": 1748502631872, "results": "48", "hashOfConfig": "36"}, {"size": 19283, "mtime": 1748352994687, "results": "49", "hashOfConfig": "36"}, {"size": 4503, "mtime": 1748341004213, "results": "50", "hashOfConfig": "36"}, {"size": 26386, "mtime": 1748502592952, "results": "51", "hashOfConfig": "36"}, {"size": 5825, "mtime": 1748502680240, "results": "52", "hashOfConfig": "36"}, {"size": 8260, "mtime": 1748502719538, "results": "53", "hashOfConfig": "36"}, {"size": 17140, "mtime": 1748508543368, "results": "54", "hashOfConfig": "36"}, {"size": 18365, "mtime": 1748508505094, "results": "55", "hashOfConfig": "36"}, {"size": 15353, "mtime": 1748486959232, "results": "56", "hashOfConfig": "36"}, {"size": 15084, "mtime": 1748486881151, "results": "57", "hashOfConfig": "36"}, {"size": 14551, "mtime": 1748508448270, "results": "58", "hashOfConfig": "36"}, {"size": 18070, "mtime": 1748508192048, "results": "59", "hashOfConfig": "36"}, {"size": 21237, "mtime": 1748508478504, "results": "60", "hashOfConfig": "36"}, {"size": 19315, "mtime": 1748508250630, "results": "61", "hashOfConfig": "36"}, {"size": 9772, "mtime": 1748488625998, "results": "62", "hashOfConfig": "36"}, {"size": 32163, "mtime": 1748508148176, "results": "63", "hashOfConfig": "36"}, {"size": 10799, "mtime": 1748496033772, "results": "64", "hashOfConfig": "36"}, {"size": 10233, "mtime": 1748496079485, "results": "65", "hashOfConfig": "36"}, {"size": 9052, "mtime": 1748496118220, "results": "66", "hashOfConfig": "36"}, {"size": 9020, "mtime": 1748510223875, "results": "67", "hashOfConfig": "36"}, {"size": 7672, "mtime": 1748510265477, "results": "68", "hashOfConfig": "36"}, {"size": 4585, "mtime": 1748510196524, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js", ["172", "173", "174", "175"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js", ["176", "177", "178", "179"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js", ["180"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js", ["181"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js", ["182"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js", ["183", "184"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js", ["185"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js", ["186", "187"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js", ["188", "189", "190"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js", ["191", "192"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js", ["193"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js", ["194", "195"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js", ["196"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\EquipmentSystems.js", ["197", "198", "199"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PetSystems.js", ["200", "201"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\RaceDistribution.js", ["202"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ResourceDistribution.js", ["203", "204"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SecretRealms.js", ["205", "206"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\DimensionStructure.js", ["207"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SpiritualTreasureSystems.js", ["208"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\MapStructure.js", ["209", "210"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterDetail.js", ["211"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeList.js", ["212", "213", "214", "215", "216", "217", "218", "219", "220"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CivilianSystems.js", ["221", "222", "223", "224", "225"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\JudicialSystems.js", ["226", "227", "228"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProfessionSystems.js", ["229", "230", "231", "232", "233"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SettingsManagement.js", ["234"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\VolumeManagement.js", ["235", "236", "237"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ContentManagement.js", ["238"], [], {"ruleId": "239", "severity": 1, "message": "240", "line": 1, "column": 27, "nodeType": "241", "messageId": "242", "endLine": 1, "endColumn": 36}, {"ruleId": "239", "severity": 1, "message": "243", "line": 18, "column": 17, "nodeType": "241", "messageId": "242", "endLine": 18, "endColumn": 25}, {"ruleId": "239", "severity": 1, "message": "244", "line": 25, "column": 26, "nodeType": "241", "messageId": "242", "endLine": 25, "endColumn": 43}, {"ruleId": "239", "severity": 1, "message": "245", "line": 55, "column": 28, "nodeType": "241", "messageId": "242", "endLine": 55, "endColumn": 47}, {"ruleId": "239", "severity": 1, "message": "246", "line": 23, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 23, "endColumn": 9}, {"ruleId": "239", "severity": 1, "message": "247", "line": 36, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 36, "endColumn": 17}, {"ruleId": "239", "severity": 1, "message": "248", "line": 39, "column": 22, "nodeType": "241", "messageId": "242", "endLine": 39, "endColumn": 31}, {"ruleId": "249", "severity": 1, "message": "250", "line": 110, "column": 6, "nodeType": "251", "endLine": 110, "endColumn": 8, "suggestions": "252"}, {"ruleId": "249", "severity": 1, "message": "253", "line": 124, "column": 6, "nodeType": "251", "endLine": 124, "endColumn": 8, "suggestions": "254"}, {"ruleId": "249", "severity": 1, "message": "255", "line": 32, "column": 9, "nodeType": "256", "endLine": 49, "endColumn": 4}, {"ruleId": "249", "severity": 1, "message": "257", "line": 115, "column": 6, "nodeType": "251", "endLine": 115, "endColumn": 8, "suggestions": "258"}, {"ruleId": "239", "severity": 1, "message": "259", "line": 19, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 19, "endColumn": 15}, {"ruleId": "249", "severity": 1, "message": "260", "line": 116, "column": 6, "nodeType": "251", "endLine": 116, "endColumn": 8, "suggestions": "261"}, {"ruleId": "249", "severity": 1, "message": "262", "line": 131, "column": 6, "nodeType": "251", "endLine": 131, "endColumn": 8, "suggestions": "263"}, {"ruleId": "249", "severity": 1, "message": "264", "line": 112, "column": 6, "nodeType": "251", "endLine": 112, "endColumn": 8, "suggestions": "265"}, {"ruleId": "239", "severity": 1, "message": "266", "line": 356, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 356, "endColumn": 23}, {"ruleId": "239", "severity": 1, "message": "267", "line": 24, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 24, "endColumn": 10}, {"ruleId": "239", "severity": 1, "message": "268", "line": 34, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 34, "endColumn": 18}, {"ruleId": "249", "severity": 1, "message": "269", "line": 151, "column": 6, "nodeType": "251", "endLine": 151, "endColumn": 8, "suggestions": "270"}, {"ruleId": "239", "severity": 1, "message": "267", "line": 20, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 20, "endColumn": 10}, {"ruleId": "249", "severity": 1, "message": "271", "line": 115, "column": 6, "nodeType": "251", "endLine": 115, "endColumn": 8, "suggestions": "272"}, {"ruleId": "239", "severity": 1, "message": "273", "line": 23, "column": 16, "nodeType": "241", "messageId": "242", "endLine": 23, "endColumn": 20}, {"ruleId": "239", "severity": 1, "message": "274", "line": 108, "column": 10, "nodeType": "241", "messageId": "242", "endLine": 108, "endColumn": 19}, {"ruleId": "249", "severity": 1, "message": "275", "line": 371, "column": 6, "nodeType": "251", "endLine": 371, "endColumn": 8, "suggestions": "276"}, {"ruleId": "249", "severity": 1, "message": "277", "line": 133, "column": 6, "nodeType": "251", "endLine": 133, "endColumn": 8, "suggestions": "278"}, {"ruleId": "239", "severity": 1, "message": "279", "line": 23, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 23, "endColumn": 11}, {"ruleId": "239", "severity": 1, "message": "280", "line": 34, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 34, "endColumn": 15}, {"ruleId": "249", "severity": 1, "message": "281", "line": 103, "column": 6, "nodeType": "251", "endLine": 103, "endColumn": 17, "suggestions": "282"}, {"ruleId": "239", "severity": 1, "message": "283", "line": 32, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 32, "endColumn": 17}, {"ruleId": "249", "severity": 1, "message": "284", "line": 113, "column": 6, "nodeType": "251", "endLine": 113, "endColumn": 17, "suggestions": "285"}, {"ruleId": "249", "severity": 1, "message": "286", "line": 87, "column": 6, "nodeType": "251", "endLine": 87, "endColumn": 17, "suggestions": "287"}, {"ruleId": "239", "severity": 1, "message": "279", "line": 21, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 21, "endColumn": 11}, {"ruleId": "249", "severity": 1, "message": "288", "line": 81, "column": 6, "nodeType": "251", "endLine": 81, "endColumn": 17, "suggestions": "289"}, {"ruleId": "239", "severity": 1, "message": "279", "line": 23, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 23, "endColumn": 11}, {"ruleId": "249", "severity": 1, "message": "290", "line": 88, "column": 6, "nodeType": "251", "endLine": 88, "endColumn": 17, "suggestions": "291"}, {"ruleId": "249", "severity": 1, "message": "292", "line": 113, "column": 6, "nodeType": "251", "endLine": 113, "endColumn": 17, "suggestions": "293"}, {"ruleId": "249", "severity": 1, "message": "294", "line": 141, "column": 6, "nodeType": "251", "endLine": 141, "endColumn": 17, "suggestions": "295"}, {"ruleId": "239", "severity": 1, "message": "296", "line": 32, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 32, "endColumn": 15}, {"ruleId": "249", "severity": 1, "message": "297", "line": 107, "column": 6, "nodeType": "251", "endLine": 107, "endColumn": 17, "suggestions": "298"}, {"ruleId": "239", "severity": 1, "message": "267", "line": 16, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 16, "endColumn": 10}, {"ruleId": "239", "severity": 1, "message": "299", "line": 26, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 26, "endColumn": 8}, {"ruleId": "239", "severity": 1, "message": "300", "line": 36, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 36, "endColumn": 22}, {"ruleId": "239", "severity": 1, "message": "301", "line": 39, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 39, "endColumn": 22}, {"ruleId": "239", "severity": 1, "message": "302", "line": 40, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 40, "endColumn": 19}, {"ruleId": "239", "severity": 1, "message": "303", "line": 46, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 46, "endColumn": 14}, {"ruleId": "239", "severity": 1, "message": "304", "line": 47, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 47, "endColumn": 16}, {"ruleId": "239", "severity": 1, "message": "305", "line": 61, "column": 10, "nodeType": "241", "messageId": "242", "endLine": 61, "endColumn": 26}, {"ruleId": "249", "severity": 1, "message": "306", "line": 184, "column": 6, "nodeType": "251", "endLine": 184, "endColumn": 8, "suggestions": "307"}, {"ruleId": "239", "severity": 1, "message": "308", "line": 189, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 189, "endColumn": 26}, {"ruleId": "239", "severity": 1, "message": "302", "line": 29, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 29, "endColumn": 19}, {"ruleId": "239", "severity": 1, "message": "309", "line": 35, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 35, "endColumn": 15}, {"ruleId": "239", "severity": 1, "message": "310", "line": 47, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 47, "endColumn": 27}, {"ruleId": "239", "severity": 1, "message": "311", "line": 61, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 61, "endColumn": 25}, {"ruleId": "249", "severity": 1, "message": "312", "line": 74, "column": 6, "nodeType": "251", "endLine": 74, "endColumn": 17, "suggestions": "313"}, {"ruleId": "239", "severity": 1, "message": "314", "line": 19, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 19, "endColumn": 12}, {"ruleId": "239", "severity": 1, "message": "315", "line": 29, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 29, "endColumn": 17}, {"ruleId": "249", "severity": 1, "message": "312", "line": 64, "column": 6, "nodeType": "251", "endLine": 64, "endColumn": 17, "suggestions": "316"}, {"ruleId": "239", "severity": 1, "message": "314", "line": 19, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 19, "endColumn": 12}, {"ruleId": "239", "severity": 1, "message": "317", "line": 29, "column": 3, "nodeType": "241", "messageId": "242", "endLine": 29, "endColumn": 15}, {"ruleId": "239", "severity": 1, "message": "309", "line": 35, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 35, "endColumn": 15}, {"ruleId": "239", "severity": 1, "message": "318", "line": 47, "column": 9, "nodeType": "241", "messageId": "242", "endLine": 47, "endColumn": 34}, {"ruleId": "249", "severity": 1, "message": "312", "line": 64, "column": 6, "nodeType": "251", "endLine": 64, "endColumn": 17, "suggestions": "319"}, {"ruleId": "239", "severity": 1, "message": "320", "line": 1, "column": 17, "nodeType": "241", "messageId": "242", "endLine": 1, "endColumn": 25}, {"ruleId": "239", "severity": 1, "message": "321", "line": 30, "column": 10, "nodeType": "241", "messageId": "242", "endLine": 30, "endColumn": 17}, {"ruleId": "239", "severity": 1, "message": "322", "line": 31, "column": 10, "nodeType": "241", "messageId": "242", "endLine": 31, "endColumn": 17}, {"ruleId": "249", "severity": 1, "message": "323", "line": 71, "column": 6, "nodeType": "251", "endLine": 71, "endColumn": 10, "suggestions": "324"}, {"ruleId": "239", "severity": 1, "message": "320", "line": 1, "column": 17, "nodeType": "241", "messageId": "242", "endLine": 1, "endColumn": 25}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'setStats' is assigned a value but never used.", "'setRecentProjects' is assigned a value but never used.", "'setRecentActivities' is assigned a value but never used.", "'Upload' is defined but never used.", "'UploadOutlined' is defined but never used.", "'Paragraph' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mockCharacters'. Either include it or remove the dependency array.", "ArrayExpression", ["325"], "React Hook useEffect has a missing dependency: 'loadProjects'. Either include it or remove the dependency array.", ["326"], "The 'mockProject' object makes the dependencies of useCallback Hook (at line 63) change on every render. Move it inside the useCallback callback. Alternatively, wrap the initialization of 'mockProject' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useEffect has a missing dependency: 'mockFactions'. Either include it or remove the dependency array.", ["327"], "'Descriptions' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockSettings'. Either include it or remove the dependency array.", ["328"], "React Hook useEffect has a missing dependency: 'mockSystems'. Either include it or remove the dependency array.", ["329"], "React Hook useEffect has a missing dependency: 'mockPlots'. Either include it or remove the dependency array.", ["330"], "'completedPlots' is assigned a value but never used.", "'Divider' is defined but never used.", "'HistoryOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockEvents'. Either include it or remove the dependency array.", ["331"], "React Hook useEffect has a missing dependency: 'mockRelations'. Either include it or remove the dependency array.", ["332"], "'Text' is assigned a value but never used.", "'providers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAIInfo'. Either include it or remove the dependency array.", ["333"], "React Hook useEffect has a missing dependency: 'testAllAPIs'. Either include it or remove the dependency array.", ["334"], "'Progress' is defined but never used.", "'StarOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEquipment'. Either include it or remove the dependency array.", ["335"], "'ShieldOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadPets'. Either include it or remove the dependency array.", ["336"], "React Hook useEffect has a missing dependency: 'loadRaces'. Either include it or remove the dependency array.", ["337"], "React Hook useEffect has a missing dependency: 'loadResources'. Either include it or remove the dependency array.", ["338"], "React Hook useEffect has a missing dependency: 'loadRealms'. Either include it or remove the dependency array.", ["339"], "React Hook useEffect has a missing dependency: 'loadDimensions'. Either include it or remove the dependency array.", ["340"], "React Hook useEffect has a missing dependency: 'loadTreasures'. Either include it or remove the dependency array.", ["341"], "'ShopOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadLocations'. Either include it or remove the dependency array.", ["342"], "'Empty' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'OrderedListOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", "'Panel' is assigned a value but never used.", "'TabPane' is assigned a value but never used.", "'selectedVolumeId' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'mockChapters' and 'mockVolumes'. Either include them or remove the dependency array.", ["343"], "'completedChapters' is assigned a value but never used.", "'Option' is assigned a value but never used.", "'socialClassOptions' is assigned a value but never used.", "'lifestyleOptions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSystems'. Either include it or remove the dependency array.", ["344"], "'Statistic' is defined but never used.", "'SafetyOutlined' is defined but never used.", ["345"], "'ToolOutlined' is defined but never used.", "'professionCategoryOptions' is assigned a value but never used.", ["346"], "'useState' is defined but never used.", "'loading' is assigned a value but never used.", "'volumes' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadVolumes'. Either include it or remove the dependency array.", ["347"], {"desc": "348", "fix": "349"}, {"desc": "350", "fix": "351"}, {"desc": "352", "fix": "353"}, {"desc": "354", "fix": "355"}, {"desc": "356", "fix": "357"}, {"desc": "358", "fix": "359"}, {"desc": "360", "fix": "361"}, {"desc": "362", "fix": "363"}, {"desc": "364", "fix": "365"}, {"desc": "366", "fix": "367"}, {"desc": "368", "fix": "369"}, {"desc": "370", "fix": "371"}, {"desc": "372", "fix": "373"}, {"desc": "374", "fix": "375"}, {"desc": "376", "fix": "377"}, {"desc": "378", "fix": "379"}, {"desc": "380", "fix": "381"}, {"desc": "382", "fix": "383"}, {"desc": "384", "fix": "385"}, {"desc": "386", "fix": "387"}, {"desc": "386", "fix": "388"}, {"desc": "386", "fix": "389"}, {"desc": "390", "fix": "391"}, "Update the dependencies array to be: [mockCharacters]", {"range": "392", "text": "393"}, "Update the dependencies array to be: [loadProjects]", {"range": "394", "text": "395"}, "Update the dependencies array to be: [mockFactions]", {"range": "396", "text": "397"}, "Update the dependencies array to be: [mockSettings]", {"range": "398", "text": "399"}, "Update the dependencies array to be: [mockSystems]", {"range": "400", "text": "401"}, "Update the dependencies array to be: [mockPlots]", {"range": "402", "text": "403"}, "Update the dependencies array to be: [mockEvents]", {"range": "404", "text": "405"}, "Update the dependencies array to be: [mockRelations]", {"range": "406", "text": "407"}, "Update the dependencies array to be: [fetchAIInfo]", {"range": "408", "text": "409"}, "Update the dependencies array to be: [testAllAPIs]", {"range": "410", "text": "411"}, "Update the dependencies array to be: [loadEquipment, projectId]", {"range": "412", "text": "413"}, "Update the dependencies array to be: [loadPets, projectId]", {"range": "414", "text": "415"}, "Update the dependencies array to be: [loadRaces, projectId]", {"range": "416", "text": "417"}, "Update the dependencies array to be: [loadResources, projectId]", {"range": "418", "text": "419"}, "Update the dependencies array to be: [loadRealms, projectId]", {"range": "420", "text": "421"}, "Update the dependencies array to be: [loadDimensions, projectId]", {"range": "422", "text": "423"}, "Update the dependencies array to be: [loadTreasures, projectId]", {"range": "424", "text": "425"}, "Update the dependencies array to be: [loadLocations, projectId]", {"range": "426", "text": "427"}, "Update the dependencies array to be: [mockChapters, mockVolumes]", {"range": "428", "text": "429"}, "Update the dependencies array to be: [fetchSystems, projectId]", {"range": "430", "text": "431"}, {"range": "432", "text": "431"}, {"range": "433", "text": "431"}, "Update the dependencies array to be: [id, loadVolumes]", {"range": "434", "text": "435"}, [2480, 2482], "[mockCharacters]", [2528, 2530], "[loadProjects]", [2547, 2549], "[mockFactions]", [2658, 2660], "[mockSettings]", [4240, 4242], "[mockSystems]", [2622, 2624], "[mockPlots]", [3567, 3569], "[mockEvents]", [2672, 2674], "[mockRelations]", [9568, 9570], "[fetchAIInfo]", [3445, 3447], "[testAllAPIs]", [2125, 2136], "[loadEquipment, projectId]", [2294, 2305], "[loadPets, projectId]", [1775, 1786], "[loadRaces, projectId]", [1716, 1727], "[loadResources, projectId]", [1877, 1888], "[loadRealms, projectId]", [2536, 2547], "[loadDimensions, projectId]", [3173, 3184], "[loadTreasures, projectId]", [2390, 2401], "[loadLocations, projectId]", [4452, 4454], "[mockChapters, mockVolumes]", [1755, 1766], "[fetchSystems, projectId]", [1396, 1407], [1500, 1511], [1306, 1310], "[id, loadVolumes]"]