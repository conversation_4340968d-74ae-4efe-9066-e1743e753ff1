{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\Timeline.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport { Card, Typography, Button, Timeline as AntTimeline, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Tooltip, Row, Col, Statistic, Descriptions, DatePicker, InputNumber, Divider } from 'antd';\nimport { PlusOutlined, ClockCircleOutlined, EditOutlined, DeleteOutlined, EyeOutlined, RobotOutlined, CalendarOutlined, HistoryOutlined, StarOutlined, ExclamationCircleOutlined, CheckCircleOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst Timeline = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const [events, setEvents] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingEvent, setEditingEvent] = useState(null);\n  const [viewingEvent, setViewingEvent] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟时间线事件数据\n  const mockEvents = [{\n    id: 1,\n    title: '林天觉醒修仙天赋',\n    type: 'major',\n    category: 'character',\n    timeType: 'story',\n    storyTime: '第1年春',\n    realTime: '2024-01-15',\n    chapter: 1,\n    description: '主角林天在一次意外中觉醒了修仙天赋，从此踏上修仙之路',\n    participants: ['林天'],\n    location: '青云村',\n    consequences: '开启修仙之路，改变人生轨迹',\n    relatedPlots: ['主线：修仙之路'],\n    importance: 5,\n    status: 'completed',\n    createdAt: '2024-01-15'\n  }, {\n    id: 2,\n    title: '拜入青云宗',\n    type: 'major',\n    category: 'plot',\n    timeType: 'story',\n    storyTime: '第1年夏',\n    realTime: '2024-01-16',\n    chapter: 3,\n    description: '林天通过考核，正式拜入青云宗，成为云长老的弟子',\n    participants: ['林天', '云长老'],\n    location: '青云宗',\n    consequences: '获得正式修炼资格，开始系统学习',\n    relatedPlots: ['主线：修仙之路'],\n    importance: 4,\n    status: 'completed',\n    createdAt: '2024-01-16'\n  }, {\n    id: 3,\n    title: '初遇苏雪儿',\n    type: 'minor',\n    category: 'relationship',\n    timeType: 'story',\n    storyTime: '第1年秋',\n    realTime: '2024-01-17',\n    chapter: 5,\n    description: '林天在宗门内初次遇见苏雪儿，两人产生好感',\n    participants: ['林天', '苏雪儿'],\n    location: '青云宗藏书阁',\n    consequences: '开启感情线，为后续发展埋下伏笔',\n    relatedPlots: ['支线：情感线索'],\n    importance: 3,\n    status: 'completed',\n    createdAt: '2024-01-17'\n  }, {\n    id: 4,\n    title: '首次与血煞魔君交锋',\n    type: 'major',\n    category: 'conflict',\n    timeType: 'story',\n    storyTime: '第2年春',\n    realTime: '2024-01-18',\n    chapter: 8,\n    description: '林天在历练中遭遇血煞魔君，双方首次交手',\n    participants: ['林天', '血煞魔君'],\n    location: '血煞谷',\n    consequences: '确立宿敌关系，推动主线剧情发展',\n    relatedPlots: ['主线：修仙之路'],\n    importance: 5,\n    status: 'completed',\n    createdAt: '2024-01-18'\n  }, {\n    id: 5,\n    title: '突破筑基期',\n    type: 'minor',\n    category: 'cultivation',\n    timeType: 'story',\n    storyTime: '第2年夏',\n    realTime: '2024-01-19',\n    chapter: 10,\n    description: '林天经过刻苦修炼，成功突破到筑基期',\n    participants: ['林天'],\n    location: '青云宗修炼室',\n    consequences: '实力大幅提升，为后续挑战做准备',\n    relatedPlots: ['主线：修仙之路'],\n    importance: 3,\n    status: 'completed',\n    createdAt: '2024-01-19'\n  }];\n  useEffect(() => {\n    setEvents(mockEvents.sort((a, b) => a.chapter - b.chapter));\n  }, []);\n\n  // 事件类型配置\n  const typeConfig = {\n    major: {\n      color: 'red',\n      text: '重大事件',\n      icon: /*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 48\n      }, this)\n    },\n    minor: {\n      color: 'blue',\n      text: '一般事件',\n      icon: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 49\n      }, this)\n    },\n    milestone: {\n      color: 'gold',\n      text: '里程碑',\n      icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 52\n      }, this)\n    }\n  };\n\n  // 事件分类配置\n  const categoryConfig = {\n    character: {\n      color: 'blue',\n      text: '人物'\n    },\n    plot: {\n      color: 'green',\n      text: '剧情'\n    },\n    relationship: {\n      color: 'pink',\n      text: '关系'\n    },\n    conflict: {\n      color: 'red',\n      text: '冲突'\n    },\n    cultivation: {\n      color: 'orange',\n      text: '修炼'\n    },\n    world: {\n      color: 'purple',\n      text: '世界'\n    }\n  };\n\n  // 状态配置\n  const statusConfig = {\n    planned: {\n      color: 'blue',\n      text: '计划中'\n    },\n    ongoing: {\n      color: 'orange',\n      text: '进行中'\n    },\n    completed: {\n      color: 'green',\n      text: '已完成'\n    },\n    cancelled: {\n      color: 'default',\n      text: '已取消'\n    }\n  };\n\n  // 处理新建/编辑事件\n  const handleCreateOrEdit = () => {\n    setEditingEvent(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = event => {\n    setEditingEvent(event);\n    form.setFieldsValue({\n      ...event,\n      participants: event.participants.join('\\n'),\n      relatedPlots: event.relatedPlots.join('\\n'),\n      realTime: event.realTime ? dayjs(event.realTime) : null\n    });\n    setModalVisible(true);\n  };\n  const handleView = event => {\n    setViewingEvent(event);\n    setDetailModalVisible(true);\n  };\n  const handleAIGenerate = event => {\n    message.info(`AI分析事件：${event.title}`);\n  };\n  const handleDelete = async id => {\n    try {\n      // 调用后端API删除数据\n      await axios.delete(`/api/project-data/projects/${projectId}/data/timeline/${id}`);\n\n      // 删除成功后从列表中移除\n      setEvents(events.filter(e => e.id !== id));\n      message.success('事件删除成功');\n    } catch (error) {\n      console.error('删除事件失败:', error);\n      message.error('删除事件失败');\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 处理数组字段和日期\n      const processedValues = {\n        ...values,\n        participants: values.participants ? values.participants.split('\\n').filter(item => item.trim()) : [],\n        relatedPlots: values.relatedPlots ? values.relatedPlots.split('\\n').filter(item => item.trim()) : [],\n        realTime: values.realTime ? values.realTime.format('YYYY-MM-DD') : null\n      };\n      if (editingEvent) {\n        // 编辑事件\n        setEvents(events.map(e => e.id === editingEvent.id ? {\n          ...e,\n          ...processedValues\n        } : e).sort((a, b) => a.chapter - b.chapter));\n        message.success('事件更新成功');\n      } else {\n        // 新建事件\n        const newEvent = {\n          id: Date.now(),\n          ...processedValues,\n          createdAt: new Date().toISOString().split('T')[0]\n        };\n        setEvents([...events, newEvent].sort((a, b) => a.chapter - b.chapter));\n        message.success('事件创建成功');\n      }\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalEvents = events.length;\n  const majorEvents = events.filter(e => e.type === 'major').length;\n  const completedEvents = events.filter(e => e.status === 'completed').length;\n  const plannedEvents = events.filter(e => e.status === 'planned').length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u65F6\\u95F4\\u7EBF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4E8B\\u4EF6\\u6570\",\n            value: totalEvents,\n            prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u91CD\\u5927\\u4E8B\\u4EF6\",\n            value: majorEvents,\n            prefix: /*#__PURE__*/_jsxDEV(StarOutlined, {\n              style: {\n                color: '#f5222d'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u5B8C\\u6210\",\n            value: completedEvents,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BA1\\u5212\\u4E2D\",\n            value: plannedEvents,\n            prefix: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 273,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-left\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 21\n            }, this),\n            onClick: handleCreateOrEdit,\n            children: \"\\u6DFB\\u52A0\\u4E8B\\u4EF6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AntTimeline, {\n        mode: \"left\",\n        style: {\n          marginTop: 24\n        },\n        children: events.map(event => /*#__PURE__*/_jsxDEV(AntTimeline.Item, {\n          color: typeConfig[event.type].color,\n          dot: typeConfig[event.type].icon,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            size: \"small\",\n            style: {\n              marginBottom: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'flex-start'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  flex: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  direction: \"vertical\",\n                  size: \"small\",\n                  style: {\n                    width: '100%'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      strong: true,\n                      style: {\n                        fontSize: '16px'\n                      },\n                      children: event.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 338,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Space, {\n                      style: {\n                        marginLeft: 16\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Tag, {\n                        color: typeConfig[event.type].color,\n                        children: typeConfig[event.type].text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 340,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                        color: categoryConfig[event.category].color,\n                        children: categoryConfig[event.category].text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 343,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                        color: statusConfig[event.status].color,\n                        children: statusConfig[event.status].text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 346,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 337,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Space, {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [/*#__PURE__*/_jsxDEV(CalendarOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 354,\n                        columnNumber: 27\n                      }, this), \" \", event.storyTime]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\u7B2C\", event.chapter, \"\\u7AE0\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 356,\n                      columnNumber: 25\n                    }, this), event.location && /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: [\"\\uD83D\\uDCCD \", event.location]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 360,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 352,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                    style: {\n                      margin: 0,\n                      color: '#666'\n                    },\n                    children: event.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 366,\n                    columnNumber: 23\n                  }, this), event.participants && event.participants.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u53C2\\u4E0E\\u4EBA\\u7269\\uFF1A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 372,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(Space, {\n                      wrap: true,\n                      children: event.participants.map((participant, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                        size: \"small\",\n                        children: participant\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 375,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 373,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 371,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"text\",\n                    icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 31\n                    }, this),\n                    onClick: () => handleView(event)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 385,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 384,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"\\u7F16\\u8F91\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"text\",\n                    icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 394,\n                      columnNumber: 31\n                    }, this),\n                    onClick: () => handleEdit(event)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 392,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"AI\\u5206\\u6790\",\n                  children: /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"text\",\n                    icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 31\n                    }, this),\n                    onClick: () => handleAIGenerate(event)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 399,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n                  title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u4E8B\\u4EF6\\u5417\\uFF1F\",\n                  onConfirm: () => handleDelete(event.id),\n                  okText: \"\\u786E\\u5B9A\",\n                  cancelText: \"\\u53D6\\u6D88\",\n                  children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"\\u5220\\u9664\",\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"text\",\n                      danger: true,\n                      icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 415,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 411,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 15\n          }, this)\n        }, event.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingEvent ? '编辑事件' : '新建事件',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => {\n        setModalVisible(false);\n        form.resetFields();\n      },\n      confirmLoading: loading,\n      width: 900,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          type: 'minor',\n          category: 'plot',\n          timeType: 'story',\n          status: 'planned',\n          importance: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 16,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u4E8B\\u4EF6\\u6807\\u9898\",\n              rules: [{\n                required: true,\n                message: '请输入事件标题'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4E8B\\u4EF6\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u4E8B\\u4EF6\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择事件类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"major\",\n                  children: \"\\u91CD\\u5927\\u4E8B\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"minor\",\n                  children: \"\\u4E00\\u822C\\u4E8B\\u4EF6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"milestone\",\n                  children: \"\\u91CC\\u7A0B\\u7891\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 466,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"category\",\n              label: \"\\u4E8B\\u4EF6\\u5206\\u7C7B\",\n              rules: [{\n                required: true,\n                message: '请选择事件分类'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"character\",\n                  children: \"\\u4EBA\\u7269\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 483,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"plot\",\n                  children: \"\\u5267\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"relationship\",\n                  children: \"\\u5173\\u7CFB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"conflict\",\n                  children: \"\\u51B2\\u7A81\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"cultivation\",\n                  children: \"\\u4FEE\\u70BC\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"world\",\n                  children: \"\\u4E16\\u754C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u4E8B\\u4EF6\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择事件状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"planned\",\n                  children: \"\\u8BA1\\u5212\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"ongoing\",\n                  children: \"\\u8FDB\\u884C\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"completed\",\n                  children: \"\\u5DF2\\u5B8C\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"cancelled\",\n                  children: \"\\u5DF2\\u53D6\\u6D88\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 492,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"importance\",\n              label: \"\\u91CD\\u8981\\u7A0B\\u5EA6\",\n              rules: [{\n                required: true,\n                message: '请选择重要程度'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: 1,\n                  children: \"1 - \\u5F88\\u4F4E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 513,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 2,\n                  children: \"2 - \\u4F4E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 514,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 3,\n                  children: \"3 - \\u4E2D\\u7B49\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 515,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 4,\n                  children: \"4 - \\u9AD8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 516,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 5,\n                  children: \"5 - \\u5F88\\u9AD8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"storyTime\",\n              label: \"\\u6545\\u4E8B\\u65F6\\u95F4\",\n              rules: [{\n                required: true,\n                message: '请输入故事时间'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u5982\\uFF1A\\u7B2C1\\u5E74\\u6625\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 530,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 525,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 524,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"chapter\",\n              label: \"\\u6240\\u5728\\u7AE0\\u8282\",\n              rules: [{\n                required: true,\n                message: '请输入章节'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u7AE0\\u8282\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 539,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"realTime\",\n              label: \"\\u73B0\\u5B9E\\u65F6\\u95F4\",\n              children: /*#__PURE__*/_jsxDEV(DatePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 523,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"location\",\n          label: \"\\u53D1\\u751F\\u5730\\u70B9\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u4E8B\\u4EF6\\u53D1\\u751F\\u7684\\u5730\\u70B9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u4E8B\\u4EF6\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入事件描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u8FD9\\u4E2A\\u4E8B\\u4EF6\\u7684\\u5185\\u5BB9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 559,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"participants\",\n              label: \"\\u53C2\\u4E0E\\u4EBA\\u7269\",\n              tooltip: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u4EBA\\u7269\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u5982\\uFF1A\\u6797\\u5929\\n\\u82CF\\u96EA\\u513F\\n\\u4E91\\u957F\\u8001\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"relatedPlots\",\n              label: \"\\u76F8\\u5173\\u5267\\u60C5\",\n              tooltip: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u5267\\u60C5\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u5982\\uFF1A\\u4E3B\\u7EBF\\uFF1A\\u4FEE\\u4ED9\\u4E4B\\u8DEF\\n\\u652F\\u7EBF\\uFF1A\\u60C5\\u611F\\u7EBF\\u7D22\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 589,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"consequences\",\n          label: \"\\u4E8B\\u4EF6\\u540E\\u679C\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 2,\n            placeholder: \"\\u8FD9\\u4E2A\\u4E8B\\u4EF6\\u4EA7\\u751F\\u7684\\u5F71\\u54CD\\u548C\\u540E\\u679C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4E8B\\u4EF6\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 615,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: viewingEvent && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n          bordered: true,\n          column: 2,\n          style: {\n            marginBottom: 24\n          },\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u4E8B\\u4EF6\\u6807\\u9898\",\n            span: 2,\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              children: [typeConfig[viewingEvent.type].icon, /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: viewingEvent.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 627,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: typeConfig[viewingEvent.type].color,\n                children: typeConfig[viewingEvent.type].text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 628,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: categoryConfig[viewingEvent.category].color,\n                children: categoryConfig[viewingEvent.category].text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: statusConfig[viewingEvent.status].color,\n                children: statusConfig[viewingEvent.status].text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 634,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 624,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u6545\\u4E8B\\u65F6\\u95F4\",\n            children: viewingEvent.storyTime\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 640,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u6240\\u5728\\u7AE0\\u8282\",\n            children: [\"\\u7B2C\", viewingEvent.chapter, \"\\u7AE0\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 641,\n            columnNumber: 15\n          }, this), viewingEvent.realTime && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u73B0\\u5B9E\\u65F6\\u95F4\",\n            children: viewingEvent.realTime\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 17\n          }, this), viewingEvent.location && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u53D1\\u751F\\u5730\\u70B9\",\n            children: viewingEvent.location\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u91CD\\u8981\\u7A0B\\u5EA6\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              children: [Array.from({\n                length: viewingEvent.importance\n              }, (_, i) => /*#__PURE__*/_jsxDEV(StarOutlined, {\n                style: {\n                  color: '#faad14'\n                }\n              }, i, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 21\n              }, this)), /*#__PURE__*/_jsxDEV(Text, {\n                children: [\"(\", viewingEvent.importance, \"/5)\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 651,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n            children: viewingEvent.createdAt\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u4E8B\\u4EF6\\u63CF\\u8FF0\",\n            span: 2,\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingEvent.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 15\n          }, this), viewingEvent.consequences && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u4E8B\\u4EF6\\u540E\\u679C\",\n            span: 2,\n            children: /*#__PURE__*/_jsxDEV(Paragraph, {\n              children: viewingEvent.consequences\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          style: {\n            marginTop: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u53C2\\u4E0E\\u4EBA\\u7269\",\n              size: \"small\",\n              children: viewingEvent.participants && viewingEvent.participants.length > 0 ? /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: viewingEvent.participants.map((participant, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"blue\",\n                  children: participant\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 677,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 675,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u6682\\u65E0\\u53C2\\u4E0E\\u4EBA\\u7269\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 681,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 672,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u76F8\\u5173\\u5267\\u60C5\",\n              size: \"small\",\n              children: viewingEvent.relatedPlots && viewingEvent.relatedPlots.length > 0 ? /*#__PURE__*/_jsxDEV(Space, {\n                wrap: true,\n                children: viewingEvent.relatedPlots.map((plot, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                  color: \"green\",\n                  children: plot\n                }, index, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 690,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 688,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u6682\\u65E0\\u76F8\\u5173\\u5267\\u60C5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 694,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 671,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 622,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 610,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 267,\n    columnNumber: 5\n  }, this);\n};\n_s(Timeline, \"edbctAs7eHsVmTUv8AXDzzUPfA4=\", false, function () {\n  return [useParams, Form.useForm];\n});\n_c = Timeline;\nexport default Timeline;\nvar _c;\n$RefreshReg$(_c, \"Timeline\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "axios", "Card", "Typography", "<PERSON><PERSON>", "Timeline", "AntTimeline", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "<PERSON><PERSON><PERSON>", "Row", "Col", "Statistic", "Descriptions", "DatePicker", "InputNumber", "Divider", "PlusOutlined", "ClockCircleOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "RobotOutlined", "CalendarOutlined", "HistoryOutlined", "StarOutlined", "ExclamationCircleOutlined", "CheckCircleOutlined", "dayjs", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "TextArea", "Option", "_s", "id", "projectId", "events", "setEvents", "loading", "setLoading", "modalVisible", "setModalVisible", "detailModalVisible", "setDetailModalVisible", "editingEvent", "setEditingEvent", "viewingEvent", "setViewingEvent", "form", "useForm", "mockEvents", "title", "type", "category", "timeType", "storyTime", "realTime", "chapter", "description", "participants", "location", "consequences", "relatedPlots", "importance", "status", "createdAt", "sort", "a", "b", "typeConfig", "major", "color", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "minor", "milestone", "categoryConfig", "character", "plot", "relationship", "conflict", "cultivation", "world", "statusConfig", "planned", "ongoing", "completed", "cancelled", "handleCreateOrEdit", "resetFields", "handleEdit", "event", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "handleView", "handleAIGenerate", "info", "handleDelete", "delete", "filter", "e", "success", "error", "console", "handleModalOk", "values", "validateFields", "processedValues", "split", "item", "trim", "format", "map", "newEvent", "Date", "now", "toISOString", "totalEvents", "length", "majorEvents", "completedEvents", "plannedEvents", "className", "children", "level", "gutter", "style", "marginBottom", "span", "value", "prefix", "onClick", "mode", "marginTop", "<PERSON><PERSON>", "dot", "size", "display", "justifyContent", "alignItems", "flex", "direction", "width", "strong", "fontSize", "marginLeft", "margin", "wrap", "participant", "index", "onConfirm", "okText", "cancelText", "danger", "open", "onOk", "onCancel", "confirmLoading", "layout", "initialValues", "name", "label", "rules", "required", "placeholder", "min", "rows", "tooltip", "footer", "bordered", "column", "Array", "from", "_", "i", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/Timeline.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  Card,\n  Typography,\n  Button,\n  Timeline as AntTimeline,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Tooltip,\n  Row,\n  Col,\n  Statistic,\n  Descriptions,\n  DatePicker,\n  InputNumber,\n  Divider\n} from 'antd';\nimport {\n  PlusOutlined,\n  ClockCircleOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  RobotOutlined,\n  CalendarOutlined,\n  HistoryOutlined,\n  StarOutlined,\n  ExclamationCircleOutlined,\n  CheckCircleOutlined\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst Timeline = () => {\n  const { id: projectId } = useParams();\n  const [events, setEvents] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingEvent, setEditingEvent] = useState(null);\n  const [viewingEvent, setViewingEvent] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟时间线事件数据\n  const mockEvents = [\n    {\n      id: 1,\n      title: '林天觉醒修仙天赋',\n      type: 'major',\n      category: 'character',\n      timeType: 'story',\n      storyTime: '第1年春',\n      realTime: '2024-01-15',\n      chapter: 1,\n      description: '主角林天在一次意外中觉醒了修仙天赋，从此踏上修仙之路',\n      participants: ['林天'],\n      location: '青云村',\n      consequences: '开启修仙之路，改变人生轨迹',\n      relatedPlots: ['主线：修仙之路'],\n      importance: 5,\n      status: 'completed',\n      createdAt: '2024-01-15'\n    },\n    {\n      id: 2,\n      title: '拜入青云宗',\n      type: 'major',\n      category: 'plot',\n      timeType: 'story',\n      storyTime: '第1年夏',\n      realTime: '2024-01-16',\n      chapter: 3,\n      description: '林天通过考核，正式拜入青云宗，成为云长老的弟子',\n      participants: ['林天', '云长老'],\n      location: '青云宗',\n      consequences: '获得正式修炼资格，开始系统学习',\n      relatedPlots: ['主线：修仙之路'],\n      importance: 4,\n      status: 'completed',\n      createdAt: '2024-01-16'\n    },\n    {\n      id: 3,\n      title: '初遇苏雪儿',\n      type: 'minor',\n      category: 'relationship',\n      timeType: 'story',\n      storyTime: '第1年秋',\n      realTime: '2024-01-17',\n      chapter: 5,\n      description: '林天在宗门内初次遇见苏雪儿，两人产生好感',\n      participants: ['林天', '苏雪儿'],\n      location: '青云宗藏书阁',\n      consequences: '开启感情线，为后续发展埋下伏笔',\n      relatedPlots: ['支线：情感线索'],\n      importance: 3,\n      status: 'completed',\n      createdAt: '2024-01-17'\n    },\n    {\n      id: 4,\n      title: '首次与血煞魔君交锋',\n      type: 'major',\n      category: 'conflict',\n      timeType: 'story',\n      storyTime: '第2年春',\n      realTime: '2024-01-18',\n      chapter: 8,\n      description: '林天在历练中遭遇血煞魔君，双方首次交手',\n      participants: ['林天', '血煞魔君'],\n      location: '血煞谷',\n      consequences: '确立宿敌关系，推动主线剧情发展',\n      relatedPlots: ['主线：修仙之路'],\n      importance: 5,\n      status: 'completed',\n      createdAt: '2024-01-18'\n    },\n    {\n      id: 5,\n      title: '突破筑基期',\n      type: 'minor',\n      category: 'cultivation',\n      timeType: 'story',\n      storyTime: '第2年夏',\n      realTime: '2024-01-19',\n      chapter: 10,\n      description: '林天经过刻苦修炼，成功突破到筑基期',\n      participants: ['林天'],\n      location: '青云宗修炼室',\n      consequences: '实力大幅提升，为后续挑战做准备',\n      relatedPlots: ['主线：修仙之路'],\n      importance: 3,\n      status: 'completed',\n      createdAt: '2024-01-19'\n    }\n  ];\n\n  useEffect(() => {\n    setEvents(mockEvents.sort((a, b) => a.chapter - b.chapter));\n  }, []);\n\n  // 事件类型配置\n  const typeConfig = {\n    major: { color: 'red', text: '重大事件', icon: <StarOutlined /> },\n    minor: { color: 'blue', text: '一般事件', icon: <ClockCircleOutlined /> },\n    milestone: { color: 'gold', text: '里程碑', icon: <CheckCircleOutlined /> }\n  };\n\n  // 事件分类配置\n  const categoryConfig = {\n    character: { color: 'blue', text: '人物' },\n    plot: { color: 'green', text: '剧情' },\n    relationship: { color: 'pink', text: '关系' },\n    conflict: { color: 'red', text: '冲突' },\n    cultivation: { color: 'orange', text: '修炼' },\n    world: { color: 'purple', text: '世界' }\n  };\n\n  // 状态配置\n  const statusConfig = {\n    planned: { color: 'blue', text: '计划中' },\n    ongoing: { color: 'orange', text: '进行中' },\n    completed: { color: 'green', text: '已完成' },\n    cancelled: { color: 'default', text: '已取消' }\n  };\n\n  // 处理新建/编辑事件\n  const handleCreateOrEdit = () => {\n    setEditingEvent(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (event) => {\n    setEditingEvent(event);\n    form.setFieldsValue({\n      ...event,\n      participants: event.participants.join('\\n'),\n      relatedPlots: event.relatedPlots.join('\\n'),\n      realTime: event.realTime ? dayjs(event.realTime) : null\n    });\n    setModalVisible(true);\n  };\n\n  const handleView = (event) => {\n    setViewingEvent(event);\n    setDetailModalVisible(true);\n  };\n\n  const handleAIGenerate = (event) => {\n    message.info(`AI分析事件：${event.title}`);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 调用后端API删除数据\n      await axios.delete(`/api/project-data/projects/${projectId}/data/timeline/${id}`);\n\n      // 删除成功后从列表中移除\n      setEvents(events.filter(e => e.id !== id));\n      message.success('事件删除成功');\n    } catch (error) {\n      console.error('删除事件失败:', error);\n      message.error('删除事件失败');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 处理数组字段和日期\n      const processedValues = {\n        ...values,\n        participants: values.participants ? values.participants.split('\\n').filter(item => item.trim()) : [],\n        relatedPlots: values.relatedPlots ? values.relatedPlots.split('\\n').filter(item => item.trim()) : [],\n        realTime: values.realTime ? values.realTime.format('YYYY-MM-DD') : null\n      };\n\n      if (editingEvent) {\n        // 编辑事件\n        setEvents(events.map(e =>\n          e.id === editingEvent.id\n            ? { ...e, ...processedValues }\n            : e\n        ).sort((a, b) => a.chapter - b.chapter));\n        message.success('事件更新成功');\n      } else {\n        // 新建事件\n        const newEvent = {\n          id: Date.now(),\n          ...processedValues,\n          createdAt: new Date().toISOString().split('T')[0]\n        };\n        setEvents([...events, newEvent].sort((a, b) => a.chapter - b.chapter));\n        message.success('事件创建成功');\n      }\n\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalEvents = events.length;\n  const majorEvents = events.filter(e => e.type === 'major').length;\n  const completedEvents = events.filter(e => e.status === 'completed').length;\n  const plannedEvents = events.filter(e => e.status === 'planned').length;\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">时间线</Title>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总事件数\"\n              value={totalEvents}\n              prefix={<ClockCircleOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"重大事件\"\n              value={majorEvents}\n              prefix={<StarOutlined style={{ color: '#f5222d' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"已完成\"\n              value={completedEvents}\n              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"计划中\"\n              value={plannedEvents}\n              prefix={<ExclamationCircleOutlined style={{ color: '#1890ff' }} />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreateOrEdit}\n            >\n              添加事件\n            </Button>\n          </div>\n        </div>\n\n        {/* 时间线展示 */}\n        <AntTimeline mode=\"left\" style={{ marginTop: 24 }}>\n          {events.map((event) => (\n            <AntTimeline.Item\n              key={event.id}\n              color={typeConfig[event.type].color}\n              dot={typeConfig[event.type].icon}\n            >\n              <Card size=\"small\" style={{ marginBottom: 16 }}>\n                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>\n                  <div style={{ flex: 1 }}>\n                    <Space direction=\"vertical\" size=\"small\" style={{ width: '100%' }}>\n                      <div>\n                        <Text strong style={{ fontSize: '16px' }}>{event.title}</Text>\n                        <Space style={{ marginLeft: 16 }}>\n                          <Tag color={typeConfig[event.type].color}>\n                            {typeConfig[event.type].text}\n                          </Tag>\n                          <Tag color={categoryConfig[event.category].color}>\n                            {categoryConfig[event.category].text}\n                          </Tag>\n                          <Tag color={statusConfig[event.status].color}>\n                            {statusConfig[event.status].text}\n                          </Tag>\n                        </Space>\n                      </div>\n\n                      <Space>\n                        <Text type=\"secondary\">\n                          <CalendarOutlined /> {event.storyTime}\n                        </Text>\n                        <Text type=\"secondary\">\n                          第{event.chapter}章\n                        </Text>\n                        {event.location && (\n                          <Text type=\"secondary\">\n                            📍 {event.location}\n                          </Text>\n                        )}\n                      </Space>\n\n                      <Paragraph style={{ margin: 0, color: '#666' }}>\n                        {event.description}\n                      </Paragraph>\n\n                      {event.participants && event.participants.length > 0 && (\n                        <div>\n                          <Text type=\"secondary\">参与人物：</Text>\n                          <Space wrap>\n                            {event.participants.map((participant, index) => (\n                              <Tag key={index} size=\"small\">{participant}</Tag>\n                            ))}\n                          </Space>\n                        </div>\n                      )}\n                    </Space>\n                  </div>\n\n                  <Space>\n                    <Tooltip title=\"查看详情\">\n                      <Button\n                        type=\"text\"\n                        icon={<EyeOutlined />}\n                        onClick={() => handleView(event)}\n                      />\n                    </Tooltip>\n                    <Tooltip title=\"编辑\">\n                      <Button\n                        type=\"text\"\n                        icon={<EditOutlined />}\n                        onClick={() => handleEdit(event)}\n                      />\n                    </Tooltip>\n                    <Tooltip title=\"AI分析\">\n                      <Button\n                        type=\"text\"\n                        icon={<RobotOutlined />}\n                        onClick={() => handleAIGenerate(event)}\n                      />\n                    </Tooltip>\n                    <Popconfirm\n                      title=\"确定删除这个事件吗？\"\n                      onConfirm={() => handleDelete(event.id)}\n                      okText=\"确定\"\n                      cancelText=\"取消\"\n                    >\n                      <Tooltip title=\"删除\">\n                        <Button\n                          type=\"text\"\n                          danger\n                          icon={<DeleteOutlined />}\n                        />\n                      </Tooltip>\n                    </Popconfirm>\n                  </Space>\n                </div>\n              </Card>\n            </AntTimeline.Item>\n          ))}\n        </AntTimeline>\n      </Card>\n\n      {/* 新建/编辑事件模态框 */}\n      <Modal\n        title={editingEvent ? '编辑事件' : '新建事件'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => {\n          setModalVisible(false);\n          form.resetFields();\n        }}\n        confirmLoading={loading}\n        width={900}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            type: 'minor',\n            category: 'plot',\n            timeType: 'story',\n            status: 'planned',\n            importance: 3\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"title\"\n                label=\"事件标题\"\n                rules={[{ required: true, message: '请输入事件标题' }]}\n              >\n                <Input placeholder=\"请输入事件标题\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"type\"\n                label=\"事件类型\"\n                rules={[{ required: true, message: '请选择事件类型' }]}\n              >\n                <Select>\n                  <Option value=\"major\">重大事件</Option>\n                  <Option value=\"minor\">一般事件</Option>\n                  <Option value=\"milestone\">里程碑</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"category\"\n                label=\"事件分类\"\n                rules={[{ required: true, message: '请选择事件分类' }]}\n              >\n                <Select>\n                  <Option value=\"character\">人物</Option>\n                  <Option value=\"plot\">剧情</Option>\n                  <Option value=\"relationship\">关系</Option>\n                  <Option value=\"conflict\">冲突</Option>\n                  <Option value=\"cultivation\">修炼</Option>\n                  <Option value=\"world\">世界</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"status\"\n                label=\"事件状态\"\n                rules={[{ required: true, message: '请选择事件状态' }]}\n              >\n                <Select>\n                  <Option value=\"planned\">计划中</Option>\n                  <Option value=\"ongoing\">进行中</Option>\n                  <Option value=\"completed\">已完成</Option>\n                  <Option value=\"cancelled\">已取消</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"importance\"\n                label=\"重要程度\"\n                rules={[{ required: true, message: '请选择重要程度' }]}\n              >\n                <Select>\n                  <Option value={1}>1 - 很低</Option>\n                  <Option value={2}>2 - 低</Option>\n                  <Option value={3}>3 - 中等</Option>\n                  <Option value={4}>4 - 高</Option>\n                  <Option value={5}>5 - 很高</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"storyTime\"\n                label=\"故事时间\"\n                rules={[{ required: true, message: '请输入故事时间' }]}\n              >\n                <Input placeholder=\"如：第1年春\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"chapter\"\n                label=\"所在章节\"\n                rules={[{ required: true, message: '请输入章节' }]}\n              >\n                <InputNumber min={1} style={{ width: '100%' }} placeholder=\"章节号\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"realTime\"\n                label=\"现实时间\"\n              >\n                <DatePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"location\"\n            label=\"发生地点\"\n          >\n            <Input placeholder=\"事件发生的地点\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"事件描述\"\n            rules={[{ required: true, message: '请输入事件描述' }]}\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"请详细描述这个事件的内容\"\n            />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"participants\"\n                label=\"参与人物\"\n                tooltip=\"每行一个人物\"\n              >\n                <TextArea\n                  rows={3}\n                  placeholder=\"如：林天&#10;苏雪儿&#10;云长老\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"relatedPlots\"\n                label=\"相关剧情\"\n                tooltip=\"每行一个剧情\"\n              >\n                <TextArea\n                  rows={3}\n                  placeholder=\"如：主线：修仙之路&#10;支线：情感线索\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"consequences\"\n            label=\"事件后果\"\n          >\n            <TextArea\n              rows={2}\n              placeholder=\"这个事件产生的影响和后果\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 事件详情查看模态框 */}\n      <Modal\n        title=\"事件详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {viewingEvent && (\n          <div>\n            <Descriptions bordered column={2} style={{ marginBottom: 24 }}>\n              <Descriptions.Item label=\"事件标题\" span={2}>\n                <Space>\n                  {typeConfig[viewingEvent.type].icon}\n                  <Text strong>{viewingEvent.title}</Text>\n                  <Tag color={typeConfig[viewingEvent.type].color}>\n                    {typeConfig[viewingEvent.type].text}\n                  </Tag>\n                  <Tag color={categoryConfig[viewingEvent.category].color}>\n                    {categoryConfig[viewingEvent.category].text}\n                  </Tag>\n                  <Tag color={statusConfig[viewingEvent.status].color}>\n                    {statusConfig[viewingEvent.status].text}\n                  </Tag>\n                </Space>\n              </Descriptions.Item>\n\n              <Descriptions.Item label=\"故事时间\">{viewingEvent.storyTime}</Descriptions.Item>\n              <Descriptions.Item label=\"所在章节\">第{viewingEvent.chapter}章</Descriptions.Item>\n\n              {viewingEvent.realTime && (\n                <Descriptions.Item label=\"现实时间\">{viewingEvent.realTime}</Descriptions.Item>\n              )}\n              {viewingEvent.location && (\n                <Descriptions.Item label=\"发生地点\">{viewingEvent.location}</Descriptions.Item>\n              )}\n\n              <Descriptions.Item label=\"重要程度\">\n                <Space>\n                  {Array.from({ length: viewingEvent.importance }, (_, i) => (\n                    <StarOutlined key={i} style={{ color: '#faad14' }} />\n                  ))}\n                  <Text>({viewingEvent.importance}/5)</Text>\n                </Space>\n              </Descriptions.Item>\n              <Descriptions.Item label=\"创建时间\">{viewingEvent.createdAt}</Descriptions.Item>\n\n              <Descriptions.Item label=\"事件描述\" span={2}>\n                <Paragraph>{viewingEvent.description}</Paragraph>\n              </Descriptions.Item>\n\n              {viewingEvent.consequences && (\n                <Descriptions.Item label=\"事件后果\" span={2}>\n                  <Paragraph>{viewingEvent.consequences}</Paragraph>\n                </Descriptions.Item>\n              )}\n            </Descriptions>\n\n            <Row gutter={16} style={{ marginTop: 16 }}>\n              <Col span={12}>\n                <Card title=\"参与人物\" size=\"small\">\n                  {viewingEvent.participants && viewingEvent.participants.length > 0 ? (\n                    <Space wrap>\n                      {viewingEvent.participants.map((participant, index) => (\n                        <Tag key={index} color=\"blue\">{participant}</Tag>\n                      ))}\n                    </Space>\n                  ) : (\n                    <Text type=\"secondary\">暂无参与人物</Text>\n                  )}\n                </Card>\n              </Col>\n              <Col span={12}>\n                <Card title=\"相关剧情\" size=\"small\">\n                  {viewingEvent.relatedPlots && viewingEvent.relatedPlots.length > 0 ? (\n                    <Space wrap>\n                      {viewingEvent.relatedPlots.map((plot, index) => (\n                        <Tag key={index} color=\"green\">{plot}</Tag>\n                      ))}\n                    </Space>\n                  ) : (\n                    <Text type=\"secondary\">暂无相关剧情</Text>\n                  )}\n                </Card>\n              </Col>\n            </Row>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default Timeline;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,QAAQ,IAAIC,WAAW,EACvBC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,YAAY,EACZC,UAAU,EACVC,WAAW,EACXC,OAAO,QACF,MAAM;AACb,SACEC,YAAY,EACZC,mBAAmB,EACnBC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,gBAAgB,EAChBC,eAAe,EACfC,YAAY,EACZC,yBAAyB,EACzBC,mBAAmB,QACd,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGpC,UAAU;AAC7C,MAAM;EAAEqC;AAAS,CAAC,GAAG7B,KAAK;AAC1B,MAAM;EAAE8B;AAAO,CAAC,GAAG7B,MAAM;AAEzB,MAAMP,QAAQ,GAAGA,CAAA,KAAM;EAAAqC,EAAA;EACrB,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAG5C,SAAS,CAAC,CAAC;EACrC,MAAM,CAAC6C,MAAM,EAAEC,SAAS,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACmD,YAAY,EAAEC,eAAe,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC2D,IAAI,CAAC,GAAG/C,IAAI,CAACgD,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,UAAU,GAAG,CACjB;IACEhB,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,UAAU;IACjBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,4BAA4B;IACzCC,YAAY,EAAE,CAAC,IAAI,CAAC;IACpBC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,CAAC,SAAS,CAAC;IACzBC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,yBAAyB;IACtCC,YAAY,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;IAC3BC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,iBAAiB;IAC/BC,YAAY,EAAE,CAAC,SAAS,CAAC;IACzBC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,cAAc;IACxBC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,sBAAsB;IACnCC,YAAY,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;IAC3BC,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,iBAAiB;IAC/BC,YAAY,EAAE,CAAC,SAAS,CAAC;IACzBC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,CAAC;IACVC,WAAW,EAAE,qBAAqB;IAClCC,YAAY,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC;IAC5BC,QAAQ,EAAE,KAAK;IACfC,YAAY,EAAE,iBAAiB;IAC/BC,YAAY,EAAE,CAAC,SAAS,CAAC;IACzBC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE;EACb,CAAC,EACD;IACE/B,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,OAAO;IACbC,QAAQ,EAAE,aAAa;IACvBC,QAAQ,EAAE,OAAO;IACjBC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,YAAY;IACtBC,OAAO,EAAE,EAAE;IACXC,WAAW,EAAE,mBAAmB;IAChCC,YAAY,EAAE,CAAC,IAAI,CAAC;IACpBC,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,iBAAiB;IAC/BC,YAAY,EAAE,CAAC,SAAS,CAAC;IACzBC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAE,WAAW;IACnBC,SAAS,EAAE;EACb,CAAC,CACF;EAED3E,SAAS,CAAC,MAAM;IACd+C,SAAS,CAACa,UAAU,CAACgB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACV,OAAO,GAAGW,CAAC,CAACX,OAAO,CAAC,CAAC;EAC7D,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMY,UAAU,GAAG;IACjBC,KAAK,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAE9C,OAAA,CAACL,YAAY;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC7DC,KAAK,EAAE;MAAEP,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,MAAM;MAAEC,IAAI,eAAE9C,OAAA,CAACZ,mBAAmB;QAAA2D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACrEE,SAAS,EAAE;MAAER,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,KAAK;MAAEC,IAAI,eAAE9C,OAAA,CAACH,mBAAmB;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;EACzE,CAAC;;EAED;EACA,MAAMG,cAAc,GAAG;IACrBC,SAAS,EAAE;MAAEV,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAK,CAAC;IACxCU,IAAI,EAAE;MAAEX,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAK,CAAC;IACpCW,YAAY,EAAE;MAAEZ,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAK,CAAC;IAC3CY,QAAQ,EAAE;MAAEb,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAC;IACtCa,WAAW,EAAE;MAAEd,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK,CAAC;IAC5Cc,KAAK,EAAE;MAAEf,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK;EACvC,CAAC;;EAED;EACA,MAAMe,YAAY,GAAG;IACnBC,OAAO,EAAE;MAAEjB,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACvCiB,OAAO,EAAE;MAAElB,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAM,CAAC;IACzCkB,SAAS,EAAE;MAAEnB,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC1CmB,SAAS,EAAE;MAAEpB,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAM;EAC7C,CAAC;;EAED;EACA,MAAMoB,kBAAkB,GAAGA,CAAA,KAAM;IAC/B/C,eAAe,CAAC,IAAI,CAAC;IACrBG,IAAI,CAAC6C,WAAW,CAAC,CAAC;IAClBpD,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqD,UAAU,GAAIC,KAAK,IAAK;IAC5BlD,eAAe,CAACkD,KAAK,CAAC;IACtB/C,IAAI,CAACgD,cAAc,CAAC;MAClB,GAAGD,KAAK;MACRpC,YAAY,EAAEoC,KAAK,CAACpC,YAAY,CAACsC,IAAI,CAAC,IAAI,CAAC;MAC3CnC,YAAY,EAAEiC,KAAK,CAACjC,YAAY,CAACmC,IAAI,CAAC,IAAI,CAAC;MAC3CzC,QAAQ,EAAEuC,KAAK,CAACvC,QAAQ,GAAG/B,KAAK,CAACsE,KAAK,CAACvC,QAAQ,CAAC,GAAG;IACrD,CAAC,CAAC;IACFf,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMyD,UAAU,GAAIH,KAAK,IAAK;IAC5BhD,eAAe,CAACgD,KAAK,CAAC;IACtBpD,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMwD,gBAAgB,GAAIJ,KAAK,IAAK;IAClC3F,OAAO,CAACgG,IAAI,CAAC,UAAUL,KAAK,CAAC5C,KAAK,EAAE,CAAC;EACvC,CAAC;EAED,MAAMkD,YAAY,GAAG,MAAOnE,EAAE,IAAK;IACjC,IAAI;MACF;MACA,MAAM1C,KAAK,CAAC8G,MAAM,CAAC,8BAA8BnE,SAAS,kBAAkBD,EAAE,EAAE,CAAC;;MAEjF;MACAG,SAAS,CAACD,MAAM,CAACmE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtE,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC1C9B,OAAO,CAACqG,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BtG,OAAO,CAACsG,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM7D,IAAI,CAAC8D,cAAc,CAAC,CAAC;MAC1CvE,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMwE,eAAe,GAAG;QACtB,GAAGF,MAAM;QACTlD,YAAY,EAAEkD,MAAM,CAAClD,YAAY,GAAGkD,MAAM,CAAClD,YAAY,CAACqD,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QACpGpD,YAAY,EAAE+C,MAAM,CAAC/C,YAAY,GAAG+C,MAAM,CAAC/C,YAAY,CAACkD,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QACpG1D,QAAQ,EAAEqD,MAAM,CAACrD,QAAQ,GAAGqD,MAAM,CAACrD,QAAQ,CAAC2D,MAAM,CAAC,YAAY,CAAC,GAAG;MACrE,CAAC;MAED,IAAIvE,YAAY,EAAE;QAChB;QACAP,SAAS,CAACD,MAAM,CAACgF,GAAG,CAACZ,CAAC,IACpBA,CAAC,CAACtE,EAAE,KAAKU,YAAY,CAACV,EAAE,GACpB;UAAE,GAAGsE,CAAC;UAAE,GAAGO;QAAgB,CAAC,GAC5BP,CACN,CAAC,CAACtC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACV,OAAO,GAAGW,CAAC,CAACX,OAAO,CAAC,CAAC;QACxCrD,OAAO,CAACqG,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMY,QAAQ,GAAG;UACfnF,EAAE,EAAEoF,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGR,eAAe;UAClB9C,SAAS,EAAE,IAAIqD,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACR,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACD3E,SAAS,CAAC,CAAC,GAAGD,MAAM,EAAEiF,QAAQ,CAAC,CAACnD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACV,OAAO,GAAGW,CAAC,CAACX,OAAO,CAAC,CAAC;QACtErD,OAAO,CAACqG,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEAhE,eAAe,CAAC,KAAK,CAAC;MACtBO,IAAI,CAAC6C,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRnE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkF,WAAW,GAAGrF,MAAM,CAACsF,MAAM;EACjC,MAAMC,WAAW,GAAGvF,MAAM,CAACmE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACpD,IAAI,KAAK,OAAO,CAAC,CAACsE,MAAM;EACjE,MAAME,eAAe,GAAGxF,MAAM,CAACmE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,WAAW,CAAC,CAAC0D,MAAM;EAC3E,MAAMG,aAAa,GAAGzF,MAAM,CAACmE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxC,MAAM,KAAK,SAAS,CAAC,CAAC0D,MAAM;EAEvE,oBACE/F,OAAA;IAAKmG,SAAS,EAAC,SAAS;IAAAC,QAAA,gBACtBpG,OAAA;MAAKmG,SAAS,EAAC,aAAa;MAAAC,QAAA,eAC1BpG,OAAA,CAACC,KAAK;QAACoG,KAAK,EAAE,CAAE;QAACF,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAG;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC,eAGNlD,OAAA,CAACpB,GAAG;MAAC0H,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAAJ,QAAA,gBAC3CpG,OAAA,CAACnB,GAAG;QAAC4H,IAAI,EAAE,CAAE;QAAAL,QAAA,eACXpG,OAAA,CAAClC,IAAI;UAAAsI,QAAA,eACHpG,OAAA,CAAClB,SAAS;YACR0C,KAAK,EAAC,0BAAM;YACZkF,KAAK,EAAEZ,WAAY;YACnBa,MAAM,eAAE3G,OAAA,CAACZ,mBAAmB;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACnB,GAAG;QAAC4H,IAAI,EAAE,CAAE;QAAAL,QAAA,eACXpG,OAAA,CAAClC,IAAI;UAAAsI,QAAA,eACHpG,OAAA,CAAClB,SAAS;YACR0C,KAAK,EAAC,0BAAM;YACZkF,KAAK,EAAEV,WAAY;YACnBW,MAAM,eAAE3G,OAAA,CAACL,YAAY;cAAC4G,KAAK,EAAE;gBAAE3D,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACnB,GAAG;QAAC4H,IAAI,EAAE,CAAE;QAAAL,QAAA,eACXpG,OAAA,CAAClC,IAAI;UAAAsI,QAAA,eACHpG,OAAA,CAAClB,SAAS;YACR0C,KAAK,EAAC,oBAAK;YACXkF,KAAK,EAAET,eAAgB;YACvBU,MAAM,eAAE3G,OAAA,CAACH,mBAAmB;cAAC0G,KAAK,EAAE;gBAAE3D,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAACnB,GAAG;QAAC4H,IAAI,EAAE,CAAE;QAAAL,QAAA,eACXpG,OAAA,CAAClC,IAAI;UAAAsI,QAAA,eACHpG,OAAA,CAAClB,SAAS;YACR0C,KAAK,EAAC,oBAAK;YACXkF,KAAK,EAAER,aAAc;YACrBS,MAAM,eAAE3G,OAAA,CAACJ,yBAAyB;cAAC2G,KAAK,EAAE;gBAAE3D,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlD,OAAA,CAAClC,IAAI;MAAAsI,QAAA,gBACHpG,OAAA;QAAKmG,SAAS,EAAC,SAAS;QAAAC,QAAA,eACtBpG,OAAA;UAAKmG,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BpG,OAAA,CAAChC,MAAM;YACLyD,IAAI,EAAC,SAAS;YACdqB,IAAI,eAAE9C,OAAA,CAACb,YAAY;cAAA4D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB0D,OAAO,EAAE3C,kBAAmB;YAAAmC,QAAA,EAC7B;UAED;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlD,OAAA,CAAC9B,WAAW;QAAC2I,IAAI,EAAC,MAAM;QAACN,KAAK,EAAE;UAAEO,SAAS,EAAE;QAAG,CAAE;QAAAV,QAAA,EAC/C3F,MAAM,CAACgF,GAAG,CAAErB,KAAK,iBAChBpE,OAAA,CAAC9B,WAAW,CAAC6I,IAAI;UAEfnE,KAAK,EAAEF,UAAU,CAAC0B,KAAK,CAAC3C,IAAI,CAAC,CAACmB,KAAM;UACpCoE,GAAG,EAAEtE,UAAU,CAAC0B,KAAK,CAAC3C,IAAI,CAAC,CAACqB,IAAK;UAAAsD,QAAA,eAEjCpG,OAAA,CAAClC,IAAI;YAACmJ,IAAI,EAAC,OAAO;YAACV,KAAK,EAAE;cAAEC,YAAY,EAAE;YAAG,CAAE;YAAAJ,QAAA,eAC7CpG,OAAA;cAAKuG,KAAK,EAAE;gBAAEW,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEC,UAAU,EAAE;cAAa,CAAE;cAAAhB,QAAA,gBACzFpG,OAAA;gBAAKuG,KAAK,EAAE;kBAAEc,IAAI,EAAE;gBAAE,CAAE;gBAAAjB,QAAA,eACtBpG,OAAA,CAAC7B,KAAK;kBAACmJ,SAAS,EAAC,UAAU;kBAACL,IAAI,EAAC,OAAO;kBAACV,KAAK,EAAE;oBAAEgB,KAAK,EAAE;kBAAO,CAAE;kBAAAnB,QAAA,gBAChEpG,OAAA;oBAAAoG,QAAA,gBACEpG,OAAA,CAACE,IAAI;sBAACsH,MAAM;sBAACjB,KAAK,EAAE;wBAAEkB,QAAQ,EAAE;sBAAO,CAAE;sBAAArB,QAAA,EAAEhC,KAAK,CAAC5C;oBAAK;sBAAAuB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAC9DlD,OAAA,CAAC7B,KAAK;sBAACoI,KAAK,EAAE;wBAAEmB,UAAU,EAAE;sBAAG,CAAE;sBAAAtB,QAAA,gBAC/BpG,OAAA,CAAC5B,GAAG;wBAACwE,KAAK,EAAEF,UAAU,CAAC0B,KAAK,CAAC3C,IAAI,CAAC,CAACmB,KAAM;wBAAAwD,QAAA,EACtC1D,UAAU,CAAC0B,KAAK,CAAC3C,IAAI,CAAC,CAACoB;sBAAI;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CAAC,eACNlD,OAAA,CAAC5B,GAAG;wBAACwE,KAAK,EAAES,cAAc,CAACe,KAAK,CAAC1C,QAAQ,CAAC,CAACkB,KAAM;wBAAAwD,QAAA,EAC9C/C,cAAc,CAACe,KAAK,CAAC1C,QAAQ,CAAC,CAACmB;sBAAI;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC,eACNlD,OAAA,CAAC5B,GAAG;wBAACwE,KAAK,EAAEgB,YAAY,CAACQ,KAAK,CAAC/B,MAAM,CAAC,CAACO,KAAM;wBAAAwD,QAAA,EAC1CxC,YAAY,CAACQ,KAAK,CAAC/B,MAAM,CAAC,CAACQ;sBAAI;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAENlD,OAAA,CAAC7B,KAAK;oBAAAiI,QAAA,gBACJpG,OAAA,CAACE,IAAI;sBAACuB,IAAI,EAAC,WAAW;sBAAA2E,QAAA,gBACpBpG,OAAA,CAACP,gBAAgB;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,KAAC,EAACkB,KAAK,CAACxC,SAAS;oBAAA;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjC,CAAC,eACPlD,OAAA,CAACE,IAAI;sBAACuB,IAAI,EAAC,WAAW;sBAAA2E,QAAA,GAAC,QACpB,EAAChC,KAAK,CAACtC,OAAO,EAAC,QAClB;oBAAA;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EACNkB,KAAK,CAACnC,QAAQ,iBACbjC,OAAA,CAACE,IAAI;sBAACuB,IAAI,EAAC,WAAW;sBAAA2E,QAAA,GAAC,eAClB,EAAChC,KAAK,CAACnC,QAAQ;oBAAA;sBAAAc,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CACP;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC,eAERlD,OAAA,CAACG,SAAS;oBAACoG,KAAK,EAAE;sBAAEoB,MAAM,EAAE,CAAC;sBAAE/E,KAAK,EAAE;oBAAO,CAAE;oBAAAwD,QAAA,EAC5ChC,KAAK,CAACrC;kBAAW;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,EAEXkB,KAAK,CAACpC,YAAY,IAAIoC,KAAK,CAACpC,YAAY,CAAC+D,MAAM,GAAG,CAAC,iBAClD/F,OAAA;oBAAAoG,QAAA,gBACEpG,OAAA,CAACE,IAAI;sBAACuB,IAAI,EAAC,WAAW;sBAAA2E,QAAA,EAAC;oBAAK;sBAAArD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACnClD,OAAA,CAAC7B,KAAK;sBAACyJ,IAAI;sBAAAxB,QAAA,EACRhC,KAAK,CAACpC,YAAY,CAACyD,GAAG,CAAC,CAACoC,WAAW,EAAEC,KAAK,kBACzC9H,OAAA,CAAC5B,GAAG;wBAAa6I,IAAI,EAAC,OAAO;wBAAAb,QAAA,EAAEyB;sBAAW,GAAhCC,KAAK;wBAAA/E,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAiC,CACjD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAENlD,OAAA,CAAC7B,KAAK;gBAAAiI,QAAA,gBACJpG,OAAA,CAACrB,OAAO;kBAAC6C,KAAK,EAAC,0BAAM;kBAAA4E,QAAA,eACnBpG,OAAA,CAAChC,MAAM;oBACLyD,IAAI,EAAC,MAAM;oBACXqB,IAAI,eAAE9C,OAAA,CAACT,WAAW;sBAAAwD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACtB0D,OAAO,EAAEA,CAAA,KAAMrC,UAAU,CAACH,KAAK;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACVlD,OAAA,CAACrB,OAAO;kBAAC6C,KAAK,EAAC,cAAI;kBAAA4E,QAAA,eACjBpG,OAAA,CAAChC,MAAM;oBACLyD,IAAI,EAAC,MAAM;oBACXqB,IAAI,eAAE9C,OAAA,CAACX,YAAY;sBAAA0D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACvB0D,OAAO,EAAEA,CAAA,KAAMzC,UAAU,CAACC,KAAK;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACVlD,OAAA,CAACrB,OAAO;kBAAC6C,KAAK,EAAC,gBAAM;kBAAA4E,QAAA,eACnBpG,OAAA,CAAChC,MAAM;oBACLyD,IAAI,EAAC,MAAM;oBACXqB,IAAI,eAAE9C,OAAA,CAACR,aAAa;sBAAAuD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBACxB0D,OAAO,EAAEA,CAAA,KAAMpC,gBAAgB,CAACJ,KAAK;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACK,CAAC,eACVlD,OAAA,CAACtB,UAAU;kBACT8C,KAAK,EAAC,8DAAY;kBAClBuG,SAAS,EAAEA,CAAA,KAAMrD,YAAY,CAACN,KAAK,CAAC7D,EAAE,CAAE;kBACxCyH,MAAM,EAAC,cAAI;kBACXC,UAAU,EAAC,cAAI;kBAAA7B,QAAA,eAEfpG,OAAA,CAACrB,OAAO;oBAAC6C,KAAK,EAAC,cAAI;oBAAA4E,QAAA,eACjBpG,OAAA,CAAChC,MAAM;sBACLyD,IAAI,EAAC,MAAM;sBACXyG,MAAM;sBACNpF,IAAI,eAAE9C,OAAA,CAACV,cAAc;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAE;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GA5FFkB,KAAK,CAAC7D,EAAE;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6FG,CACnB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGPlD,OAAA,CAAC3B,KAAK;MACJmD,KAAK,EAAEP,YAAY,GAAG,MAAM,GAAG,MAAO;MACtCkH,IAAI,EAAEtH,YAAa;MACnBuH,IAAI,EAAEnD,aAAc;MACpBoD,QAAQ,EAAEA,CAAA,KAAM;QACdvH,eAAe,CAAC,KAAK,CAAC;QACtBO,IAAI,CAAC6C,WAAW,CAAC,CAAC;MACpB,CAAE;MACFoE,cAAc,EAAE3H,OAAQ;MACxB4G,KAAK,EAAE,GAAI;MAAAnB,QAAA,eAEXpG,OAAA,CAAC1B,IAAI;QACH+C,IAAI,EAAEA,IAAK;QACXkH,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACb/G,IAAI,EAAE,OAAO;UACbC,QAAQ,EAAE,MAAM;UAChBC,QAAQ,EAAE,OAAO;UACjBU,MAAM,EAAE,SAAS;UACjBD,UAAU,EAAE;QACd,CAAE;QAAAgE,QAAA,gBAEFpG,OAAA,CAACpB,GAAG;UAAC0H,MAAM,EAAE,EAAG;UAAAF,QAAA,gBACdpG,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,EAAG;YAAAL,QAAA,eACZpG,OAAA,CAAC1B,IAAI,CAACyI,IAAI;cACR0B,IAAI,EAAC,OAAO;cACZC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2H,QAAA,eAEhDpG,OAAA,CAACzB,KAAK;gBAACsK,WAAW,EAAC;cAAS;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,CAAE;YAAAL,QAAA,eACXpG,OAAA,CAAC1B,IAAI,CAACyI,IAAI;cACR0B,IAAI,EAAC,MAAM;cACXC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2H,QAAA,eAEhDpG,OAAA,CAACxB,MAAM;gBAAA4H,QAAA,gBACLpG,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,OAAO;kBAAAN,QAAA,EAAC;gBAAI;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,OAAO;kBAAAN,QAAA,EAAC;gBAAI;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,WAAW;kBAAAN,QAAA,EAAC;gBAAG;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAACpB,GAAG;UAAC0H,MAAM,EAAE,EAAG;UAAAF,QAAA,gBACdpG,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,CAAE;YAAAL,QAAA,eACXpG,OAAA,CAAC1B,IAAI,CAACyI,IAAI;cACR0B,IAAI,EAAC,UAAU;cACfC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2H,QAAA,eAEhDpG,OAAA,CAACxB,MAAM;gBAAA4H,QAAA,gBACLpG,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,WAAW;kBAAAN,QAAA,EAAC;gBAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,MAAM;kBAAAN,QAAA,EAAC;gBAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,cAAc;kBAAAN,QAAA,EAAC;gBAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,UAAU;kBAAAN,QAAA,EAAC;gBAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,aAAa;kBAAAN,QAAA,EAAC;gBAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,OAAO;kBAAAN,QAAA,EAAC;gBAAE;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,CAAE;YAAAL,QAAA,eACXpG,OAAA,CAAC1B,IAAI,CAACyI,IAAI;cACR0B,IAAI,EAAC,QAAQ;cACbC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2H,QAAA,eAEhDpG,OAAA,CAACxB,MAAM;gBAAA4H,QAAA,gBACLpG,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,SAAS;kBAAAN,QAAA,EAAC;gBAAG;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,SAAS;kBAAAN,QAAA,EAAC;gBAAG;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,WAAW;kBAAAN,QAAA,EAAC;gBAAG;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAC,WAAW;kBAAAN,QAAA,EAAC;gBAAG;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,CAAE;YAAAL,QAAA,eACXpG,OAAA,CAAC1B,IAAI,CAACyI,IAAI;cACR0B,IAAI,EAAC,YAAY;cACjBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2H,QAAA,eAEhDpG,OAAA,CAACxB,MAAM;gBAAA4H,QAAA,gBACLpG,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAM;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAK;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAM;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAK;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClD,OAAA,CAACK,MAAM;kBAACqG,KAAK,EAAE,CAAE;kBAAAN,QAAA,EAAC;gBAAM;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAACpB,GAAG;UAAC0H,MAAM,EAAE,EAAG;UAAAF,QAAA,gBACdpG,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,CAAE;YAAAL,QAAA,eACXpG,OAAA,CAAC1B,IAAI,CAACyI,IAAI;cACR0B,IAAI,EAAC,WAAW;cAChBC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2H,QAAA,eAEhDpG,OAAA,CAACzB,KAAK;gBAACsK,WAAW,EAAC;cAAQ;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,CAAE;YAAAL,QAAA,eACXpG,OAAA,CAAC1B,IAAI,CAACyI,IAAI;cACR0B,IAAI,EAAC,SAAS;cACdC,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEnK,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAA2H,QAAA,eAE9CpG,OAAA,CAACf,WAAW;gBAAC6J,GAAG,EAAE,CAAE;gBAACvC,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO,CAAE;gBAACsB,WAAW,EAAC;cAAK;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,CAAE;YAAAL,QAAA,eACXpG,OAAA,CAAC1B,IAAI,CAACyI,IAAI;cACR0B,IAAI,EAAC,UAAU;cACfC,KAAK,EAAC,0BAAM;cAAAtC,QAAA,eAEZpG,OAAA,CAAChB,UAAU;gBAACuH,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO;cAAE;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAAC1B,IAAI,CAACyI,IAAI;UACR0B,IAAI,EAAC,UAAU;UACfC,KAAK,EAAC,0BAAM;UAAAtC,QAAA,eAEZpG,OAAA,CAACzB,KAAK;YAACsK,WAAW,EAAC;UAAS;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZlD,OAAA,CAAC1B,IAAI,CAACyI,IAAI;UACR0B,IAAI,EAAC,aAAa;UAClBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEnK,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA2H,QAAA,eAEhDpG,OAAA,CAACI,QAAQ;YACP2I,IAAI,EAAE,CAAE;YACRF,WAAW,EAAC;UAAc;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZlD,OAAA,CAACpB,GAAG;UAAC0H,MAAM,EAAE,EAAG;UAAAF,QAAA,gBACdpG,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,EAAG;YAAAL,QAAA,eACZpG,OAAA,CAAC1B,IAAI,CAACyI,IAAI;cACR0B,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAC,0BAAM;cACZM,OAAO,EAAC,sCAAQ;cAAA5C,QAAA,eAEhBpG,OAAA,CAACI,QAAQ;gBACP2I,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC;cAAsB;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,EAAG;YAAAL,QAAA,eACZpG,OAAA,CAAC1B,IAAI,CAACyI,IAAI;cACR0B,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAC,0BAAM;cACZM,OAAO,EAAC,sCAAQ;cAAA5C,QAAA,eAEhBpG,OAAA,CAACI,QAAQ;gBACP2I,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC;cAAuB;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAAC1B,IAAI,CAACyI,IAAI;UACR0B,IAAI,EAAC,cAAc;UACnBC,KAAK,EAAC,0BAAM;UAAAtC,QAAA,eAEZpG,OAAA,CAACI,QAAQ;YACP2I,IAAI,EAAE,CAAE;YACRF,WAAW,EAAC;UAAc;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRlD,OAAA,CAAC3B,KAAK;MACJmD,KAAK,EAAC,0BAAM;MACZ2G,IAAI,EAAEpH,kBAAmB;MACzBsH,QAAQ,EAAEA,CAAA,KAAMrH,qBAAqB,CAAC,KAAK,CAAE;MAC7CiI,MAAM,EAAE,cACNjJ,OAAA,CAAChC,MAAM;QAAa4I,OAAO,EAAEA,CAAA,KAAM5F,qBAAqB,CAAC,KAAK,CAAE;QAAAoF,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFqE,KAAK,EAAE,GAAI;MAAAnB,QAAA,EAEVjF,YAAY,iBACXnB,OAAA;QAAAoG,QAAA,gBACEpG,OAAA,CAACjB,YAAY;UAACmK,QAAQ;UAACC,MAAM,EAAE,CAAE;UAAC5C,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAAJ,QAAA,gBAC5DpG,OAAA,CAACjB,YAAY,CAACgI,IAAI;YAAC2B,KAAK,EAAC,0BAAM;YAACjC,IAAI,EAAE,CAAE;YAAAL,QAAA,eACtCpG,OAAA,CAAC7B,KAAK;cAAAiI,QAAA,GACH1D,UAAU,CAACvB,YAAY,CAACM,IAAI,CAAC,CAACqB,IAAI,eACnC9C,OAAA,CAACE,IAAI;gBAACsH,MAAM;gBAAApB,QAAA,EAAEjF,YAAY,CAACK;cAAK;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACxClD,OAAA,CAAC5B,GAAG;gBAACwE,KAAK,EAAEF,UAAU,CAACvB,YAAY,CAACM,IAAI,CAAC,CAACmB,KAAM;gBAAAwD,QAAA,EAC7C1D,UAAU,CAACvB,YAAY,CAACM,IAAI,CAAC,CAACoB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CAAC,eACNlD,OAAA,CAAC5B,GAAG;gBAACwE,KAAK,EAAES,cAAc,CAAClC,YAAY,CAACO,QAAQ,CAAC,CAACkB,KAAM;gBAAAwD,QAAA,EACrD/C,cAAc,CAAClC,YAAY,CAACO,QAAQ,CAAC,CAACmB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC,CAAC,eACNlD,OAAA,CAAC5B,GAAG;gBAACwE,KAAK,EAAEgB,YAAY,CAACzC,YAAY,CAACkB,MAAM,CAAC,CAACO,KAAM;gBAAAwD,QAAA,EACjDxC,YAAY,CAACzC,YAAY,CAACkB,MAAM,CAAC,CAACQ;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eAEpBlD,OAAA,CAACjB,YAAY,CAACgI,IAAI;YAAC2B,KAAK,EAAC,0BAAM;YAAAtC,QAAA,EAAEjF,YAAY,CAACS;UAAS;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAC5ElD,OAAA,CAACjB,YAAY,CAACgI,IAAI;YAAC2B,KAAK,EAAC,0BAAM;YAAAtC,QAAA,GAAC,QAAC,EAACjF,YAAY,CAACW,OAAO,EAAC,QAAC;UAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAAC,EAE3E/B,YAAY,CAACU,QAAQ,iBACpB7B,OAAA,CAACjB,YAAY,CAACgI,IAAI;YAAC2B,KAAK,EAAC,0BAAM;YAAAtC,QAAA,EAAEjF,YAAY,CAACU;UAAQ;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAC3E,EACA/B,YAAY,CAACc,QAAQ,iBACpBjC,OAAA,CAACjB,YAAY,CAACgI,IAAI;YAAC2B,KAAK,EAAC,0BAAM;YAAAtC,QAAA,EAAEjF,YAAY,CAACc;UAAQ;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAC3E,eAEDlD,OAAA,CAACjB,YAAY,CAACgI,IAAI;YAAC2B,KAAK,EAAC,0BAAM;YAAAtC,QAAA,eAC7BpG,OAAA,CAAC7B,KAAK;cAAAiI,QAAA,GACHgD,KAAK,CAACC,IAAI,CAAC;gBAAEtD,MAAM,EAAE5E,YAAY,CAACiB;cAAW,CAAC,EAAE,CAACkH,CAAC,EAAEC,CAAC,kBACpDvJ,OAAA,CAACL,YAAY;gBAAS4G,KAAK,EAAE;kBAAE3D,KAAK,EAAE;gBAAU;cAAE,GAA/B2G,CAAC;gBAAAxG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAgC,CACrD,CAAC,eACFlD,OAAA,CAACE,IAAI;gBAAAkG,QAAA,GAAC,GAAC,EAACjF,YAAY,CAACiB,UAAU,EAAC,KAAG;cAAA;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACpBlD,OAAA,CAACjB,YAAY,CAACgI,IAAI;YAAC2B,KAAK,EAAC,0BAAM;YAAAtC,QAAA,EAAEjF,YAAY,CAACmB;UAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAE5ElD,OAAA,CAACjB,YAAY,CAACgI,IAAI;YAAC2B,KAAK,EAAC,0BAAM;YAACjC,IAAI,EAAE,CAAE;YAAAL,QAAA,eACtCpG,OAAA,CAACG,SAAS;cAAAiG,QAAA,EAAEjF,YAAY,CAACY;YAAW;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC,EAEnB/B,YAAY,CAACe,YAAY,iBACxBlC,OAAA,CAACjB,YAAY,CAACgI,IAAI;YAAC2B,KAAK,EAAC,0BAAM;YAACjC,IAAI,EAAE,CAAE;YAAAL,QAAA,eACtCpG,OAAA,CAACG,SAAS;cAAAiG,QAAA,EAAEjF,YAAY,CAACe;YAAY;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CACpB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eAEflD,OAAA,CAACpB,GAAG;UAAC0H,MAAM,EAAE,EAAG;UAACC,KAAK,EAAE;YAAEO,SAAS,EAAE;UAAG,CAAE;UAAAV,QAAA,gBACxCpG,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,EAAG;YAAAL,QAAA,eACZpG,OAAA,CAAClC,IAAI;cAAC0D,KAAK,EAAC,0BAAM;cAACyF,IAAI,EAAC,OAAO;cAAAb,QAAA,EAC5BjF,YAAY,CAACa,YAAY,IAAIb,YAAY,CAACa,YAAY,CAAC+D,MAAM,GAAG,CAAC,gBAChE/F,OAAA,CAAC7B,KAAK;gBAACyJ,IAAI;gBAAAxB,QAAA,EACRjF,YAAY,CAACa,YAAY,CAACyD,GAAG,CAAC,CAACoC,WAAW,EAAEC,KAAK,kBAChD9H,OAAA,CAAC5B,GAAG;kBAAawE,KAAK,EAAC,MAAM;kBAAAwD,QAAA,EAAEyB;gBAAW,GAAhCC,KAAK;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAiC,CACjD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,gBAERlD,OAAA,CAACE,IAAI;gBAACuB,IAAI,EAAC,WAAW;gBAAA2E,QAAA,EAAC;cAAM;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACpC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNlD,OAAA,CAACnB,GAAG;YAAC4H,IAAI,EAAE,EAAG;YAAAL,QAAA,eACZpG,OAAA,CAAClC,IAAI;cAAC0D,KAAK,EAAC,0BAAM;cAACyF,IAAI,EAAC,OAAO;cAAAb,QAAA,EAC5BjF,YAAY,CAACgB,YAAY,IAAIhB,YAAY,CAACgB,YAAY,CAAC4D,MAAM,GAAG,CAAC,gBAChE/F,OAAA,CAAC7B,KAAK;gBAACyJ,IAAI;gBAAAxB,QAAA,EACRjF,YAAY,CAACgB,YAAY,CAACsD,GAAG,CAAC,CAAClC,IAAI,EAAEuE,KAAK,kBACzC9H,OAAA,CAAC5B,GAAG;kBAAawE,KAAK,EAAC,OAAO;kBAAAwD,QAAA,EAAE7C;gBAAI,GAA1BuE,KAAK;kBAAA/E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAA2B,CAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,gBAERlD,OAAA,CAACE,IAAI;gBAACuB,IAAI,EAAC,WAAW;gBAAA2E,QAAA,EAAC;cAAM;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YACpC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5C,EAAA,CAnpBIrC,QAAQ;EAAA,QACcL,SAAS,EAOpBU,IAAI,CAACgD,OAAO;AAAA;AAAAkI,EAAA,GARvBvL,QAAQ;AAqpBd,eAAeA,QAAQ;AAAC,IAAAuL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}