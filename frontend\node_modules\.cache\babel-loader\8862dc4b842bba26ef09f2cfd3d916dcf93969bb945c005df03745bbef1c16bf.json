{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\CharacterList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Avatar, Tooltip, Row, Col, Statistic, Descriptions, Upload, InputNumber } from 'antd';\nimport { PlusOutlined, UserOutlined, EditOutlined, DeleteOutlined, EyeOutlined, RobotOutlined, TeamOutlined, CrownOutlined, HeartOutlined, UploadOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst CharacterList = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const [characters, setCharacters] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingCharacter, setEditingCharacter] = useState(null);\n  const [viewingCharacter, setViewingCharacter] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟人物数据\n  const mockCharacters = [{\n    id: 1,\n    name: '林天',\n    role: 'protagonist',\n    gender: 'male',\n    age: 18,\n    cultivation: '筑基期',\n    faction: '青云宗',\n    appearance: '身材修长，剑眉星目，气质出尘',\n    personality: '坚毅果敢，重情重义，天赋异禀',\n    background: '出身平凡，因缘际会踏入修仙之路',\n    abilities: ['剑法精通', '灵力感知', '快速修炼'],\n    relationships: ['师父：云长老', '师兄：王峰'],\n    avatar: null,\n    importance: 'high',\n    status: 'active'\n  }, {\n    id: 2,\n    name: '苏雪儿',\n    role: 'supporting',\n    gender: 'female',\n    age: 17,\n    cultivation: '练气期',\n    faction: '青云宗',\n    appearance: '容貌绝美，肌肤如雪，气质清冷',\n    personality: '冰雪聪明，外冷内热，心地善良',\n    background: '宗门长老之女，从小修炼',\n    abilities: ['冰系法术', '炼丹术', '阵法'],\n    relationships: ['父亲：苏长老', '青梅竹马：林天'],\n    avatar: null,\n    importance: 'high',\n    status: 'active'\n  }, {\n    id: 3,\n    name: '魔君血煞',\n    role: 'antagonist',\n    gender: 'male',\n    age: 500,\n    cultivation: '元婴期',\n    faction: '血煞门',\n    appearance: '身材魁梧，面容狰狞，煞气逼人',\n    personality: '残忍嗜血，野心勃勃，实力强大',\n    background: '魔道巨擘，称霸一方',\n    abilities: ['血煞神功', '魔道秘术', '精神攻击'],\n    relationships: ['手下：血煞四将'],\n    avatar: null,\n    importance: 'medium',\n    status: 'active'\n  }];\n  useEffect(() => {\n    setCharacters(mockCharacters);\n  }, []);\n\n  // 角色类型配置\n  const roleConfig = {\n    protagonist: {\n      color: 'gold',\n      text: '主角',\n      icon: /*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 53\n      }, this)\n    },\n    supporting: {\n      color: 'blue',\n      text: '配角',\n      icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 52\n      }, this)\n    },\n    antagonist: {\n      color: 'red',\n      text: '反派',\n      icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 51\n      }, this)\n    },\n    minor: {\n      color: 'default',\n      text: '次要',\n      icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 50\n      }, this)\n    }\n  };\n\n  // 重要性配置\n  const importanceConfig = {\n    high: {\n      color: 'red',\n      text: '重要'\n    },\n    medium: {\n      color: 'orange',\n      text: '一般'\n    },\n    low: {\n      color: 'default',\n      text: '次要'\n    }\n  };\n\n  // 表格列配置\n  const columns = [{\n    title: '头像',\n    dataIndex: 'avatar',\n    key: 'avatar',\n    render: (avatar, record) => /*#__PURE__*/_jsxDEV(Avatar, {\n      size: 40,\n      src: avatar,\n      icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 17\n      }, this),\n      style: {\n        backgroundColor: record.role === 'protagonist' ? '#f56a00' : '#87d068'\n      },\n      children: !avatar && record.name.charAt(0)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '姓名',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [roleConfig[record.role].icon, /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '角色类型',\n    dataIndex: 'role',\n    key: 'role',\n    render: role => /*#__PURE__*/_jsxDEV(Tag, {\n      color: roleConfig[role].color,\n      children: roleConfig[role].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 9\n    }, this),\n    filters: [{\n      text: '主角',\n      value: 'protagonist'\n    }, {\n      text: '配角',\n      value: 'supporting'\n    }, {\n      text: '反派',\n      value: 'antagonist'\n    }, {\n      text: '次要',\n      value: 'minor'\n    }],\n    onFilter: (value, record) => record.role === value\n  }, {\n    title: '性别',\n    dataIndex: 'gender',\n    key: 'gender',\n    render: gender => gender === 'male' ? '男' : '女'\n  }, {\n    title: '年龄',\n    dataIndex: 'age',\n    key: 'age',\n    sorter: (a, b) => a.age - b.age\n  }, {\n    title: '修为',\n    dataIndex: 'cultivation',\n    key: 'cultivation'\n  }, {\n    title: '势力',\n    dataIndex: 'faction',\n    key: 'faction'\n  }, {\n    title: '重要性',\n    dataIndex: 'importance',\n    key: 'importance',\n    render: importance => /*#__PURE__*/_jsxDEV(Tag, {\n      color: importanceConfig[importance].color,\n      children: importanceConfig[importance].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleView(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"AI\\u751F\\u6210\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAIGenerate(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u4EBA\\u7269\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理新建/编辑人物\n  const handleCreateOrEdit = () => {\n    setEditingCharacter(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = character => {\n    setEditingCharacter(character);\n    form.setFieldsValue({\n      ...character,\n      abilities: character.abilities.join('\\n'),\n      relationships: character.relationships.join('\\n')\n    });\n    setModalVisible(true);\n  };\n  const handleView = character => {\n    setViewingCharacter(character);\n    setDetailModalVisible(true);\n  };\n  const handleAIGenerate = character => {\n    message.info(`AI生成人物详情：${character.name}`);\n  };\n  const handleDelete = async id => {\n    try {\n      // 调用后端API删除数据\n      await axios.delete(`/api/project-data/projects/${projectId}/data/character/${id}`);\n\n      // 删除成功后从列表中移除\n      setCharacters(characters.filter(c => c.id !== id));\n      message.success('人物删除成功');\n    } catch (error) {\n      console.error('删除人物失败:', error);\n      message.error('删除人物失败');\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 处理数组字段\n      const processedValues = {\n        ...values,\n        abilities: values.abilities ? values.abilities.split('\\n').filter(item => item.trim()) : [],\n        relationships: values.relationships ? values.relationships.split('\\n').filter(item => item.trim()) : []\n      };\n      if (editingCharacter) {\n        // 编辑人物\n        setCharacters(characters.map(c => c.id === editingCharacter.id ? {\n          ...c,\n          ...processedValues\n        } : c));\n        message.success('人物更新成功');\n      } else {\n        // 新建人物\n        const newCharacter = {\n          id: Date.now(),\n          ...processedValues,\n          status: 'active'\n        };\n        setCharacters([...characters, newCharacter]);\n        message.success('人物创建成功');\n      }\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalCharacters = characters.length;\n  const protagonists = characters.filter(c => c.role === 'protagonist').length;\n  const antagonists = characters.filter(c => c.role === 'antagonist').length;\n  const supporting = characters.filter(c => c.role === 'supporting').length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u4EBA\\u7269\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4EBA\\u7269\\u6570\",\n            value: totalCharacters,\n            prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4E3B\\u89D2\",\n            value: protagonists,\n            prefix: /*#__PURE__*/_jsxDEV(CrownOutlined, {\n              style: {\n                color: '#faad14'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 352,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u914D\\u89D2\",\n            value: supporting,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u53CD\\u6D3E\",\n            value: antagonists,\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {\n              style: {\n                color: '#f5222d'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-left\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 21\n            }, this),\n            onClick: handleCreateOrEdit,\n            children: \"\\u6DFB\\u52A0\\u4EBA\\u7269\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: characters,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个人物`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 394,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingCharacter ? '编辑人物' : '新建人物',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => {\n        setModalVisible(false);\n        form.resetFields();\n      },\n      confirmLoading: loading,\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          role: 'supporting',\n          gender: 'male',\n          importance: 'medium',\n          status: 'active'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u59D3\\u540D\",\n              rules: [{\n                required: true,\n                message: '请输入人物姓名'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EBA\\u7269\\u59D3\\u540D\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"role\",\n              label: \"\\u89D2\\u8272\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择角色类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"protagonist\",\n                  children: \"\\u4E3B\\u89D2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 446,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"supporting\",\n                  children: \"\\u914D\\u89D2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"antagonist\",\n                  children: \"\\u53CD\\u6D3E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"minor\",\n                  children: \"\\u6B21\\u8981\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 449,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"gender\",\n              label: \"\\u6027\\u522B\",\n              rules: [{\n                required: true,\n                message: '请选择性别'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"male\",\n                  children: \"\\u7537\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"female\",\n                  children: \"\\u5973\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"age\",\n              label: \"\\u5E74\\u9F84\",\n              rules: [{\n                required: true,\n                message: '请输入年龄'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 10000,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"importance\",\n              label: \"\\u91CD\\u8981\\u6027\",\n              rules: [{\n                required: true,\n                message: '请选择重要性'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"high\",\n                  children: \"\\u91CD\\u8981\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 484,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"medium\",\n                  children: \"\\u4E00\\u822C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 485,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"low\",\n                  children: \"\\u6B21\\u8981\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 486,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"cultivation\",\n              label: \"\\u4FEE\\u4E3A\\u5883\\u754C\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u5982\\uFF1A\\u7B51\\u57FA\\u671F\\u3001\\u91D1\\u4E39\\u671F\\u7B49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"faction\",\n              label: \"\\u6240\\u5C5E\\u52BF\\u529B\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u5982\\uFF1A\\u9752\\u4E91\\u5B97\\u3001\\u9B54\\u9053\\u95E8\\u6D3E\\u7B49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 506,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"appearance\",\n          label: \"\\u5916\\u8C8C\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u63CF\\u8FF0\\u4EBA\\u7269\\u7684\\u5916\\u8C8C\\u7279\\u5F81...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 511,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"personality\",\n          label: \"\\u6027\\u683C\\u7279\\u70B9\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u63CF\\u8FF0\\u4EBA\\u7269\\u7684\\u6027\\u683C\\u7279\\u5F81...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"background\",\n          label: \"\\u80CC\\u666F\\u6545\\u4E8B\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u63CF\\u8FF0\\u4EBA\\u7269\\u7684\\u80CC\\u666F\\u7ECF\\u5386...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"abilities\",\n          label: \"\\u80FD\\u529B\\u6280\\u80FD\",\n          extra: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u6280\\u80FD\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u5982\\uFF1A\\u5251\\u6CD5\\u7CBE\\u901A\\n\\u7075\\u529B\\u611F\\u77E5\\n\\u5FEB\\u901F\\u4FEE\\u70BC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"relationships\",\n          label: \"\\u4EBA\\u9645\\u5173\\u7CFB\",\n          extra: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u5173\\u7CFB\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u5982\\uFF1A\\u5E08\\u7236\\uFF1A\\u4E91\\u957F\\u8001\\n\\u5E08\\u5144\\uFF1A\\u738B\\u5CF0\\n\\u9752\\u6885\\u7AF9\\u9A6C\\uFF1A\\u82CF\\u96EA\\u513F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u4EBA\\u7269\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 571,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: viewingCharacter && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 4,\n            children: /*#__PURE__*/_jsxDEV(Avatar, {\n              size: 80,\n              src: viewingCharacter.avatar,\n              icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 584,\n                columnNumber: 25\n              }, this),\n              style: {\n                backgroundColor: viewingCharacter.role === 'protagonist' ? '#f56a00' : '#87d068'\n              },\n              children: !viewingCharacter.avatar && viewingCharacter.name.charAt(0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 20,\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 3,\n              children: viewingCharacter.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 591,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Tag, {\n                color: roleConfig[viewingCharacter.role].color,\n                children: roleConfig[viewingCharacter.role].text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 593,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: importanceConfig[viewingCharacter.importance].color,\n                children: importanceConfig[viewingCharacter.importance].text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 592,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions, {\n          bordered: true,\n          column: 2,\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u6027\\u522B\",\n            children: viewingCharacter.gender === 'male' ? '男' : '女'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5E74\\u9F84\",\n            children: [viewingCharacter.age, \"\\u5C81\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u4FEE\\u4E3A\\u5883\\u754C\",\n            children: viewingCharacter.cultivation || '未知'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u6240\\u5C5E\\u52BF\\u529B\",\n            children: viewingCharacter.faction || '无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5916\\u8C8C\\u63CF\\u8FF0\",\n            span: 2,\n            children: viewingCharacter.appearance || '暂无描述'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u6027\\u683C\\u7279\\u70B9\",\n            span: 2,\n            children: viewingCharacter.personality || '暂无描述'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u80CC\\u666F\\u6545\\u4E8B\",\n            span: 2,\n            children: viewingCharacter.background || '暂无描述'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 622,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u80FD\\u529B\\u6280\\u80FD\",\n            span: 2,\n            children: viewingCharacter.abilities && viewingCharacter.abilities.length > 0 ? /*#__PURE__*/_jsxDEV(Space, {\n              wrap: true,\n              children: viewingCharacter.abilities.map((ability, index) => /*#__PURE__*/_jsxDEV(Tag, {\n                color: \"blue\",\n                children: ability\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 627,\n              columnNumber: 19\n            }, this) : '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u4EBA\\u9645\\u5173\\u7CFB\",\n            span: 2,\n            children: viewingCharacter.relationships && viewingCharacter.relationships.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n              children: viewingCharacter.relationships.map((relation, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: 4\n                },\n                children: [/*#__PURE__*/_jsxDEV(HeartOutlined, {\n                  style: {\n                    marginRight: 8,\n                    color: '#f5222d'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 25\n                }, this), relation]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 638,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 19\n            }, this) : '暂无'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 578,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 336,\n    columnNumber: 5\n  }, this);\n};\n_s(CharacterList, \"fo9ibcKkJdRgeHpd/VtAxg83pYE=\", false, function () {\n  return [useParams, Form.useForm];\n});\n_c = CharacterList;\nexport default CharacterList;\nvar _c;\n$RefreshReg$(_c, \"CharacterList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "axios", "Card", "Typography", "<PERSON><PERSON>", "Table", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "Avatar", "<PERSON><PERSON><PERSON>", "Row", "Col", "Statistic", "Descriptions", "Upload", "InputNumber", "PlusOutlined", "UserOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "RobotOutlined", "TeamOutlined", "CrownOutlined", "HeartOutlined", "UploadOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "TextArea", "Option", "CharacterList", "_s", "id", "projectId", "characters", "setChara<PERSON><PERSON>", "loading", "setLoading", "modalVisible", "setModalVisible", "detailModalVisible", "setDetailModalVisible", "editingCharacter", "setEditingCharacter", "viewingCharacter", "setViewingCharacter", "form", "useForm", "mockCharacters", "name", "role", "gender", "age", "cultivation", "faction", "appearance", "personality", "background", "abilities", "relationships", "avatar", "importance", "status", "roleConfig", "protagonist", "color", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "supporting", "antagonist", "minor", "importanceConfig", "high", "medium", "low", "columns", "title", "dataIndex", "key", "render", "record", "size", "src", "style", "backgroundColor", "children", "char<PERSON>t", "strong", "filters", "value", "onFilter", "sorter", "a", "b", "_", "type", "onClick", "handleView", "handleEdit", "handleAIGenerate", "onConfirm", "handleDelete", "okText", "cancelText", "danger", "handleCreateOrEdit", "resetFields", "character", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "info", "delete", "filter", "c", "success", "error", "console", "handleModalOk", "values", "validateFields", "processedValues", "split", "item", "trim", "map", "newCharacter", "Date", "now", "totalCharacters", "length", "protagonists", "antagonists", "className", "level", "gutter", "marginBottom", "span", "prefix", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onOk", "onCancel", "confirmLoading", "width", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "min", "max", "rows", "extra", "footer", "bordered", "column", "wrap", "ability", "index", "relation", "marginRight", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/CharacterList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  Card,\n  Typography,\n  Button,\n  Table,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Avatar,\n  Tooltip,\n  Row,\n  Col,\n  Statistic,\n  Descriptions,\n  Upload,\n  InputNumber\n} from 'antd';\nimport {\n  PlusOutlined,\n  UserOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  RobotOutlined,\n  TeamOutlined,\n  CrownOutlined,\n  HeartOutlined,\n  UploadOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst CharacterList = () => {\n  const { id: projectId } = useParams();\n  const [characters, setCharacters] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editing<PERSON>haracter, setEditingCharacter] = useState(null);\n  const [viewingCharacter, setViewingCharacter] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟人物数据\n  const mockCharacters = [\n    {\n      id: 1,\n      name: '林天',\n      role: 'protagonist',\n      gender: 'male',\n      age: 18,\n      cultivation: '筑基期',\n      faction: '青云宗',\n      appearance: '身材修长，剑眉星目，气质出尘',\n      personality: '坚毅果敢，重情重义，天赋异禀',\n      background: '出身平凡，因缘际会踏入修仙之路',\n      abilities: ['剑法精通', '灵力感知', '快速修炼'],\n      relationships: ['师父：云长老', '师兄：王峰'],\n      avatar: null,\n      importance: 'high',\n      status: 'active'\n    },\n    {\n      id: 2,\n      name: '苏雪儿',\n      role: 'supporting',\n      gender: 'female',\n      age: 17,\n      cultivation: '练气期',\n      faction: '青云宗',\n      appearance: '容貌绝美，肌肤如雪，气质清冷',\n      personality: '冰雪聪明，外冷内热，心地善良',\n      background: '宗门长老之女，从小修炼',\n      abilities: ['冰系法术', '炼丹术', '阵法'],\n      relationships: ['父亲：苏长老', '青梅竹马：林天'],\n      avatar: null,\n      importance: 'high',\n      status: 'active'\n    },\n    {\n      id: 3,\n      name: '魔君血煞',\n      role: 'antagonist',\n      gender: 'male',\n      age: 500,\n      cultivation: '元婴期',\n      faction: '血煞门',\n      appearance: '身材魁梧，面容狰狞，煞气逼人',\n      personality: '残忍嗜血，野心勃勃，实力强大',\n      background: '魔道巨擘，称霸一方',\n      abilities: ['血煞神功', '魔道秘术', '精神攻击'],\n      relationships: ['手下：血煞四将'],\n      avatar: null,\n      importance: 'medium',\n      status: 'active'\n    }\n  ];\n\n  useEffect(() => {\n    setCharacters(mockCharacters);\n  }, []);\n\n  // 角色类型配置\n  const roleConfig = {\n    protagonist: { color: 'gold', text: '主角', icon: <CrownOutlined /> },\n    supporting: { color: 'blue', text: '配角', icon: <UserOutlined /> },\n    antagonist: { color: 'red', text: '反派', icon: <UserOutlined /> },\n    minor: { color: 'default', text: '次要', icon: <UserOutlined /> }\n  };\n\n  // 重要性配置\n  const importanceConfig = {\n    high: { color: 'red', text: '重要' },\n    medium: { color: 'orange', text: '一般' },\n    low: { color: 'default', text: '次要' }\n  };\n\n  // 表格列配置\n  const columns = [\n    {\n      title: '头像',\n      dataIndex: 'avatar',\n      key: 'avatar',\n      render: (avatar, record) => (\n        <Avatar\n          size={40}\n          src={avatar}\n          icon={<UserOutlined />}\n          style={{ backgroundColor: record.role === 'protagonist' ? '#f56a00' : '#87d068' }}\n        >\n          {!avatar && record.name.charAt(0)}\n        </Avatar>\n      )\n    },\n    {\n      title: '姓名',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          {roleConfig[record.role].icon}\n          <Text strong>{text}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '角色类型',\n      dataIndex: 'role',\n      key: 'role',\n      render: (role) => (\n        <Tag color={roleConfig[role].color}>\n          {roleConfig[role].text}\n        </Tag>\n      ),\n      filters: [\n        { text: '主角', value: 'protagonist' },\n        { text: '配角', value: 'supporting' },\n        { text: '反派', value: 'antagonist' },\n        { text: '次要', value: 'minor' }\n      ],\n      onFilter: (value, record) => record.role === value\n    },\n    {\n      title: '性别',\n      dataIndex: 'gender',\n      key: 'gender',\n      render: (gender) => gender === 'male' ? '男' : '女'\n    },\n    {\n      title: '年龄',\n      dataIndex: 'age',\n      key: 'age',\n      sorter: (a, b) => a.age - b.age\n    },\n    {\n      title: '修为',\n      dataIndex: 'cultivation',\n      key: 'cultivation'\n    },\n    {\n      title: '势力',\n      dataIndex: 'faction',\n      key: 'faction'\n    },\n    {\n      title: '重要性',\n      dataIndex: 'importance',\n      key: 'importance',\n      render: (importance) => (\n        <Tag color={importanceConfig[importance].color}>\n          {importanceConfig[importance].text}\n        </Tag>\n      )\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"AI生成\">\n            <Button\n              type=\"text\"\n              icon={<RobotOutlined />}\n              onClick={() => handleAIGenerate(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个人物吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  // 处理新建/编辑人物\n  const handleCreateOrEdit = () => {\n    setEditingCharacter(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (character) => {\n    setEditingCharacter(character);\n    form.setFieldsValue({\n      ...character,\n      abilities: character.abilities.join('\\n'),\n      relationships: character.relationships.join('\\n')\n    });\n    setModalVisible(true);\n  };\n\n  const handleView = (character) => {\n    setViewingCharacter(character);\n    setDetailModalVisible(true);\n  };\n\n  const handleAIGenerate = (character) => {\n    message.info(`AI生成人物详情：${character.name}`);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 调用后端API删除数据\n      await axios.delete(`/api/project-data/projects/${projectId}/data/character/${id}`);\n\n      // 删除成功后从列表中移除\n      setCharacters(characters.filter(c => c.id !== id));\n      message.success('人物删除成功');\n    } catch (error) {\n      console.error('删除人物失败:', error);\n      message.error('删除人物失败');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 处理数组字段\n      const processedValues = {\n        ...values,\n        abilities: values.abilities ? values.abilities.split('\\n').filter(item => item.trim()) : [],\n        relationships: values.relationships ? values.relationships.split('\\n').filter(item => item.trim()) : []\n      };\n\n      if (editingCharacter) {\n        // 编辑人物\n        setCharacters(characters.map(c =>\n          c.id === editingCharacter.id\n            ? { ...c, ...processedValues }\n            : c\n        ));\n        message.success('人物更新成功');\n      } else {\n        // 新建人物\n        const newCharacter = {\n          id: Date.now(),\n          ...processedValues,\n          status: 'active'\n        };\n        setCharacters([...characters, newCharacter]);\n        message.success('人物创建成功');\n      }\n\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalCharacters = characters.length;\n  const protagonists = characters.filter(c => c.role === 'protagonist').length;\n  const antagonists = characters.filter(c => c.role === 'antagonist').length;\n  const supporting = characters.filter(c => c.role === 'supporting').length;\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">人物管理</Title>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总人物数\"\n              value={totalCharacters}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"主角\"\n              value={protagonists}\n              prefix={<CrownOutlined style={{ color: '#faad14' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"配角\"\n              value={supporting}\n              prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"反派\"\n              value={antagonists}\n              prefix={<UserOutlined style={{ color: '#f5222d' }} />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreateOrEdit}\n            >\n              添加人物\n            </Button>\n          </div>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={characters}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个人物`\n          }}\n        />\n      </Card>\n\n      {/* 新建/编辑人物模态框 */}\n      <Modal\n        title={editingCharacter ? '编辑人物' : '新建人物'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => {\n          setModalVisible(false);\n          form.resetFields();\n        }}\n        confirmLoading={loading}\n        width={800}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            role: 'supporting',\n            gender: 'male',\n            importance: 'medium',\n            status: 'active'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"姓名\"\n                rules={[{ required: true, message: '请输入人物姓名' }]}\n              >\n                <Input placeholder=\"请输入人物姓名\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"role\"\n                label=\"角色类型\"\n                rules={[{ required: true, message: '请选择角色类型' }]}\n              >\n                <Select>\n                  <Option value=\"protagonist\">主角</Option>\n                  <Option value=\"supporting\">配角</Option>\n                  <Option value=\"antagonist\">反派</Option>\n                  <Option value=\"minor\">次要</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"gender\"\n                label=\"性别\"\n                rules={[{ required: true, message: '请选择性别' }]}\n              >\n                <Select>\n                  <Option value=\"male\">男</Option>\n                  <Option value=\"female\">女</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"age\"\n                label=\"年龄\"\n                rules={[{ required: true, message: '请输入年龄' }]}\n              >\n                <InputNumber min={1} max={10000} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"importance\"\n                label=\"重要性\"\n                rules={[{ required: true, message: '请选择重要性' }]}\n              >\n                <Select>\n                  <Option value=\"high\">重要</Option>\n                  <Option value=\"medium\">一般</Option>\n                  <Option value=\"low\">次要</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"cultivation\"\n                label=\"修为境界\"\n              >\n                <Input placeholder=\"如：筑基期、金丹期等\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"faction\"\n                label=\"所属势力\"\n              >\n                <Input placeholder=\"如：青云宗、魔道门派等\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"appearance\"\n            label=\"外貌描述\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"描述人物的外貌特征...\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"personality\"\n            label=\"性格特点\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"描述人物的性格特征...\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"background\"\n            label=\"背景故事\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"描述人物的背景经历...\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"abilities\"\n            label=\"能力技能\"\n            extra=\"每行一个技能\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"如：剑法精通&#10;灵力感知&#10;快速修炼\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"relationships\"\n            label=\"人际关系\"\n            extra=\"每行一个关系\"\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"如：师父：云长老&#10;师兄：王峰&#10;青梅竹马：苏雪儿\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 人物详情模态框 */}\n      <Modal\n        title=\"人物详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {viewingCharacter && (\n          <div>\n            <Row gutter={16} style={{ marginBottom: 16 }}>\n              <Col span={4}>\n                <Avatar\n                  size={80}\n                  src={viewingCharacter.avatar}\n                  icon={<UserOutlined />}\n                  style={{ backgroundColor: viewingCharacter.role === 'protagonist' ? '#f56a00' : '#87d068' }}\n                >\n                  {!viewingCharacter.avatar && viewingCharacter.name.charAt(0)}\n                </Avatar>\n              </Col>\n              <Col span={20}>\n                <Title level={3}>{viewingCharacter.name}</Title>\n                <Space>\n                  <Tag color={roleConfig[viewingCharacter.role].color}>\n                    {roleConfig[viewingCharacter.role].text}\n                  </Tag>\n                  <Tag color={importanceConfig[viewingCharacter.importance].color}>\n                    {importanceConfig[viewingCharacter.importance].text}\n                  </Tag>\n                </Space>\n              </Col>\n            </Row>\n\n            <Descriptions bordered column={2}>\n              <Descriptions.Item label=\"性别\">\n                {viewingCharacter.gender === 'male' ? '男' : '女'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"年龄\">\n                {viewingCharacter.age}岁\n              </Descriptions.Item>\n              <Descriptions.Item label=\"修为境界\">\n                {viewingCharacter.cultivation || '未知'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"所属势力\">\n                {viewingCharacter.faction || '无'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"外貌描述\" span={2}>\n                {viewingCharacter.appearance || '暂无描述'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"性格特点\" span={2}>\n                {viewingCharacter.personality || '暂无描述'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"背景故事\" span={2}>\n                {viewingCharacter.background || '暂无描述'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"能力技能\" span={2}>\n                {viewingCharacter.abilities && viewingCharacter.abilities.length > 0 ? (\n                  <Space wrap>\n                    {viewingCharacter.abilities.map((ability, index) => (\n                      <Tag key={index} color=\"blue\">{ability}</Tag>\n                    ))}\n                  </Space>\n                ) : '暂无'}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"人际关系\" span={2}>\n                {viewingCharacter.relationships && viewingCharacter.relationships.length > 0 ? (\n                  <div>\n                    {viewingCharacter.relationships.map((relation, index) => (\n                      <div key={index} style={{ marginBottom: 4 }}>\n                        <HeartOutlined style={{ marginRight: 8, color: '#f5222d' }} />\n                        {relation}\n                      </div>\n                    ))}\n                  </div>\n                ) : '暂无'}\n              </Descriptions.Item>\n            </Descriptions>\n          </div>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default CharacterList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,YAAY,EACZC,MAAM,EACNC,WAAW,QACN,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,aAAa,EACbC,aAAa,EACbC,cAAc,QACT,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGjC,UAAU;AAC7C,MAAM;EAAEkC;AAAS,CAAC,GAAG3B,KAAK;AAC1B,MAAM;EAAE4B;AAAO,CAAC,GAAG3B,MAAM;AAEzB,MAAM4B,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAG1C,SAAS,CAAC,CAAC;EACrC,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACuD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACyD,IAAI,CAAC,GAAG9C,IAAI,CAAC+C,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,cAAc,GAAG,CACrB;IACEhB,EAAE,EAAE,CAAC;IACLiB,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,aAAa;IACnBC,MAAM,EAAE,MAAM;IACdC,GAAG,EAAE,EAAE;IACPC,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,gBAAgB;IAC5BC,WAAW,EAAE,gBAAgB;IAC7BC,UAAU,EAAE,iBAAiB;IAC7BC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACnCC,aAAa,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;IAClCC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE;EACV,CAAC,EACD;IACE9B,EAAE,EAAE,CAAC;IACLiB,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,QAAQ;IAChBC,GAAG,EAAE,EAAE;IACPC,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,gBAAgB;IAC5BC,WAAW,EAAE,gBAAgB;IAC7BC,UAAU,EAAE,aAAa;IACzBC,SAAS,EAAE,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;IAChCC,aAAa,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;IACpCC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE;EACV,CAAC,EACD;IACE9B,EAAE,EAAE,CAAC;IACLiB,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,YAAY;IAClBC,MAAM,EAAE,MAAM;IACdC,GAAG,EAAE,GAAG;IACRC,WAAW,EAAE,KAAK;IAClBC,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,gBAAgB;IAC5BC,WAAW,EAAE,gBAAgB;IAC7BC,UAAU,EAAE,WAAW;IACvBC,SAAS,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IACnCC,aAAa,EAAE,CAAC,SAAS,CAAC;IAC1BC,MAAM,EAAE,IAAI;IACZC,UAAU,EAAE,QAAQ;IACpBC,MAAM,EAAE;EACV,CAAC,CACF;EAEDxE,SAAS,CAAC,MAAM;IACd6C,aAAa,CAACa,cAAc,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMe,UAAU,GAAG;IACjBC,WAAW,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE3C,OAAA,CAACJ,aAAa;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACnEC,UAAU,EAAE;MAAEP,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE3C,OAAA,CAACV,YAAY;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACjEE,UAAU,EAAE;MAAER,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE3C,OAAA,CAACV,YAAY;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAChEG,KAAK,EAAE;MAAET,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE3C,OAAA,CAACV,YAAY;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;EAChE,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAG;IACvBC,IAAI,EAAE;MAAEX,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK,CAAC;IAClCW,MAAM,EAAE;MAAEZ,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK,CAAC;IACvCY,GAAG,EAAE;MAAEb,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK;EACtC,CAAC;;EAED;EACA,MAAMa,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACvB,MAAM,EAAEwB,MAAM,kBACrB5D,OAAA,CAACnB,MAAM;MACLgF,IAAI,EAAE,EAAG;MACTC,GAAG,EAAE1B,MAAO;MACZO,IAAI,eAAE3C,OAAA,CAACV,YAAY;QAAAsD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACvBgB,KAAK,EAAE;QAAEC,eAAe,EAAEJ,MAAM,CAAClC,IAAI,KAAK,aAAa,GAAG,SAAS,GAAG;MAAU,CAAE;MAAAuC,QAAA,EAEjF,CAAC7B,MAAM,IAAIwB,MAAM,CAACnC,IAAI,CAACyC,MAAM,CAAC,CAAC;IAAC;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B;EAEZ,CAAC,EACD;IACES,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACjB,IAAI,EAAEkB,MAAM,kBACnB5D,OAAA,CAAC3B,KAAK;MAAA4F,QAAA,GACH1B,UAAU,CAACqB,MAAM,CAAClC,IAAI,CAAC,CAACiB,IAAI,eAC7B3C,OAAA,CAACE,IAAI;QAACiE,MAAM;QAAAF,QAAA,EAAEvB;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAEX,CAAC,EACD;IACES,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGjC,IAAI,iBACX1B,OAAA,CAAC1B,GAAG;MAACmE,KAAK,EAAEF,UAAU,CAACb,IAAI,CAAC,CAACe,KAAM;MAAAwB,QAAA,EAChC1B,UAAU,CAACb,IAAI,CAAC,CAACgB;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACN;IACDqB,OAAO,EAAE,CACP;MAAE1B,IAAI,EAAE,IAAI;MAAE2B,KAAK,EAAE;IAAc,CAAC,EACpC;MAAE3B,IAAI,EAAE,IAAI;MAAE2B,KAAK,EAAE;IAAa,CAAC,EACnC;MAAE3B,IAAI,EAAE,IAAI;MAAE2B,KAAK,EAAE;IAAa,CAAC,EACnC;MAAE3B,IAAI,EAAE,IAAI;MAAE2B,KAAK,EAAE;IAAQ,CAAC,CAC/B;IACDC,QAAQ,EAAEA,CAACD,KAAK,EAAET,MAAM,KAAKA,MAAM,CAAClC,IAAI,KAAK2C;EAC/C,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGhC,MAAM,IAAKA,MAAM,KAAK,MAAM,GAAG,GAAG,GAAG;EAChD,CAAC,EACD;IACE6B,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,KAAK;IAChBC,GAAG,EAAE,KAAK;IACVa,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5C,GAAG,GAAG6C,CAAC,CAAC7C;EAC9B,CAAC,EACD;IACE4B,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAGtB,UAAU,iBACjBrC,OAAA,CAAC1B,GAAG;MAACmE,KAAK,EAAEU,gBAAgB,CAACd,UAAU,CAAC,CAACI,KAAM;MAAAwB,QAAA,EAC5Cd,gBAAgB,CAACd,UAAU,CAAC,CAACK;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B;EAET,CAAC,EACD;IACES,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACe,CAAC,EAAEd,MAAM,kBAChB5D,OAAA,CAAC3B,KAAK;MAAA4F,QAAA,gBACJjE,OAAA,CAAClB,OAAO;QAAC0E,KAAK,EAAC,0BAAM;QAAAS,QAAA,eACnBjE,OAAA,CAAC7B,MAAM;UACLwG,IAAI,EAAC,MAAM;UACXhC,IAAI,eAAE3C,OAAA,CAACP,WAAW;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtB6B,OAAO,EAAEA,CAAA,KAAMC,UAAU,CAACjB,MAAM;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV/C,OAAA,CAAClB,OAAO;QAAC0E,KAAK,EAAC,cAAI;QAAAS,QAAA,eACjBjE,OAAA,CAAC7B,MAAM;UACLwG,IAAI,EAAC,MAAM;UACXhC,IAAI,eAAE3C,OAAA,CAACT,YAAY;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB6B,OAAO,EAAEA,CAAA,KAAME,UAAU,CAAClB,MAAM;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV/C,OAAA,CAAClB,OAAO;QAAC0E,KAAK,EAAC,gBAAM;QAAAS,QAAA,eACnBjE,OAAA,CAAC7B,MAAM;UACLwG,IAAI,EAAC,MAAM;UACXhC,IAAI,eAAE3C,OAAA,CAACN,aAAa;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxB6B,OAAO,EAAEA,CAAA,KAAMG,gBAAgB,CAACnB,MAAM;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV/C,OAAA,CAACpB,UAAU;QACT4E,KAAK,EAAC,8DAAY;QAClBwB,SAAS,EAAEA,CAAA,KAAMC,YAAY,CAACrB,MAAM,CAACpD,EAAE,CAAE;QACzC0E,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAlB,QAAA,eAEfjE,OAAA,CAAClB,OAAO;UAAC0E,KAAK,EAAC,cAAI;UAAAS,QAAA,eACjBjE,OAAA,CAAC7B,MAAM;YACLwG,IAAI,EAAC,MAAM;YACXS,MAAM;YACNzC,IAAI,eAAE3C,OAAA,CAACR,cAAc;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BlE,mBAAmB,CAAC,IAAI,CAAC;IACzBG,IAAI,CAACgE,WAAW,CAAC,CAAC;IAClBvE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM+D,UAAU,GAAIS,SAAS,IAAK;IAChCpE,mBAAmB,CAACoE,SAAS,CAAC;IAC9BjE,IAAI,CAACkE,cAAc,CAAC;MAClB,GAAGD,SAAS;MACZrD,SAAS,EAAEqD,SAAS,CAACrD,SAAS,CAACuD,IAAI,CAAC,IAAI,CAAC;MACzCtD,aAAa,EAAEoD,SAAS,CAACpD,aAAa,CAACsD,IAAI,CAAC,IAAI;IAClD,CAAC,CAAC;IACF1E,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM8D,UAAU,GAAIU,SAAS,IAAK;IAChClE,mBAAmB,CAACkE,SAAS,CAAC;IAC9BtE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAM8D,gBAAgB,GAAIQ,SAAS,IAAK;IACtC5G,OAAO,CAAC+G,IAAI,CAAC,YAAYH,SAAS,CAAC9D,IAAI,EAAE,CAAC;EAC5C,CAAC;EAED,MAAMwD,YAAY,GAAG,MAAOzE,EAAE,IAAK;IACjC,IAAI;MACF;MACA,MAAMxC,KAAK,CAAC2H,MAAM,CAAC,8BAA8BlF,SAAS,mBAAmBD,EAAE,EAAE,CAAC;;MAElF;MACAG,aAAa,CAACD,UAAU,CAACkF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACrF,EAAE,KAAKA,EAAE,CAAC,CAAC;MAClD7B,OAAO,CAACmH,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpH,OAAO,CAACoH,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM5E,IAAI,CAAC6E,cAAc,CAAC,CAAC;MAC1CtF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMuF,eAAe,GAAG;QACtB,GAAGF,MAAM;QACThE,SAAS,EAAEgE,MAAM,CAAChE,SAAS,GAAGgE,MAAM,CAAChE,SAAS,CAACmE,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAC3FpE,aAAa,EAAE+D,MAAM,CAAC/D,aAAa,GAAG+D,MAAM,CAAC/D,aAAa,CAACkE,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG;MACvG,CAAC;MAED,IAAIrF,gBAAgB,EAAE;QACpB;QACAP,aAAa,CAACD,UAAU,CAAC8F,GAAG,CAACX,CAAC,IAC5BA,CAAC,CAACrF,EAAE,KAAKU,gBAAgB,CAACV,EAAE,GACxB;UAAE,GAAGqF,CAAC;UAAE,GAAGO;QAAgB,CAAC,GAC5BP,CACN,CAAC,CAAC;QACFlH,OAAO,CAACmH,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMW,YAAY,GAAG;UACnBjG,EAAE,EAAEkG,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGP,eAAe;UAClB9D,MAAM,EAAE;QACV,CAAC;QACD3B,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE+F,YAAY,CAAC,CAAC;QAC5C9H,OAAO,CAACmH,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEA/E,eAAe,CAAC,KAAK,CAAC;MACtBO,IAAI,CAACgE,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRlF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM+F,eAAe,GAAGlG,UAAU,CAACmG,MAAM;EACzC,MAAMC,YAAY,GAAGpG,UAAU,CAACkF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnE,IAAI,KAAK,aAAa,CAAC,CAACmF,MAAM;EAC5E,MAAME,WAAW,GAAGrG,UAAU,CAACkF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnE,IAAI,KAAK,YAAY,CAAC,CAACmF,MAAM;EAC1E,MAAM7D,UAAU,GAAGtC,UAAU,CAACkF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnE,IAAI,KAAK,YAAY,CAAC,CAACmF,MAAM;EAEzE,oBACE7G,OAAA;IAAKgH,SAAS,EAAC,SAAS;IAAA/C,QAAA,gBACtBjE,OAAA;MAAKgH,SAAS,EAAC,aAAa;MAAA/C,QAAA,eAC1BjE,OAAA,CAACC,KAAK;QAACgH,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAA/C,QAAA,EAAC;MAAI;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGN/C,OAAA,CAACjB,GAAG;MAACmI,MAAM,EAAE,EAAG;MAACnD,KAAK,EAAE;QAAEoD,YAAY,EAAE;MAAG,CAAE;MAAAlD,QAAA,gBAC3CjE,OAAA,CAAChB,GAAG;QAACoI,IAAI,EAAE,CAAE;QAAAnD,QAAA,eACXjE,OAAA,CAAC/B,IAAI;UAAAgG,QAAA,eACHjE,OAAA,CAACf,SAAS;YACRuE,KAAK,EAAC,0BAAM;YACZa,KAAK,EAAEuC,eAAgB;YACvBS,MAAM,eAAErH,OAAA,CAACL,YAAY;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/C,OAAA,CAAChB,GAAG;QAACoI,IAAI,EAAE,CAAE;QAAAnD,QAAA,eACXjE,OAAA,CAAC/B,IAAI;UAAAgG,QAAA,eACHjE,OAAA,CAACf,SAAS;YACRuE,KAAK,EAAC,cAAI;YACVa,KAAK,EAAEyC,YAAa;YACpBO,MAAM,eAAErH,OAAA,CAACJ,aAAa;cAACmE,KAAK,EAAE;gBAAEtB,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/C,OAAA,CAAChB,GAAG;QAACoI,IAAI,EAAE,CAAE;QAAAnD,QAAA,eACXjE,OAAA,CAAC/B,IAAI;UAAAgG,QAAA,eACHjE,OAAA,CAACf,SAAS;YACRuE,KAAK,EAAC,cAAI;YACVa,KAAK,EAAErB,UAAW;YAClBqE,MAAM,eAAErH,OAAA,CAACV,YAAY;cAACyE,KAAK,EAAE;gBAAEtB,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/C,OAAA,CAAChB,GAAG;QAACoI,IAAI,EAAE,CAAE;QAAAnD,QAAA,eACXjE,OAAA,CAAC/B,IAAI;UAAAgG,QAAA,eACHjE,OAAA,CAACf,SAAS;YACRuE,KAAK,EAAC,cAAI;YACVa,KAAK,EAAE0C,WAAY;YACnBM,MAAM,eAAErH,OAAA,CAACV,YAAY;cAACyE,KAAK,EAAE;gBAAEtB,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN/C,OAAA,CAAC/B,IAAI;MAAAgG,QAAA,gBACHjE,OAAA;QAAKgH,SAAS,EAAC,SAAS;QAAA/C,QAAA,eACtBjE,OAAA;UAAKgH,SAAS,EAAC,cAAc;UAAA/C,QAAA,eAC3BjE,OAAA,CAAC7B,MAAM;YACLwG,IAAI,EAAC,SAAS;YACdhC,IAAI,eAAE3C,OAAA,CAACX,YAAY;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB6B,OAAO,EAAES,kBAAmB;YAAApB,QAAA,EAC7B;UAED;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/C,OAAA,CAAC5B,KAAK;QACJmF,OAAO,EAAEA,OAAQ;QACjB+D,UAAU,EAAE5G,UAAW;QACvB6G,MAAM,EAAC,IAAI;QACX3G,OAAO,EAAEA,OAAQ;QACjB4G,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAhF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP/C,OAAA,CAACzB,KAAK;MACJiF,KAAK,EAAEtC,gBAAgB,GAAG,MAAM,GAAG,MAAO;MAC1C2G,IAAI,EAAE/G,YAAa;MACnBgH,IAAI,EAAE7B,aAAc;MACpB8B,QAAQ,EAAEA,CAAA,KAAM;QACdhH,eAAe,CAAC,KAAK,CAAC;QACtBO,IAAI,CAACgE,WAAW,CAAC,CAAC;MACpB,CAAE;MACF0C,cAAc,EAAEpH,OAAQ;MACxBqH,KAAK,EAAE,GAAI;MAAAhE,QAAA,eAEXjE,OAAA,CAACxB,IAAI;QACH8C,IAAI,EAAEA,IAAK;QACX4G,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACbzG,IAAI,EAAE,YAAY;UAClBC,MAAM,EAAE,MAAM;UACdU,UAAU,EAAE,QAAQ;UACpBC,MAAM,EAAE;QACV,CAAE;QAAA2B,QAAA,gBAEFjE,OAAA,CAACjB,GAAG;UAACmI,MAAM,EAAE,EAAG;UAAAjD,QAAA,gBACdjE,OAAA,CAAChB,GAAG;YAACoI,IAAI,EAAE,EAAG;YAAAnD,QAAA,eACZjE,OAAA,CAACxB,IAAI,CAAC4J,IAAI;cACR3G,IAAI,EAAC,MAAM;cACX4G,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsF,QAAA,eAEhDjE,OAAA,CAACvB,KAAK;gBAAC+J,WAAW,EAAC;cAAS;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/C,OAAA,CAAChB,GAAG;YAACoI,IAAI,EAAE,EAAG;YAAAnD,QAAA,eACZjE,OAAA,CAACxB,IAAI,CAAC4J,IAAI;cACR3G,IAAI,EAAC,MAAM;cACX4G,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5J,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsF,QAAA,eAEhDjE,OAAA,CAACtB,MAAM;gBAAAuF,QAAA,gBACLjE,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,aAAa;kBAAAJ,QAAA,EAAC;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvC/C,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,YAAY;kBAAAJ,QAAA,EAAC;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC/C,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,YAAY;kBAAAJ,QAAA,EAAC;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtC/C,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,OAAO;kBAAAJ,QAAA,EAAC;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA,CAACjB,GAAG;UAACmI,MAAM,EAAE,EAAG;UAAAjD,QAAA,gBACdjE,OAAA,CAAChB,GAAG;YAACoI,IAAI,EAAE,CAAE;YAAAnD,QAAA,eACXjE,OAAA,CAACxB,IAAI,CAAC4J,IAAI;cACR3G,IAAI,EAAC,QAAQ;cACb4G,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5J,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAsF,QAAA,eAE9CjE,OAAA,CAACtB,MAAM;gBAAAuF,QAAA,gBACLjE,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,MAAM;kBAAAJ,QAAA,EAAC;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/B/C,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,QAAQ;kBAAAJ,QAAA,EAAC;gBAAC;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/C,OAAA,CAAChB,GAAG;YAACoI,IAAI,EAAE,CAAE;YAAAnD,QAAA,eACXjE,OAAA,CAACxB,IAAI,CAAC4J,IAAI;cACR3G,IAAI,EAAC,KAAK;cACV4G,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5J,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAAsF,QAAA,eAE9CjE,OAAA,CAACZ,WAAW;gBAACqJ,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,KAAM;gBAAC3E,KAAK,EAAE;kBAAEkE,KAAK,EAAE;gBAAO;cAAE;gBAAArF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/C,OAAA,CAAChB,GAAG;YAACoI,IAAI,EAAE,CAAE;YAAAnD,QAAA,eACXjE,OAAA,CAACxB,IAAI,CAAC4J,IAAI;cACR3G,IAAI,EAAC,YAAY;cACjB4G,KAAK,EAAC,oBAAK;cACXC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5J,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAsF,QAAA,eAE/CjE,OAAA,CAACtB,MAAM;gBAAAuF,QAAA,gBACLjE,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,MAAM;kBAAAJ,QAAA,EAAC;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChC/C,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,QAAQ;kBAAAJ,QAAA,EAAC;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClC/C,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,KAAK;kBAAAJ,QAAA,EAAC;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA,CAACjB,GAAG;UAACmI,MAAM,EAAE,EAAG;UAAAjD,QAAA,gBACdjE,OAAA,CAAChB,GAAG;YAACoI,IAAI,EAAE,EAAG;YAAAnD,QAAA,eACZjE,OAAA,CAACxB,IAAI,CAAC4J,IAAI;cACR3G,IAAI,EAAC,aAAa;cAClB4G,KAAK,EAAC,0BAAM;cAAApE,QAAA,eAEZjE,OAAA,CAACvB,KAAK;gBAAC+J,WAAW,EAAC;cAAY;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/C,OAAA,CAAChB,GAAG;YAACoI,IAAI,EAAE,EAAG;YAAAnD,QAAA,eACZjE,OAAA,CAACxB,IAAI,CAAC4J,IAAI;cACR3G,IAAI,EAAC,SAAS;cACd4G,KAAK,EAAC,0BAAM;cAAApE,QAAA,eAEZjE,OAAA,CAACvB,KAAK;gBAAC+J,WAAW,EAAC;cAAa;gBAAA5F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA,CAACxB,IAAI,CAAC4J,IAAI;UACR3G,IAAI,EAAC,YAAY;UACjB4G,KAAK,EAAC,0BAAM;UAAApE,QAAA,eAEZjE,OAAA,CAACI,QAAQ;YACPuI,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAc;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/C,OAAA,CAACxB,IAAI,CAAC4J,IAAI;UACR3G,IAAI,EAAC,aAAa;UAClB4G,KAAK,EAAC,0BAAM;UAAApE,QAAA,eAEZjE,OAAA,CAACI,QAAQ;YACPuI,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAc;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/C,OAAA,CAACxB,IAAI,CAAC4J,IAAI;UACR3G,IAAI,EAAC,YAAY;UACjB4G,KAAK,EAAC,0BAAM;UAAApE,QAAA,eAEZjE,OAAA,CAACI,QAAQ;YACPuI,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAc;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/C,OAAA,CAACxB,IAAI,CAAC4J,IAAI;UACR3G,IAAI,EAAC,WAAW;UAChB4G,KAAK,EAAC,0BAAM;UACZO,KAAK,EAAC,sCAAQ;UAAA3E,QAAA,eAEdjE,OAAA,CAACI,QAAQ;YACPuI,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAA0B;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/C,OAAA,CAACxB,IAAI,CAAC4J,IAAI;UACR3G,IAAI,EAAC,eAAe;UACpB4G,KAAK,EAAC,0BAAM;UACZO,KAAK,EAAC,sCAAQ;UAAA3E,QAAA,eAEdjE,OAAA,CAACI,QAAQ;YACPuI,IAAI,EAAE,CAAE;YACRH,WAAW,EAAC;UAAiC;YAAA5F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR/C,OAAA,CAACzB,KAAK;MACJiF,KAAK,EAAC,0BAAM;MACZqE,IAAI,EAAE7G,kBAAmB;MACzB+G,QAAQ,EAAEA,CAAA,KAAM9G,qBAAqB,CAAC,KAAK,CAAE;MAC7C4H,MAAM,EAAE,cACN7I,OAAA,CAAC7B,MAAM;QAAayG,OAAO,EAAEA,CAAA,KAAM3D,qBAAqB,CAAC,KAAK,CAAE;QAAAgD,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFkF,KAAK,EAAE,GAAI;MAAAhE,QAAA,EAEV7C,gBAAgB,iBACfpB,OAAA;QAAAiE,QAAA,gBACEjE,OAAA,CAACjB,GAAG;UAACmI,MAAM,EAAE,EAAG;UAACnD,KAAK,EAAE;YAAEoD,YAAY,EAAE;UAAG,CAAE;UAAAlD,QAAA,gBAC3CjE,OAAA,CAAChB,GAAG;YAACoI,IAAI,EAAE,CAAE;YAAAnD,QAAA,eACXjE,OAAA,CAACnB,MAAM;cACLgF,IAAI,EAAE,EAAG;cACTC,GAAG,EAAE1C,gBAAgB,CAACgB,MAAO;cAC7BO,IAAI,eAAE3C,OAAA,CAACV,YAAY;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBgB,KAAK,EAAE;gBAAEC,eAAe,EAAE5C,gBAAgB,CAACM,IAAI,KAAK,aAAa,GAAG,SAAS,GAAG;cAAU,CAAE;cAAAuC,QAAA,EAE3F,CAAC7C,gBAAgB,CAACgB,MAAM,IAAIhB,gBAAgB,CAACK,IAAI,CAACyC,MAAM,CAAC,CAAC;YAAC;cAAAtB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eACN/C,OAAA,CAAChB,GAAG;YAACoI,IAAI,EAAE,EAAG;YAAAnD,QAAA,gBACZjE,OAAA,CAACC,KAAK;cAACgH,KAAK,EAAE,CAAE;cAAAhD,QAAA,EAAE7C,gBAAgB,CAACK;YAAI;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChD/C,OAAA,CAAC3B,KAAK;cAAA4F,QAAA,gBACJjE,OAAA,CAAC1B,GAAG;gBAACmE,KAAK,EAAEF,UAAU,CAACnB,gBAAgB,CAACM,IAAI,CAAC,CAACe,KAAM;gBAAAwB,QAAA,EACjD1B,UAAU,CAACnB,gBAAgB,CAACM,IAAI,CAAC,CAACgB;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACN/C,OAAA,CAAC1B,GAAG;gBAACmE,KAAK,EAAEU,gBAAgB,CAAC/B,gBAAgB,CAACiB,UAAU,CAAC,CAACI,KAAM;gBAAAwB,QAAA,EAC7Dd,gBAAgB,CAAC/B,gBAAgB,CAACiB,UAAU,CAAC,CAACK;cAAI;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/C,OAAA,CAACd,YAAY;UAAC4J,QAAQ;UAACC,MAAM,EAAE,CAAE;UAAA9E,QAAA,gBAC/BjE,OAAA,CAACd,YAAY,CAACkJ,IAAI;YAACC,KAAK,EAAC,cAAI;YAAApE,QAAA,EAC1B7C,gBAAgB,CAACO,MAAM,KAAK,MAAM,GAAG,GAAG,GAAG;UAAG;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC,eACpB/C,OAAA,CAACd,YAAY,CAACkJ,IAAI;YAACC,KAAK,EAAC,cAAI;YAAApE,QAAA,GAC1B7C,gBAAgB,CAACQ,GAAG,EAAC,QACxB;UAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAAC,eACpB/C,OAAA,CAACd,YAAY,CAACkJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAApE,QAAA,EAC5B7C,gBAAgB,CAACS,WAAW,IAAI;UAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACpB/C,OAAA,CAACd,YAAY,CAACkJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAApE,QAAA,EAC5B7C,gBAAgB,CAACU,OAAO,IAAI;UAAG;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACpB/C,OAAA,CAACd,YAAY,CAACkJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAACjB,IAAI,EAAE,CAAE;YAAAnD,QAAA,EACrC7C,gBAAgB,CAACW,UAAU,IAAI;UAAM;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACpB/C,OAAA,CAACd,YAAY,CAACkJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAACjB,IAAI,EAAE,CAAE;YAAAnD,QAAA,EACrC7C,gBAAgB,CAACY,WAAW,IAAI;UAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eACpB/C,OAAA,CAACd,YAAY,CAACkJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAACjB,IAAI,EAAE,CAAE;YAAAnD,QAAA,EACrC7C,gBAAgB,CAACa,UAAU,IAAI;UAAM;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACpB/C,OAAA,CAACd,YAAY,CAACkJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAACjB,IAAI,EAAE,CAAE;YAAAnD,QAAA,EACrC7C,gBAAgB,CAACc,SAAS,IAAId,gBAAgB,CAACc,SAAS,CAAC2E,MAAM,GAAG,CAAC,gBAClE7G,OAAA,CAAC3B,KAAK;cAAC2K,IAAI;cAAA/E,QAAA,EACR7C,gBAAgB,CAACc,SAAS,CAACsE,GAAG,CAAC,CAACyC,OAAO,EAAEC,KAAK,kBAC7ClJ,OAAA,CAAC1B,GAAG;gBAAamE,KAAK,EAAC,MAAM;gBAAAwB,QAAA,EAAEgF;cAAO,GAA5BC,KAAK;gBAAAtG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAA6B,CAC7C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC,GACN;UAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC,eACpB/C,OAAA,CAACd,YAAY,CAACkJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAACjB,IAAI,EAAE,CAAE;YAAAnD,QAAA,EACrC7C,gBAAgB,CAACe,aAAa,IAAIf,gBAAgB,CAACe,aAAa,CAAC0E,MAAM,GAAG,CAAC,gBAC1E7G,OAAA;cAAAiE,QAAA,EACG7C,gBAAgB,CAACe,aAAa,CAACqE,GAAG,CAAC,CAAC2C,QAAQ,EAAED,KAAK,kBAClDlJ,OAAA;gBAAiB+D,KAAK,EAAE;kBAAEoD,YAAY,EAAE;gBAAE,CAAE;gBAAAlD,QAAA,gBAC1CjE,OAAA,CAACH,aAAa;kBAACkE,KAAK,EAAE;oBAAEqF,WAAW,EAAE,CAAC;oBAAE3G,KAAK,EAAE;kBAAU;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EAC7DoG,QAAQ;cAAA,GAFDD,KAAK;gBAAAtG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAGV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,GACJ;UAAI;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACxC,EAAA,CAjmBID,aAAa;EAAA,QACSvC,SAAS,EAOpBS,IAAI,CAAC+C,OAAO;AAAA;AAAA8H,EAAA,GARvB/I,aAAa;AAmmBnB,eAAeA,aAAa;AAAC,IAAA+I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}