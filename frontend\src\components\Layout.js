import React, { useState } from 'react';
import { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  ProjectOutlined,
  UserOutlined,
  BookOutlined,
  FileTextOutlined,
  RobotOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  LogoutOutlined,
  BellOutlined
} from '@ant-design/icons';

const { Header, Sider, Content } = AntLayout;

const Layout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // 获取当前项目ID（如果在项目页面中）
  const getProjectId = () => {
    const pathParts = location.pathname.split('/');
    if (pathParts[1] === 'projects' && pathParts[2]) {
      return pathParts[2];
    }
    return null;
  };

  const projectId = getProjectId();

  // 基础菜单项
  const baseMenuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: '项目管理',
    },
  ];

  // 项目管理子菜单项
  const projectSubMenuItems = projectId ? [
    {
      key: `/projects/${projectId}/volumes`,
      icon: <FileTextOutlined />,
      label: '卷宗管理',
    },
    {
      key: `/projects/${projectId}/content`,
      icon: <BookOutlined />,
      label: '内容管理',
    },
    {
      key: `/projects/${projectId}/settings`,
      icon: <SettingOutlined />,
      label: '设定管理',
    },
  ] : [];



  // 工具菜单项
  const toolsMenuItems = [
    {
      key: '/ai-assistant',
      icon: <RobotOutlined />,
      label: 'Agent-AI助手',
    },
    {
      key: '/ai-test',
      icon: <RobotOutlined />,
      label: 'Agent-AI测试',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '偏好设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  const handleUserMenuClick = ({ key }) => {
    switch (key) {
      case 'profile':
        navigate('/profile');
        break;
      case 'settings':
        navigate('/settings');
        break;
      case 'logout':
        // 处理退出登录
        console.log('退出登录');
        break;
      default:
        break;
    }
  };

  return (
    <AntLayout className="layout-container">
      <Header className="layout-header">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ marginRight: 16 }}
            />
            <h1 style={{ margin: 0, color: '#1890ff', fontSize: '20px', fontWeight: 'bold' }}>
              NovelCraft
            </h1>
          </div>

          <Space>
            <Button type="text" icon={<BellOutlined />} />
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <span>用户</span>
              </Space>
            </Dropdown>
          </Space>
        </div>
      </Header>

      <AntLayout className="layout-content">
        <Sider
          className="layout-sider"
          collapsed={collapsed}
          width={240}
          collapsedWidth={80}
          theme="light"
        >
          <div style={{ height: '100%', overflowY: 'auto' }}>
            {/* 基础菜单 */}
            <Menu
              mode="inline"
              selectedKeys={[location.pathname]}
              items={baseMenuItems}
              onClick={handleMenuClick}
              style={{ borderRight: 0, marginBottom: 0 }}
            />

            {/* 项目管理子菜单 */}
            {projectId && (
              <Menu
                mode="inline"
                selectedKeys={[location.pathname]}
                items={projectSubMenuItems}
                onClick={handleMenuClick}
                style={{ borderRight: 0, marginBottom: 0 }}
              />
            )}

            {/* 工具菜单 */}
            <div className="tools-divider">
              <div className="tools-title">
                工具
              </div>
              <Menu
                mode="inline"
                selectedKeys={[location.pathname]}
                items={toolsMenuItems}
                onClick={handleMenuClick}
                style={{ borderRight: 0 }}
              />
            </div>
          </div>
        </Sider>

        <Content className="layout-main">
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
