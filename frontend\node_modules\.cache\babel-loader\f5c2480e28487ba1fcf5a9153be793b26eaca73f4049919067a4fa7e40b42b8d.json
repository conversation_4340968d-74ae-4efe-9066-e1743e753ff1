{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\ProjectDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, Tabs, Button, Space, Typography, Row, Col, Statistic, Progress, Tag, Descriptions, message, Collapse } from 'antd';\nimport { EditOutlined, SettingOutlined, ExportOutlined, BackwardOutlined, CaretRightOutlined, UserOutlined, GlobalOutlined, CrownOutlined, DollarOutlined, ThunderboltOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Panel\n} = Collapse;\nconst ProjectDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [project, setProject] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // 模拟项目数据\n  const mockProject = {\n    id: 1,\n    name: '仙侠传说',\n    title: '九天仙缘录',\n    author: '作者A',\n    type: 'xianxia',\n    status: 'writing',\n    summary: '这是一个关于修仙者在九天之上寻找仙缘的故事...',\n    description: '详细的项目描述...',\n    wordCount: 89000,\n    chapterCount: 45,\n    characterCount: 12,\n    factionCount: 8,\n    plotCount: 15,\n    progress: 65,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-15'\n  };\n  const loadProject = useCallback(async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setProject(mockProject);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      message.error('加载项目详情失败');\n      setLoading(false);\n    }\n  }, [mockProject]);\n  useEffect(() => {\n    loadProject();\n  }, [id, loadProject]);\n  const getStatusColor = status => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n  const getTypeText = type => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n  if (loading || !project) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: \"\\u52A0\\u8F7D\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(BackwardOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate('/projects'),\n          children: \"\\u8FD4\\u56DE\\u9879\\u76EE\\u5217\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          className: \"page-title\",\n          children: project.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: getStatusColor(project.status),\n          children: getStatusText(project.status)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          children: getTypeText(project.type)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        style: {\n          marginTop: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 40\n          }, this),\n          children: \"\\u7F16\\u8F91\\u9879\\u76EE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 25\n          }, this),\n          children: \"\\u9879\\u76EE\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5BFC\\u51FA\\u9879\\u76EE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5B57\\u6570\",\n            value: project.wordCount,\n            formatter: value => `${(value / 10000).toFixed(1)}万`,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7AE0\\u8282\\u6570\",\n            value: project.chapterCount,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4EBA\\u7269\\u6570\",\n            value: project.characterCount,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5B8C\\u6210\\u8FDB\\u5EA6\",\n            value: project.progress,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: project.progress,\n            size: \"small\",\n            status: project.progress === 100 ? 'success' : 'active',\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        defaultActiveKey: \"overview\",\n        children: [/*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u9879\\u76EE\\u6982\\u89C8\",\n          children: /*#__PURE__*/_jsxDEV(Descriptions, {\n            bordered: true,\n            column: 2,\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u9879\\u76EE\\u540D\\u79F0\",\n              children: project.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5C0F\\u8BF4\\u6807\\u9898\",\n              children: project.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u4F5C\\u8005\",\n              children: project.author\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u9879\\u76EE\\u7C7B\\u578B\",\n              children: getTypeText(project.type)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n              children: project.createdAt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u6700\\u540E\\u4FEE\\u6539\",\n              children: project.updatedAt\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u9879\\u76EE\\u7B80\\u4ECB\",\n              span: 2,\n              children: project.summary\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8BE6\\u7EC6\\u63CF\\u8FF0\",\n              span: 2,\n              children: project.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, \"overview\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u5377\\u5B97\\u7BA1\\u7406\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [16, 16],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u5377\\u5B97\\u7BA1\\u7406\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u7BA1\\u7406\\u5377\\u5B97\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 26\n                }, this),\n                onClick: () => navigate(`/projects/${id}/volumes`),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                  value: project.volumeCount || 0,\n                  suffix: \"\\u4E2A\\u5377\\u5B97\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u7BA1\\u7406\\u5C0F\\u8BF4\\u5377\\u5B97\\u7ED3\\u6784\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u7AE0\\u8282\\u7BA1\\u7406\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u7BA1\\u7406\\u7AE0\\u8282\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 26\n                }, this),\n                onClick: () => navigate(`/projects/${id}/volumes`),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                  value: project.chapterCount,\n                  suffix: \"\\u4E2A\\u7AE0\\u8282\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u7BA1\\u7406\\u7AE0\\u8282\\u5185\\u5BB9\\u548C\\u7ED3\\u6784\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u5199\\u4F5C\\u8FDB\\u5EA6\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u67E5\\u770B\\u8BE6\\u60C5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 26\n                }, this),\n                onClick: () => navigate(`/projects/${id}/volumes`),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                  value: project.progress,\n                  suffix: \"%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u6574\\u4F53\\u521B\\u4F5C\\u8FDB\\u5EA6\\u8DDF\\u8E2A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u72EC\\u7ACB\\u7AE0\\u8282\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 26\n                }, this),\n                onClick: () => navigate(`/projects/${id}/volumes`),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                  value: 0,\n                  suffix: \"\\u4E2A\\u72EC\\u7ACB\\u7AE0\\u8282\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u672A\\u5206\\u914D\\u5230\\u5377\\u5B97\\u7684\\u7AE0\\u8282\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"AI\\u8F85\\u52A9\\u5199\\u4F5C\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u4F7F\\u7528AI\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 26\n                }, this),\n                onClick: () => navigate('/ai-assistant'),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"AI\\u7EED\\u5199\\u4E0E\\u751F\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 270,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u667A\\u80FD\\u7AE0\\u8282\\u7EED\\u5199\\u548C\\u5927\\u7EB2\\u751F\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u5BFC\\u51FA\\u529F\\u80FD\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u5BFC\\u51FA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 26\n                }, this),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u5185\\u5BB9\\u5BFC\\u51FA\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 280,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u5BFC\\u51FA\\u5377\\u5B97\\u3001\\u7AE0\\u8282\\u5185\\u5BB9\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)\n        }, \"volumes\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u5185\\u5BB9\\u7BA1\\u7406\",\n          children: /*#__PURE__*/_jsxDEV(Collapse, {\n            defaultActiveKey: ['characters', 'distribution'],\n            expandIcon: ({\n              isActive\n            }) => /*#__PURE__*/_jsxDEV(CaretRightOutlined, {\n              rotate: isActive ? 90 : 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 45\n            }, this),\n            ghost: true,\n            children: [/*#__PURE__*/_jsxDEV(Panel, {\n              header: \"\\u89D2\\u8272\\u4E0E\\u52BF\\u529B\\u7BA1\\u7406\",\n              extra: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 63\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                gutter: [16, 16],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u4EBA\\u7269\\u7BA1\\u7406\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/characters`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                      value: project.characterCount,\n                      suffix: \"\\u4E2A\\u4EBA\\u7269\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 303,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u7BA1\\u7406\\u89D2\\u8272\\u6863\\u6848\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 297,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u52BF\\u529B\\u7BA1\\u7406\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/factions`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                      value: project.factionCount,\n                      suffix: \"\\u4E2A\\u52BF\\u529B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u7BA1\\u7406\\u52BF\\u529B\\u7EC4\\u7EC7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 308,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u5267\\u60C5\\u7BA1\\u7406\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/plots`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                      value: project.plotCount,\n                      suffix: \"\\u4E2A\\u5267\\u60C5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u7BA1\\u7406\\u5267\\u60C5\\u7EBF\\u7D22\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 326,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)\n            }, \"characters\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 294,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Panel, {\n              header: \"\\u4E16\\u754C\\u5206\\u5E03\\u7BA1\\u7406\",\n              extra: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 64\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                gutter: [16, 16],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u8D44\\u6E90\\u5206\\u5E03\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/resource-distribution`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                      value: 0,\n                      suffix: \"\\u4E2A\\u8D44\\u6E90\\u70B9\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 341,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u7BA1\\u7406\\u8D44\\u6E90\\u5206\\u5E03\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 335,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u79CD\\u65CF\\u5206\\u5E03\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/race-distribution`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                      value: 0,\n                      suffix: \"\\u4E2A\\u79CD\\u65CF\\u533A\\u57DF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 352,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u7BA1\\u7406\\u79CD\\u65CF\\u5206\\u5E03\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 353,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 346,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 345,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u79D8\\u5883\\u5206\\u5E03\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/secret-realm-distribution`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                      value: 0,\n                      suffix: \"\\u4E2A\\u79D8\\u5883\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u7BA1\\u7406\\u79D8\\u5883\\u5206\\u5E03\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 364,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 357,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 17\n              }, this)\n            }, \"distribution\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this)\n        }, \"content\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u8BBE\\u5B9A\\u7BA1\\u7406\",\n          children: /*#__PURE__*/_jsxDEV(Collapse, {\n            defaultActiveKey: ['world', 'power', 'society'],\n            expandIcon: ({\n              isActive\n            }) => /*#__PURE__*/_jsxDEV(CaretRightOutlined, {\n              rotate: isActive ? 90 : 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 45\n            }, this),\n            ghost: true,\n            children: [/*#__PURE__*/_jsxDEV(Panel, {\n              header: \"\\u4E16\\u754C\\u89C2\\u4E0E\\u57FA\\u7840\\u8BBE\\u5B9A\",\n              extra: /*#__PURE__*/_jsxDEV(GlobalOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 378,\n                columnNumber: 59\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                gutter: [16, 16],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u4E16\\u754C\\u8BBE\\u5B9A\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 383,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/world-settings`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u4E16\\u754C\\u89C2\\u8BBE\\u5B9A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 387,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 388,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u5730\\u7406\\u3001\\u5386\\u53F2\\u3001\\u6587\\u5316\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 381,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 380,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u5730\\u56FE\\u7ED3\\u6784\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 395,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/map-structures`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u5730\\u56FE\\u7ED3\\u6784\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 399,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 400,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u5730\\u56FE\\u3001\\u5730\\u5F62\\u3001\\u533A\\u57DF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 393,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u7EF4\\u5EA6\\u7ED3\\u6784\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 407,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/dimension-structures`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u7EF4\\u5EA6\\u7ED3\\u6784\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 411,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 412,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u7EF4\\u5EA6\\u3001\\u6CD5\\u5219\\u3001\\u4F20\\u9001\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 413,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 405,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u79CD\\u65CF\\u7C7B\\u522B\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/race-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u79CD\\u65CF\\u7C7B\\u522B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 423,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u79CD\\u65CF\\u3001\\u7279\\u6027\\u3001\\u5173\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 425,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 379,\n                columnNumber: 17\n              }, this)\n            }, \"world\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Panel, {\n              header: \"\\u529B\\u91CF\\u4E0E\\u6218\\u6597\\u4F53\\u7CFB\",\n              extra: /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 58\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                gutter: [16, 16],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u4FEE\\u70BC\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 436,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/cultivation-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u4FEE\\u70BC\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 440,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 441,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u80FD\\u529B\\u3001\\u7B49\\u7EA7\\u3001\\u65B9\\u6CD5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 442,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 434,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u529F\\u6CD5\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 448,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/martial-arts-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u529F\\u6CD5\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 453,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u529F\\u6CD5\\u3001\\u62DB\\u5F0F\\u3001\\u4F20\\u627F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 446,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u88C5\\u5907\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 460,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/equipment-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u88C5\\u5907\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 464,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 465,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u88C5\\u5907\\u3001\\u5F3A\\u5316\\u3001\\u5957\\u88C5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 466,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u7075\\u5B9D\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 472,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/spiritual-treasure-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u7075\\u5B9D\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 476,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 477,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u7075\\u5B9D\\u3001\\u5668\\u7075\\u3001\\u70BC\\u5236\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 478,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u5BA0\\u7269\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/pet-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u5BA0\\u7269\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 489,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u5BA0\\u7269\\u3001\\u8FDB\\u5316\\u3001\\u57F9\\u517B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 481,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this)\n            }, \"power\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Panel, {\n              header: \"\\u793E\\u4F1A\\u4E0E\\u653F\\u6CBB\\u4F53\\u7CFB\",\n              extra: /*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 496,\n                columnNumber: 60\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                gutter: [16, 16],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u653F\\u6CBB\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 501,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/political-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u653F\\u6CBB\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 505,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 506,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u653F\\u5E9C\\u3001\\u6CD5\\u5F8B\\u3001\\u6743\\u529B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 507,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u53F8\\u6CD5\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/judicial-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u53F8\\u6CD5\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 517,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 518,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u6CD5\\u9662\\u3001\\u6267\\u6CD5\\u3001\\u5BA1\\u5224\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u751F\\u6C11\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 525,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/civilian-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u751F\\u6C11\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 529,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u4EBA\\u53E3\\u3001\\u793E\\u4F1A\\u3001\\u751F\\u6D3B\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 523,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u804C\\u4E1A\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 537,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/profession-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u804C\\u4E1A\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 541,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 542,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u804C\\u4E1A\\u3001\\u6280\\u80FD\\u3001\\u7EC4\\u7EC7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 535,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 497,\n                columnNumber: 17\n              }, this)\n            }, \"society\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 496,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Panel, {\n              header: \"\\u7ECF\\u6D4E\\u4E0E\\u5546\\u4E1A\\u4F53\\u7CFB\",\n              extra: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 60\n              }, this),\n              children: /*#__PURE__*/_jsxDEV(Row, {\n                gutter: [16, 16],\n                children: [/*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u8D27\\u5E01\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/currency-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u8D27\\u5E01\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 558,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 559,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u8D27\\u5E01\\u3001\\u91D1\\u878D\\u3001\\u7ECF\\u6D4E\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 560,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 552,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Col, {\n                  xs: 24,\n                  sm: 12,\n                  md: 8,\n                  children: /*#__PURE__*/_jsxDEV(Card, {\n                    title: \"\\u5546\\u4E1A\\u4F53\\u7CFB\",\n                    extra: /*#__PURE__*/_jsxDEV(Button, {\n                      type: \"link\",\n                      children: \"\\u67E5\\u770B\\u5168\\u90E8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 30\n                    }, this),\n                    onClick: () => navigate(`/projects/${id}/commerce-systems`),\n                    style: {\n                      cursor: 'pointer'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Text, {\n                      children: \"\\u5546\\u4E1A\\u4F53\\u7CFB\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 570,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 571,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Text, {\n                      type: \"secondary\",\n                      children: \"\\u8D38\\u6613\\u3001\\u5546\\u4F1A\\u3001\\u5E02\\u573A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 572,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 564,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 563,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 17\n              }, this)\n            }, \"economy\", false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)\n        }, \"settings\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TabPane, {\n          tab: \"\\u5DE5\\u5177\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [16, 16],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u65F6\\u95F4\\u7EBF\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u67E5\\u770B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 26\n                }, this),\n                onClick: () => navigate(`/projects/${id}/timeline`),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u67E5\\u770B\\u9879\\u76EE\\u65F6\\u95F4\\u7EBF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u4E8B\\u4EF6\\u3001\\u53D1\\u5C55\\u3001\\u5386\\u53F2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u5173\\u7CFB\\u7F51\\u7EDC\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u67E5\\u770B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 26\n                }, this),\n                onClick: () => navigate(`/projects/${id}/relations`),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u4EBA\\u7269\\u5173\\u7CFB\\u56FE\\u8C31\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 601,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 602,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u4EBA\\u7269\\u3001\\u52BF\\u529B\\u3001\\u5173\\u7CFB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 603,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"AI\\u52A9\\u624B\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u4F7F\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 609,\n                  columnNumber: 26\n                }, this),\n                onClick: () => navigate('/ai-assistant'),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"AI\\u8F85\\u52A9\\u521B\\u4F5C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 613,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 614,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u667A\\u80FD\\u751F\\u6210\\u3001\\u7EED\\u5199\\u3001\\u5206\\u6790\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 615,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"AI\\u6D4B\\u8BD5\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u6D4B\\u8BD5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 621,\n                  columnNumber: 26\n                }, this),\n                onClick: () => navigate('/ai-test'),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"AI\\u529F\\u80FD\\u6D4B\\u8BD5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"\\u6D4B\\u8BD5AI\\u6A21\\u578B\\u529F\\u80FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 619,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                title: \"\\u7CFB\\u7EDF\\u8BBE\\u7F6E\",\n                extra: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  children: \"\\u8BBE\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 633,\n                  columnNumber: 26\n                }, this),\n                onClick: () => navigate('/settings'),\n                style: {\n                  cursor: 'pointer'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: \"\\u7CFB\\u7EDF\\u914D\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 637,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 638,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: \"AI\\u914D\\u7F6E\\u3001\\u7CFB\\u7EDF\\u8BBE\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 639,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this)\n        }, \"tools\", false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectDetail, \"9e4jNjzfeXeQ8QUEkcs089NjU1o=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ProjectDetail;\nexport default ProjectDetail;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "Card", "Tabs", "<PERSON><PERSON>", "Space", "Typography", "Row", "Col", "Statistic", "Progress", "Tag", "Descriptions", "message", "Collapse", "EditOutlined", "SettingOutlined", "ExportOutlined", "BackwardOutlined", "CaretRightOutlined", "UserOutlined", "GlobalOutlined", "CrownOutlined", "DollarOutlined", "ThunderboltOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TabPane", "Panel", "ProjectDetail", "_s", "id", "navigate", "project", "setProject", "loading", "setLoading", "mockProject", "name", "title", "author", "type", "status", "summary", "description", "wordCount", "chapterCount", "characterCount", "factionCount", "plotCount", "progress", "createdAt", "updatedAt", "loadProject", "setTimeout", "error", "getStatusColor", "colors", "planning", "writing", "reviewing", "completed", "published", "getStatusText", "texts", "getTypeText", "fantasy", "xianxia", "wuxia", "scifi", "modern", "historical", "romance", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "onClick", "level", "color", "style", "marginTop", "gutter", "marginBottom", "xs", "sm", "lg", "value", "formatter", "toFixed", "valueStyle", "suffix", "percent", "size", "defaultActiveKey", "tab", "bordered", "column", "<PERSON><PERSON>", "label", "span", "md", "extra", "cursor", "volumeCount", "expandIcon", "isActive", "rotate", "ghost", "header", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/ProjectDetail.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Tabs,\n  Button,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Progress,\n  Tag,\n  Descriptions,\n  message,\n  Collapse\n} from 'antd';\nimport {\n  EditOutlined,\n  SettingOutlined,\n  ExportOutlined,\n  BackwardOutlined,\n  CaretRightOutlined,\n  UserOutlined,\n  GlobalOutlined,\n  CrownOutlined,\n  DollarOutlined,\n  ThunderboltOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\nconst { Panel } = Collapse;\n\nconst ProjectDetail = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const [project, setProject] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // 模拟项目数据\n  const mockProject = {\n    id: 1,\n    name: '仙侠传说',\n    title: '九天仙缘录',\n    author: '作者A',\n    type: 'xianxia',\n    status: 'writing',\n    summary: '这是一个关于修仙者在九天之上寻找仙缘的故事...',\n    description: '详细的项目描述...',\n    wordCount: 89000,\n    chapterCount: 45,\n    characterCount: 12,\n    factionCount: 8,\n    plotCount: 15,\n    progress: 65,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-15'\n  };\n\n  const loadProject = useCallback(async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setProject(mockProject);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      message.error('加载项目详情失败');\n      setLoading(false);\n    }\n  }, [mockProject]);\n\n  useEffect(() => {\n    loadProject();\n  }, [id, loadProject]);\n\n  const getStatusColor = (status) => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n\n  const getTypeText = (type) => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n\n  if (loading || !project) {\n    return <div className=\"loading-container\">加载中...</div>;\n  }\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Space>\n          <Button\n            icon={<BackwardOutlined />}\n            onClick={() => navigate('/projects')}\n          >\n            返回项目列表\n          </Button>\n          <Title level={2} className=\"page-title\">{project.name}</Title>\n          <Tag color={getStatusColor(project.status)}>\n            {getStatusText(project.status)}\n          </Tag>\n          <Tag>{getTypeText(project.type)}</Tag>\n        </Space>\n\n        <Space style={{ marginTop: 16 }}>\n          <Button type=\"primary\" icon={<EditOutlined />}>\n            编辑项目\n          </Button>\n          <Button icon={<SettingOutlined />}>\n            项目设置\n          </Button>\n          <Button icon={<ExportOutlined />}>\n            导出项目\n          </Button>\n        </Space>\n      </div>\n\n      {/* 项目统计 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总字数\"\n              value={project.wordCount}\n              formatter={(value) => `${(value / 10000).toFixed(1)}万`}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"章节数\"\n              value={project.chapterCount}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"人物数\"\n              value={project.characterCount}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"完成进度\"\n              value={project.progress}\n              suffix=\"%\"\n              valueStyle={{ color: '#fa8c16' }}\n            />\n            <Progress\n              percent={project.progress}\n              size=\"small\"\n              status={project.progress === 100 ? 'success' : 'active'}\n              style={{ marginTop: 8 }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 项目管理 */}\n      <Card>\n        <Tabs defaultActiveKey=\"overview\">\n          <TabPane tab=\"项目概览\" key=\"overview\">\n            <Descriptions bordered column={2}>\n              <Descriptions.Item label=\"项目名称\">{project.name}</Descriptions.Item>\n              <Descriptions.Item label=\"小说标题\">{project.title}</Descriptions.Item>\n              <Descriptions.Item label=\"作者\">{project.author}</Descriptions.Item>\n              <Descriptions.Item label=\"项目类型\">{getTypeText(project.type)}</Descriptions.Item>\n              <Descriptions.Item label=\"创建时间\">{project.createdAt}</Descriptions.Item>\n              <Descriptions.Item label=\"最后修改\">{project.updatedAt}</Descriptions.Item>\n              <Descriptions.Item label=\"项目简介\" span={2}>\n                {project.summary}\n              </Descriptions.Item>\n              <Descriptions.Item label=\"详细描述\" span={2}>\n                {project.description}\n              </Descriptions.Item>\n            </Descriptions>\n          </TabPane>\n\n          <TabPane tab=\"卷宗管理\" key=\"volumes\">\n            <Row gutter={[16, 16]}>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"卷宗管理\"\n                  extra={<Button type=\"link\">管理卷宗</Button>}\n                  onClick={() => navigate(`/projects/${id}/volumes`)}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Statistic value={project.volumeCount || 0} suffix=\"个卷宗\" />\n                  <Text type=\"secondary\">管理小说卷宗结构</Text>\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"章节管理\"\n                  extra={<Button type=\"link\">管理章节</Button>}\n                  onClick={() => navigate(`/projects/${id}/volumes`)}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Statistic value={project.chapterCount} suffix=\"个章节\" />\n                  <Text type=\"secondary\">管理章节内容和结构</Text>\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"写作进度\"\n                  extra={<Button type=\"link\">查看详情</Button>}\n                  onClick={() => navigate(`/projects/${id}/volumes`)}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Statistic value={project.progress} suffix=\"%\" />\n                  <Text type=\"secondary\">整体创作进度跟踪</Text>\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"独立章节\"\n                  extra={<Button type=\"link\">查看全部</Button>}\n                  onClick={() => navigate(`/projects/${id}/volumes`)}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Statistic value={0} suffix=\"个独立章节\" />\n                  <Text type=\"secondary\">未分配到卷宗的章节</Text>\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"AI辅助写作\"\n                  extra={<Button type=\"link\">使用AI</Button>}\n                  onClick={() => navigate('/ai-assistant')}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Text>AI续写与生成</Text>\n                  <br />\n                  <Text type=\"secondary\">智能章节续写和大纲生成</Text>\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"导出功能\"\n                  extra={<Button type=\"link\">导出</Button>}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Text>内容导出</Text>\n                  <br />\n                  <Text type=\"secondary\">导出卷宗、章节内容</Text>\n                </Card>\n              </Col>\n            </Row>\n          </TabPane>\n\n          <TabPane tab=\"内容管理\" key=\"content\">\n            <Collapse\n              defaultActiveKey={['characters', 'distribution']}\n              expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}\n              ghost\n            >\n              <Panel header=\"角色与势力管理\" key=\"characters\" extra={<UserOutlined />}>\n                <Row gutter={[16, 16]}>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"人物管理\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/characters`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Statistic value={project.characterCount} suffix=\"个人物\" />\n                      <Text type=\"secondary\">管理角色档案</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"势力管理\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/factions`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Statistic value={project.factionCount} suffix=\"个势力\" />\n                      <Text type=\"secondary\">管理势力组织</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"剧情管理\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/plots`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Statistic value={project.plotCount} suffix=\"个剧情\" />\n                      <Text type=\"secondary\">管理剧情线索</Text>\n                    </Card>\n                  </Col>\n                </Row>\n              </Panel>\n\n              <Panel header=\"世界分布管理\" key=\"distribution\" extra={<GlobalOutlined />}>\n                <Row gutter={[16, 16]}>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"资源分布\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/resource-distribution`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Statistic value={0} suffix=\"个资源点\" />\n                      <Text type=\"secondary\">管理资源分布</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"种族分布\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/race-distribution`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Statistic value={0} suffix=\"个种族区域\" />\n                      <Text type=\"secondary\">管理种族分布</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"秘境分布\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/secret-realm-distribution`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Statistic value={0} suffix=\"个秘境\" />\n                      <Text type=\"secondary\">管理秘境分布</Text>\n                    </Card>\n                  </Col>\n                </Row>\n              </Panel>\n            </Collapse>\n          </TabPane>\n\n          <TabPane tab=\"设定管理\" key=\"settings\">\n            <Collapse\n              defaultActiveKey={['world', 'power', 'society']}\n              expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}\n              ghost\n            >\n              <Panel header=\"世界观与基础设定\" key=\"world\" extra={<GlobalOutlined />}>\n                <Row gutter={[16, 16]}>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"世界设定\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/world-settings`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>世界观设定</Text>\n                      <br />\n                      <Text type=\"secondary\">地理、历史、文化</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"地图结构\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/map-structures`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>地图结构</Text>\n                      <br />\n                      <Text type=\"secondary\">地图、地形、区域</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"维度结构\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/dimension-structures`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>维度结构</Text>\n                      <br />\n                      <Text type=\"secondary\">维度、法则、传送</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"种族类别\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/race-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>种族类别</Text>\n                      <br />\n                      <Text type=\"secondary\">种族、特性、关系</Text>\n                    </Card>\n                  </Col>\n                </Row>\n              </Panel>\n\n              <Panel header=\"力量与战斗体系\" key=\"power\" extra={<ThunderboltOutlined />}>\n                <Row gutter={[16, 16]}>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"修炼体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/cultivation-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>修炼体系</Text>\n                      <br />\n                      <Text type=\"secondary\">能力、等级、方法</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"功法体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/martial-arts-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>功法体系</Text>\n                      <br />\n                      <Text type=\"secondary\">功法、招式、传承</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"装备体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/equipment-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>装备体系</Text>\n                      <br />\n                      <Text type=\"secondary\">装备、强化、套装</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"灵宝体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/spiritual-treasure-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>灵宝体系</Text>\n                      <br />\n                      <Text type=\"secondary\">灵宝、器灵、炼制</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"宠物体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/pet-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>宠物体系</Text>\n                      <br />\n                      <Text type=\"secondary\">宠物、进化、培养</Text>\n                    </Card>\n                  </Col>\n                </Row>\n              </Panel>\n\n              <Panel header=\"社会与政治体系\" key=\"society\" extra={<CrownOutlined />}>\n                <Row gutter={[16, 16]}>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"政治体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/political-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>政治体系</Text>\n                      <br />\n                      <Text type=\"secondary\">政府、法律、权力</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"司法体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/judicial-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>司法体系</Text>\n                      <br />\n                      <Text type=\"secondary\">法院、执法、审判</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"生民体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/civilian-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>生民体系</Text>\n                      <br />\n                      <Text type=\"secondary\">人口、社会、生活</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"职业体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/profession-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>职业体系</Text>\n                      <br />\n                      <Text type=\"secondary\">职业、技能、组织</Text>\n                    </Card>\n                  </Col>\n                </Row>\n              </Panel>\n\n              <Panel header=\"经济与商业体系\" key=\"economy\" extra={<DollarOutlined />}>\n                <Row gutter={[16, 16]}>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"货币体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/currency-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>货币体系</Text>\n                      <br />\n                      <Text type=\"secondary\">货币、金融、经济</Text>\n                    </Card>\n                  </Col>\n                  <Col xs={24} sm={12} md={8}>\n                    <Card\n                      title=\"商业体系\"\n                      extra={<Button type=\"link\">查看全部</Button>}\n                      onClick={() => navigate(`/projects/${id}/commerce-systems`)}\n                      style={{ cursor: 'pointer' }}\n                    >\n                      <Text>商业体系</Text>\n                      <br />\n                      <Text type=\"secondary\">贸易、商会、市场</Text>\n                    </Card>\n                  </Col>\n                </Row>\n              </Panel>\n            </Collapse>\n          </TabPane>\n\n          <TabPane tab=\"工具\" key=\"tools\">\n            <Row gutter={[16, 16]}>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"时间线\"\n                  extra={<Button type=\"link\">查看</Button>}\n                  onClick={() => navigate(`/projects/${id}/timeline`)}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Text>查看项目时间线</Text>\n                  <br />\n                  <Text type=\"secondary\">事件、发展、历史</Text>\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"关系网络\"\n                  extra={<Button type=\"link\">查看</Button>}\n                  onClick={() => navigate(`/projects/${id}/relations`)}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Text>人物关系图谱</Text>\n                  <br />\n                  <Text type=\"secondary\">人物、势力、关系</Text>\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"AI助手\"\n                  extra={<Button type=\"link\">使用</Button>}\n                  onClick={() => navigate('/ai-assistant')}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Text>AI辅助创作</Text>\n                  <br />\n                  <Text type=\"secondary\">智能生成、续写、分析</Text>\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"AI测试\"\n                  extra={<Button type=\"link\">测试</Button>}\n                  onClick={() => navigate('/ai-test')}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Text>AI功能测试</Text>\n                  <br />\n                  <Text type=\"secondary\">测试AI模型功能</Text>\n                </Card>\n              </Col>\n              <Col xs={24} sm={12} md={8}>\n                <Card\n                  title=\"系统设置\"\n                  extra={<Button type=\"link\">设置</Button>}\n                  onClick={() => navigate('/settings')}\n                  style={{ cursor: 'pointer' }}\n                >\n                  <Text>系统配置</Text>\n                  <br />\n                  <Text type=\"secondary\">AI配置、系统设置</Text>\n                </Card>\n              </Col>\n            </Row>\n          </TabPane>\n        </Tabs>\n      </Card>\n    </div>\n  );\n};\n\nexport default ProjectDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,IAAI,EACJC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,GAAG,EACHC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,MAAM;AACb,SACEC,YAAY,EACZC,eAAe,EACfC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,aAAa,EACbC,cAAc,EACdC,mBAAmB,QACd,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGtB,UAAU;AAClC,MAAM;EAAEuB;AAAQ,CAAC,GAAG1B,IAAI;AACxB,MAAM;EAAE2B;AAAM,CAAC,GAAGhB,QAAQ;AAE1B,MAAMiB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAG,CAAC,GAAGjC,SAAS,CAAC,CAAC;EAC1B,MAAMkC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM0C,WAAW,GAAG;IAClBN,EAAE,EAAE,CAAC;IACLO,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,YAAY;IACzBC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,WAAW,GAAGxD,WAAW,CAAC,YAAY;IAC1CuC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACAkB,UAAU,CAAC,MAAM;QACfpB,UAAU,CAACG,WAAW,CAAC;QACvBD,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,UAAU,CAAC;MACzBnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC;EAEjBzC,SAAS,CAAC,MAAM;IACdyD,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACtB,EAAE,EAAEsB,WAAW,CAAC,CAAC;EAErB,MAAMG,cAAc,GAAId,MAAM,IAAK;IACjC,MAAMe,MAAM,GAAG;MACbC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,MAAM,CAACf,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMqB,aAAa,GAAIrB,MAAM,IAAK;IAChC,MAAMsB,KAAK,GAAG;MACZN,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAACtB,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAMuB,WAAW,GAAIxB,IAAI,IAAK;IAC5B,MAAMuB,KAAK,GAAG;MACZE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE;IACX,CAAC;IACD,OAAOR,KAAK,CAACvB,IAAI,CAAC,IAAIA,IAAI;EAC5B,CAAC;EAED,IAAIN,OAAO,IAAI,CAACF,OAAO,EAAE;IACvB,oBAAOT,OAAA;MAAKiD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACxD;EAEA,oBACEtD,OAAA;IAAKiD,SAAS,EAAC,SAAS;IAAAC,QAAA,gBACtBlD,OAAA;MAAKiD,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BlD,OAAA,CAACrB,KAAK;QAAAuE,QAAA,gBACJlD,OAAA,CAACtB,MAAM;UACL6E,IAAI,eAAEvD,OAAA,CAACR,gBAAgB;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,WAAW,CAAE;UAAA0C,QAAA,EACtC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtD,OAAA,CAACC,KAAK;UAACwD,KAAK,EAAE,CAAE;UAACR,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEzC,OAAO,CAACK;QAAI;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9DtD,OAAA,CAACf,GAAG;UAACyE,KAAK,EAAE1B,cAAc,CAACvB,OAAO,CAACS,MAAM,CAAE;UAAAgC,QAAA,EACxCX,aAAa,CAAC9B,OAAO,CAACS,MAAM;QAAC;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACNtD,OAAA,CAACf,GAAG;UAAAiE,QAAA,EAAET,WAAW,CAAChC,OAAO,CAACQ,IAAI;QAAC;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAERtD,OAAA,CAACrB,KAAK;QAACgF,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAG,CAAE;QAAAV,QAAA,gBAC9BlD,OAAA,CAACtB,MAAM;UAACuC,IAAI,EAAC,SAAS;UAACsC,IAAI,eAAEvD,OAAA,CAACX,YAAY;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtD,OAAA,CAACtB,MAAM;UAAC6E,IAAI,eAAEvD,OAAA,CAACV,eAAe;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAEnC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTtD,OAAA,CAACtB,MAAM;UAAC6E,IAAI,eAAEvD,OAAA,CAACT,cAAc;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNtD,OAAA,CAACnB,GAAG;MAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACF,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAG,CAAE;MAAAZ,QAAA,gBACjDlD,OAAA,CAAClB,GAAG;QAACiF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBlD,OAAA,CAACxB,IAAI;UAAA0E,QAAA,eACHlD,OAAA,CAACjB,SAAS;YACRgC,KAAK,EAAC,oBAAK;YACXmD,KAAK,EAAEzD,OAAO,CAACY,SAAU;YACzB8C,SAAS,EAAGD,KAAK,IAAK,GAAG,CAACA,KAAK,GAAG,KAAK,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAI;YACvDC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;QAACiF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBlD,OAAA,CAACxB,IAAI;UAAA0E,QAAA,eACHlD,OAAA,CAACjB,SAAS;YACRgC,KAAK,EAAC,oBAAK;YACXmD,KAAK,EAAEzD,OAAO,CAACa,YAAa;YAC5B+C,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;QAACiF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBlD,OAAA,CAACxB,IAAI;UAAA0E,QAAA,eACHlD,OAAA,CAACjB,SAAS;YACRgC,KAAK,EAAC,oBAAK;YACXmD,KAAK,EAAEzD,OAAO,CAACc,cAAe;YAC9B8C,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;QAACiF,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBlD,OAAA,CAACxB,IAAI;UAAA0E,QAAA,gBACHlD,OAAA,CAACjB,SAAS;YACRgC,KAAK,EAAC,0BAAM;YACZmD,KAAK,EAAEzD,OAAO,CAACiB,QAAS;YACxB4C,MAAM,EAAC,GAAG;YACVD,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFtD,OAAA,CAAChB,QAAQ;YACPuF,OAAO,EAAE9D,OAAO,CAACiB,QAAS;YAC1B8C,IAAI,EAAC,OAAO;YACZtD,MAAM,EAAET,OAAO,CAACiB,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG,QAAS;YACxDiC,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAE;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA,CAACxB,IAAI;MAAA0E,QAAA,eACHlD,OAAA,CAACvB,IAAI;QAACgG,gBAAgB,EAAC,UAAU;QAAAvB,QAAA,gBAC/BlD,OAAA,CAACG,OAAO;UAACuE,GAAG,EAAC,0BAAM;UAAAxB,QAAA,eACjBlD,OAAA,CAACd,YAAY;YAACyF,QAAQ;YAACC,MAAM,EAAE,CAAE;YAAA1B,QAAA,gBAC/BlD,OAAA,CAACd,YAAY,CAAC2F,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAA5B,QAAA,EAAEzC,OAAO,CAACK;YAAI;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAClEtD,OAAA,CAACd,YAAY,CAAC2F,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAA5B,QAAA,EAAEzC,OAAO,CAACM;YAAK;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eACnEtD,OAAA,CAACd,YAAY,CAAC2F,IAAI;cAACC,KAAK,EAAC,cAAI;cAAA5B,QAAA,EAAEzC,OAAO,CAACO;YAAM;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAClEtD,OAAA,CAACd,YAAY,CAAC2F,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAA5B,QAAA,EAAET,WAAW,CAAChC,OAAO,CAACQ,IAAI;YAAC;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eAC/EtD,OAAA,CAACd,YAAY,CAAC2F,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAA5B,QAAA,EAAEzC,OAAO,CAACkB;YAAS;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eACvEtD,OAAA,CAACd,YAAY,CAAC2F,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAA5B,QAAA,EAAEzC,OAAO,CAACmB;YAAS;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAoB,CAAC,eACvEtD,OAAA,CAACd,YAAY,CAAC2F,IAAI;cAACC,KAAK,EAAC,0BAAM;cAACC,IAAI,EAAE,CAAE;cAAA7B,QAAA,EACrCzC,OAAO,CAACU;YAAO;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACpBtD,OAAA,CAACd,YAAY,CAAC2F,IAAI;cAACC,KAAK,EAAC,0BAAM;cAACC,IAAI,EAAE,CAAE;cAAA7B,QAAA,EACrCzC,OAAO,CAACW;YAAW;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAAC,GAdO,UAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAezB,CAAC,eAEVtD,OAAA,CAACG,OAAO;UAACuE,GAAG,EAAC,0BAAM;UAAAxB,QAAA,eACjBlD,OAAA,CAACnB,GAAG;YAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAAX,QAAA,gBACpBlD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,0BAAM;gBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,UAAU,CAAE;gBACnDoD,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACjB,SAAS;kBAACmF,KAAK,EAAEzD,OAAO,CAAC0E,WAAW,IAAI,CAAE;kBAACb,MAAM,EAAC;gBAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC3DtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,0BAAM;gBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,UAAU,CAAE;gBACnDoD,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACjB,SAAS;kBAACmF,KAAK,EAAEzD,OAAO,CAACa,YAAa;kBAACgD,MAAM,EAAC;gBAAK;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACvDtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,0BAAM;gBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,UAAU,CAAE;gBACnDoD,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACjB,SAAS;kBAACmF,KAAK,EAAEzD,OAAO,CAACiB,QAAS;kBAAC4C,MAAM,EAAC;gBAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACjDtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,0BAAM;gBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,UAAU,CAAE;gBACnDoD,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACjB,SAAS;kBAACmF,KAAK,EAAE,CAAE;kBAACI,MAAM,EAAC;gBAAO;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtCtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,4BAAQ;gBACdkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,eAAe,CAAE;gBACzCmD,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;kBAAAgD,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpBtD,OAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,0BAAM;gBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACvCK,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;kBAAAgD,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBtD,OAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GArEgB,SAAS;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsExB,CAAC,eAEVtD,OAAA,CAACG,OAAO;UAACuE,GAAG,EAAC,0BAAM;UAAAxB,QAAA,eACjBlD,OAAA,CAACZ,QAAQ;YACPqF,gBAAgB,EAAE,CAAC,YAAY,EAAE,cAAc,CAAE;YACjDW,UAAU,EAAEA,CAAC;cAAEC;YAAS,CAAC,kBAAKrF,OAAA,CAACP,kBAAkB;cAAC6F,MAAM,EAAED,QAAQ,GAAG,EAAE,GAAG;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChFiC,KAAK;YAAArC,QAAA,gBAELlD,OAAA,CAACI,KAAK;cAACoF,MAAM,EAAC,4CAAS;cAAkBP,KAAK,eAAEjF,OAAA,CAACN,YAAY;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eAC/DlD,OAAA,CAACnB,GAAG;gBAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;gBAAAX,QAAA,gBACpBlD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,aAAa,CAAE;oBACtDoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACjB,SAAS;sBAACmF,KAAK,EAAEzD,OAAO,CAACc,cAAe;sBAAC+C,MAAM,EAAC;oBAAK;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACzDtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,WAAW,CAAE;oBACpDoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACjB,SAAS;sBAACmF,KAAK,EAAEzD,OAAO,CAACe,YAAa;sBAAC8C,MAAM,EAAC;oBAAK;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACvDtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,QAAQ,CAAE;oBACjDoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACjB,SAAS;sBAACmF,KAAK,EAAEzD,OAAO,CAACgB,SAAU;sBAAC6C,MAAM,EAAC;oBAAK;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACpDtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAnCoB,YAAY;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoCjC,CAAC,eAERtD,OAAA,CAACI,KAAK;cAACoF,MAAM,EAAC,sCAAQ;cAAoBP,KAAK,eAAEjF,OAAA,CAACL,cAAc;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eAClElD,OAAA,CAACnB,GAAG;gBAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;gBAAAX,QAAA,gBACpBlD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,wBAAwB,CAAE;oBACjEoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACjB,SAAS;sBAACmF,KAAK,EAAE,CAAE;sBAACI,MAAM,EAAC;oBAAM;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrCtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,oBAAoB,CAAE;oBAC7DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACjB,SAAS;sBAACmF,KAAK,EAAE,CAAE;sBAACI,MAAM,EAAC;oBAAO;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtCtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,4BAA4B,CAAE;oBACrEoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACjB,SAAS;sBAACmF,KAAK,EAAE,CAAE;sBAACI,MAAM,EAAC;oBAAK;sBAAAnB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACpCtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAnCmB,cAAc;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC,GAjFW,SAAS;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkFxB,CAAC,eAEVtD,OAAA,CAACG,OAAO;UAACuE,GAAG,EAAC,0BAAM;UAAAxB,QAAA,eACjBlD,OAAA,CAACZ,QAAQ;YACPqF,gBAAgB,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,CAAE;YAChDW,UAAU,EAAEA,CAAC;cAAEC;YAAS,CAAC,kBAAKrF,OAAA,CAACP,kBAAkB;cAAC6F,MAAM,EAAED,QAAQ,GAAG,EAAE,GAAG;YAAE;cAAAlC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChFiC,KAAK;YAAArC,QAAA,gBAELlD,OAAA,CAACI,KAAK;cAACoF,MAAM,EAAC,kDAAU;cAAaP,KAAK,eAAEjF,OAAA,CAACL,cAAc;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eAC7DlD,OAAA,CAACnB,GAAG;gBAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;gBAAAX,QAAA,gBACpBlD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,iBAAiB,CAAE;oBAC1DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAK;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAClBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,iBAAiB,CAAE;oBAC1DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,uBAAuB,CAAE;oBAChEoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,eAAe,CAAE;oBACxDoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAlDqB,OAAO;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmD7B,CAAC,eAERtD,OAAA,CAACI,KAAK;cAACoF,MAAM,EAAC,4CAAS;cAAaP,KAAK,eAAEjF,OAAA,CAACF,mBAAmB;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eACjElD,OAAA,CAACnB,GAAG;gBAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;gBAAAX,QAAA,gBACpBlD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,sBAAsB,CAAE;oBAC/DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,uBAAuB,CAAE;oBAChEoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,oBAAoB,CAAE;oBAC7DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,6BAA6B,CAAE;oBACtEoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,cAAc,CAAE;oBACvDoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA9DoB,OAAO;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+D5B,CAAC,eAERtD,OAAA,CAACI,KAAK;cAACoF,MAAM,EAAC,4CAAS;cAAeP,KAAK,eAAEjF,OAAA,CAACJ,aAAa;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eAC7DlD,OAAA,CAACnB,GAAG;gBAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;gBAAAX,QAAA,gBACpBlD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,oBAAoB,CAAE;oBAC7DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,mBAAmB,CAAE;oBAC5DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,mBAAmB,CAAE;oBAC5DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,qBAAqB,CAAE;oBAC9DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GAlDoB,SAAS;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmD9B,CAAC,eAERtD,OAAA,CAACI,KAAK;cAACoF,MAAM,EAAC,4CAAS;cAAeP,KAAK,eAAEjF,OAAA,CAACH,cAAc;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,eAC9DlD,OAAA,CAACnB,GAAG;gBAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;gBAAAX,QAAA,gBACpBlD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,mBAAmB,CAAE;oBAC5DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;kBAACiF,EAAE,EAAE,EAAG;kBAACC,EAAE,EAAE,EAAG;kBAACgB,EAAE,EAAE,CAAE;kBAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;oBACHuC,KAAK,EAAC,0BAAM;oBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;sBAACuC,IAAI,EAAC,MAAM;sBAAAiC,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAE;oBACzCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,mBAAmB,CAAE;oBAC5DoD,KAAK,EAAE;sBAAEuB,MAAM,EAAE;oBAAU,CAAE;oBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;sBAAAgD,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACjBtD,OAAA;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;sBAACe,IAAI,EAAC,WAAW;sBAAAiC,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC,GA1BoB,SAAS;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA2B9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC,GA7MW,UAAU;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8MzB,CAAC,eAEVtD,OAAA,CAACG,OAAO;UAACuE,GAAG,EAAC,cAAI;UAAAxB,QAAA,eACflD,OAAA,CAACnB,GAAG;YAACgF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAAX,QAAA,gBACpBlD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,oBAAK;gBACXkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,WAAW,CAAE;gBACpDoD,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;kBAAAgD,QAAA,EAAC;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACpBtD,OAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,0BAAM;gBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,YAAY,CAAE;gBACrDoD,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;kBAAAgD,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnBtD,OAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,gBAAM;gBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,eAAe,CAAE;gBACzCmD,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;kBAAAgD,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnBtD,OAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,gBAAM;gBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,UAAU,CAAE;gBACpCmD,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;kBAAAgD,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnBtD,OAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNtD,OAAA,CAAClB,GAAG;cAACiF,EAAE,EAAE,EAAG;cAACC,EAAE,EAAE,EAAG;cAACgB,EAAE,EAAE,CAAE;cAAA9B,QAAA,eACzBlD,OAAA,CAACxB,IAAI;gBACHuC,KAAK,EAAC,0BAAM;gBACZkE,KAAK,eAAEjF,OAAA,CAACtB,MAAM;kBAACuC,IAAI,EAAC,MAAM;kBAAAiC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAE;gBACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,WAAW,CAAE;gBACrCmD,KAAK,EAAE;kBAAEuB,MAAM,EAAE;gBAAU,CAAE;gBAAAhC,QAAA,gBAE7BlD,OAAA,CAACE,IAAI;kBAAAgD,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjBtD,OAAA;kBAAAmD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNtD,OAAA,CAACE,IAAI;kBAACe,IAAI,EAAC,WAAW;kBAAAiC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GA9Dc,OAAO;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+DpB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChD,EAAA,CArmBID,aAAa;EAAA,QACF/B,SAAS,EACPC,WAAW;AAAA;AAAAkH,EAAA,GAFxBpF,aAAa;AAumBnB,eAAeA,aAAa;AAAC,IAAAoF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}