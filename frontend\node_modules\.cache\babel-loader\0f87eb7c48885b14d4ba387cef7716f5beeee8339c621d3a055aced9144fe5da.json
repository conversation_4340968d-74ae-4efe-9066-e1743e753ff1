{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"data\", \"renderItem\", \"renderRawItem\", \"itemKey\", \"itemWidth\", \"ssr\", \"style\", \"className\", \"maxCount\", \"renderRest\", \"renderRawRest\", \"suffix\", \"component\", \"itemComponent\", \"onVisibleChange\"];\nimport * as React from 'react';\nimport { useState, useMemo, useCallback } from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport Item from \"./Item\";\nimport useEffectState, { useBatcher } from \"./hooks/useEffectState\";\nimport RawItem from \"./RawItem\";\nimport { OverflowContext } from \"./context\";\nvar RESPONSIVE = 'responsive';\nvar INVALIDATE = 'invalidate';\nexport { OverflowContext } from \"./context\";\nfunction defaultRenderRest(omittedItems) {\n  return \"+ \".concat(omittedItems.length, \" ...\");\n}\nfunction Overflow(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-overflow' : _props$prefixCls,\n    _props$data = props.data,\n    data = _props$data === void 0 ? [] : _props$data,\n    renderItem = props.renderItem,\n    renderRawItem = props.renderRawItem,\n    itemKey = props.itemKey,\n    _props$itemWidth = props.itemWidth,\n    itemWidth = _props$itemWidth === void 0 ? 10 : _props$itemWidth,\n    ssr = props.ssr,\n    style = props.style,\n    className = props.className,\n    maxCount = props.maxCount,\n    renderRest = props.renderRest,\n    renderRawRest = props.renderRawRest,\n    suffix = props.suffix,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    itemComponent = props.itemComponent,\n    onVisibleChange = props.onVisibleChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var fullySSR = ssr === 'full';\n  var notifyEffectUpdate = useBatcher();\n  var _useEffectState = useEffectState(notifyEffectUpdate, null),\n    _useEffectState2 = _slicedToArray(_useEffectState, 2),\n    containerWidth = _useEffectState2[0],\n    setContainerWidth = _useEffectState2[1];\n  var mergedContainerWidth = containerWidth || 0;\n  var _useEffectState3 = useEffectState(notifyEffectUpdate, new Map()),\n    _useEffectState4 = _slicedToArray(_useEffectState3, 2),\n    itemWidths = _useEffectState4[0],\n    setItemWidths = _useEffectState4[1];\n  var _useEffectState5 = useEffectState(notifyEffectUpdate, 0),\n    _useEffectState6 = _slicedToArray(_useEffectState5, 2),\n    prevRestWidth = _useEffectState6[0],\n    setPrevRestWidth = _useEffectState6[1];\n  var _useEffectState7 = useEffectState(notifyEffectUpdate, 0),\n    _useEffectState8 = _slicedToArray(_useEffectState7, 2),\n    restWidth = _useEffectState8[0],\n    setRestWidth = _useEffectState8[1];\n  var _useEffectState9 = useEffectState(notifyEffectUpdate, 0),\n    _useEffectState10 = _slicedToArray(_useEffectState9, 2),\n    suffixWidth = _useEffectState10[0],\n    setSuffixWidth = _useEffectState10[1];\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    suffixFixedStart = _useState2[0],\n    setSuffixFixedStart = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    displayCount = _useState4[0],\n    setDisplayCount = _useState4[1];\n  var mergedDisplayCount = React.useMemo(function () {\n    if (displayCount === null && fullySSR) {\n      return Number.MAX_SAFE_INTEGER;\n    }\n    return displayCount || 0;\n  }, [displayCount, containerWidth]);\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    restReady = _useState6[0],\n    setRestReady = _useState6[1];\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n\n  // Always use the max width to avoid blink\n  var mergedRestWidth = Math.max(prevRestWidth, restWidth);\n\n  // ================================= Data =================================\n  var isResponsive = maxCount === RESPONSIVE;\n  var shouldResponsive = data.length && isResponsive;\n  var invalidate = maxCount === INVALIDATE;\n\n  /**\n   * When is `responsive`, we will always render rest node to get the real width of it for calculation\n   */\n  var showRest = shouldResponsive || typeof maxCount === 'number' && data.length > maxCount;\n  var mergedData = useMemo(function () {\n    var items = data;\n    if (shouldResponsive) {\n      if (containerWidth === null && fullySSR) {\n        items = data;\n      } else {\n        items = data.slice(0, Math.min(data.length, mergedContainerWidth / itemWidth));\n      }\n    } else if (typeof maxCount === 'number') {\n      items = data.slice(0, maxCount);\n    }\n    return items;\n  }, [data, itemWidth, containerWidth, maxCount, shouldResponsive]);\n  var omittedItems = useMemo(function () {\n    if (shouldResponsive) {\n      return data.slice(mergedDisplayCount + 1);\n    }\n    return data.slice(mergedData.length);\n  }, [data, mergedData, shouldResponsive, mergedDisplayCount]);\n\n  // ================================= Item =================================\n  var getKey = useCallback(function (item, index) {\n    var _ref;\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return (_ref = itemKey && (item === null || item === void 0 ? void 0 : item[itemKey])) !== null && _ref !== void 0 ? _ref : index;\n  }, [itemKey]);\n  var mergedRenderItem = useCallback(renderItem || function (item) {\n    return item;\n  }, [renderItem]);\n  function updateDisplayCount(count, suffixFixedStartVal, notReady) {\n    // React 18 will sync render even when the value is same in some case.\n    // We take `mergedData` as deps which may cause dead loop if it's dynamic generate.\n    // ref: https://github.com/ant-design/ant-design/issues/36559\n    if (displayCount === count && (suffixFixedStartVal === undefined || suffixFixedStartVal === suffixFixedStart)) {\n      return;\n    }\n    setDisplayCount(count);\n    if (!notReady) {\n      setRestReady(count < data.length - 1);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(count);\n    }\n    if (suffixFixedStartVal !== undefined) {\n      setSuffixFixedStart(suffixFixedStartVal);\n    }\n  }\n\n  // ================================= Size =================================\n  function onOverflowResize(_, element) {\n    setContainerWidth(element.clientWidth);\n  }\n  function registerSize(key, width) {\n    setItemWidths(function (origin) {\n      var clone = new Map(origin);\n      if (width === null) {\n        clone.delete(key);\n      } else {\n        clone.set(key, width);\n      }\n      return clone;\n    });\n  }\n  function registerOverflowSize(_, width) {\n    setRestWidth(width);\n    setPrevRestWidth(restWidth);\n  }\n  function registerSuffixSize(_, width) {\n    setSuffixWidth(width);\n  }\n\n  // ================================ Effect ================================\n  function getItemWidth(index) {\n    return itemWidths.get(getKey(mergedData[index], index));\n  }\n  useLayoutEffect(function () {\n    if (mergedContainerWidth && typeof mergedRestWidth === 'number' && mergedData) {\n      var totalWidth = suffixWidth;\n      var len = mergedData.length;\n      var lastIndex = len - 1;\n\n      // When data count change to 0, reset this since not loop will reach\n      if (!len) {\n        updateDisplayCount(0, null);\n        return;\n      }\n      for (var i = 0; i < len; i += 1) {\n        var currentItemWidth = getItemWidth(i);\n\n        // Fully will always render\n        if (fullySSR) {\n          currentItemWidth = currentItemWidth || 0;\n        }\n\n        // Break since data not ready\n        if (currentItemWidth === undefined) {\n          updateDisplayCount(i - 1, undefined, true);\n          break;\n        }\n\n        // Find best match\n        totalWidth += currentItemWidth;\n        if (\n        // Only one means `totalWidth` is the final width\n        lastIndex === 0 && totalWidth <= mergedContainerWidth ||\n        // Last two width will be the final width\n        i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth) {\n          // Additional check if match the end\n          updateDisplayCount(lastIndex, null);\n          break;\n        } else if (totalWidth + mergedRestWidth > mergedContainerWidth) {\n          // Can not hold all the content to show rest\n          updateDisplayCount(i - 1, totalWidth - currentItemWidth - suffixWidth + restWidth);\n          break;\n        }\n      }\n      if (suffix && getItemWidth(0) + suffixWidth > mergedContainerWidth) {\n        setSuffixFixedStart(null);\n      }\n    }\n  }, [mergedContainerWidth, itemWidths, restWidth, suffixWidth, getKey, mergedData]);\n\n  // ================================ Render ================================\n  var displayRest = restReady && !!omittedItems.length;\n  var suffixStyle = {};\n  if (suffixFixedStart !== null && shouldResponsive) {\n    suffixStyle = {\n      position: 'absolute',\n      left: suffixFixedStart,\n      top: 0\n    };\n  }\n  var itemSharedProps = {\n    prefixCls: itemPrefixCls,\n    responsive: shouldResponsive,\n    component: itemComponent,\n    invalidate: invalidate\n  };\n\n  // >>>>> Choice render fun by `renderRawItem`\n  var internalRenderItemNode = renderRawItem ? function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n      key: key,\n      value: _objectSpread(_objectSpread({}, itemSharedProps), {}, {\n        order: index,\n        item: item,\n        itemKey: key,\n        registerSize: registerSize,\n        display: index <= mergedDisplayCount\n      })\n    }, renderRawItem(item, index));\n  } : function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, {\n      order: index,\n      key: key,\n      item: item,\n      renderItem: mergedRenderItem,\n      itemKey: key,\n      registerSize: registerSize,\n      display: index <= mergedDisplayCount\n    }));\n  };\n\n  // >>>>> Rest node\n  var restContextProps = {\n    order: displayRest ? mergedDisplayCount : Number.MAX_SAFE_INTEGER,\n    className: \"\".concat(itemPrefixCls, \"-rest\"),\n    registerSize: registerOverflowSize,\n    display: displayRest\n  };\n  var mergedRenderRest = renderRest || defaultRenderRest;\n  var restNode = renderRawRest ? /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n    value: _objectSpread(_objectSpread({}, itemSharedProps), restContextProps)\n  }, renderRawRest(omittedItems)) : /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, restContextProps), typeof mergedRenderRest === 'function' ? mergedRenderRest(omittedItems) : mergedRenderRest);\n  var overflowNode = /*#__PURE__*/React.createElement(Component, _extends({\n    className: classNames(!invalidate && prefixCls, className),\n    style: style,\n    ref: ref\n  }, restProps), mergedData.map(internalRenderItemNode), showRest ? restNode : null, suffix && /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, {\n    responsive: isResponsive,\n    responsiveDisabled: !shouldResponsive,\n    order: mergedDisplayCount,\n    className: \"\".concat(itemPrefixCls, \"-suffix\"),\n    registerSize: registerSuffixSize,\n    display: true,\n    style: suffixStyle\n  }), suffix));\n  return isResponsive ? /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onOverflowResize,\n    disabled: !shouldResponsive\n  }, overflowNode) : overflowNode;\n}\nvar ForwardOverflow = /*#__PURE__*/React.forwardRef(Overflow);\nForwardOverflow.displayName = 'Overflow';\nForwardOverflow.Item = RawItem;\nForwardOverflow.RESPONSIVE = RESPONSIVE;\nForwardOverflow.INVALIDATE = INVALIDATE;\n\n// Convert to generic type\nexport default ForwardOverflow;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_slicedToArray", "_objectWithoutProperties", "_excluded", "React", "useState", "useMemo", "useCallback", "classNames", "ResizeObserver", "useLayoutEffect", "<PERSON><PERSON>", "useEffectState", "useBatcher", "RawItem", "OverflowContext", "RESPONSIVE", "INVALIDATE", "defaultRenderRest", "omittedItems", "concat", "length", "Overflow", "props", "ref", "_props$prefixCls", "prefixCls", "_props$data", "data", "renderItem", "renderRawItem", "itemKey", "_props$itemWidth", "itemWidth", "ssr", "style", "className", "maxCount", "renderRest", "renderRawRest", "suffix", "_props$component", "component", "Component", "itemComponent", "onVisibleChange", "restProps", "fullySSR", "notifyEffectUpdate", "_useEffectState", "_useEffectState2", "containerWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mergedContainerWidth", "_useEffectState3", "Map", "_useEffectState4", "itemWidths", "setItemWidths", "_useEffectState5", "_useEffectState6", "prevRestWidth", "setPrevRestWidth", "_useEffectState7", "_useEffectState8", "restWidth", "setRestWidth", "_useEffectState9", "_useEffectState10", "suffixWidth", "setSuffixWidth", "_useState", "_useState2", "suffixFixedStart", "setSuffixFixedStart", "_useState3", "_useState4", "displayCount", "setDisplayCount", "mergedDisplayCount", "Number", "MAX_SAFE_INTEGER", "_useState5", "_useState6", "restReady", "setRestReady", "itemPrefixCls", "mergedRestWidth", "Math", "max", "isResponsive", "shouldResponsive", "invalidate", "showRest", "mergedData", "items", "slice", "min", "<PERSON><PERSON><PERSON>", "item", "index", "_ref", "mergedRenderItem", "updateDisplayCount", "count", "suffixFixedStartVal", "notReady", "undefined", "onOverflowResize", "_", "element", "clientWidth", "registerSize", "key", "width", "origin", "clone", "delete", "set", "registerOverflowSize", "registerSuffixSize", "getItemWidth", "get", "totalWidth", "len", "lastIndex", "i", "currentItemWidth", "displayRest", "suffixStyle", "position", "left", "top", "itemSharedProps", "responsive", "internalRenderItemNode", "createElement", "Provider", "value", "order", "display", "restContextProps", "mergedRenderRest", "restNode", "overflowNode", "map", "responsiveDisabled", "onResize", "disabled", "ForwardOverflow", "forwardRef", "displayName"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/rc-overflow/es/Overflow.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"data\", \"renderItem\", \"renderRawItem\", \"itemKey\", \"itemWidth\", \"ssr\", \"style\", \"className\", \"maxCount\", \"renderRest\", \"renderRawRest\", \"suffix\", \"component\", \"itemComponent\", \"onVisibleChange\"];\nimport * as React from 'react';\nimport { useState, useMemo, useCallback } from 'react';\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport Item from \"./Item\";\nimport useEffectState, { useBatcher } from \"./hooks/useEffectState\";\nimport RawItem from \"./RawItem\";\nimport { OverflowContext } from \"./context\";\nvar RESPONSIVE = 'responsive';\nvar INVALIDATE = 'invalidate';\nexport { OverflowContext } from \"./context\";\nfunction defaultRenderRest(omittedItems) {\n  return \"+ \".concat(omittedItems.length, \" ...\");\n}\nfunction Overflow(props, ref) {\n  var _props$prefixCls = props.prefixCls,\n    prefixCls = _props$prefixCls === void 0 ? 'rc-overflow' : _props$prefixCls,\n    _props$data = props.data,\n    data = _props$data === void 0 ? [] : _props$data,\n    renderItem = props.renderItem,\n    renderRawItem = props.renderRawItem,\n    itemKey = props.itemKey,\n    _props$itemWidth = props.itemWidth,\n    itemWidth = _props$itemWidth === void 0 ? 10 : _props$itemWidth,\n    ssr = props.ssr,\n    style = props.style,\n    className = props.className,\n    maxCount = props.maxCount,\n    renderRest = props.renderRest,\n    renderRawRest = props.renderRawRest,\n    suffix = props.suffix,\n    _props$component = props.component,\n    Component = _props$component === void 0 ? 'div' : _props$component,\n    itemComponent = props.itemComponent,\n    onVisibleChange = props.onVisibleChange,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var fullySSR = ssr === 'full';\n  var notifyEffectUpdate = useBatcher();\n  var _useEffectState = useEffectState(notifyEffectUpdate, null),\n    _useEffectState2 = _slicedToArray(_useEffectState, 2),\n    containerWidth = _useEffectState2[0],\n    setContainerWidth = _useEffectState2[1];\n  var mergedContainerWidth = containerWidth || 0;\n  var _useEffectState3 = useEffectState(notifyEffectUpdate, new Map()),\n    _useEffectState4 = _slicedToArray(_useEffectState3, 2),\n    itemWidths = _useEffectState4[0],\n    setItemWidths = _useEffectState4[1];\n  var _useEffectState5 = useEffectState(notifyEffectUpdate, 0),\n    _useEffectState6 = _slicedToArray(_useEffectState5, 2),\n    prevRestWidth = _useEffectState6[0],\n    setPrevRestWidth = _useEffectState6[1];\n  var _useEffectState7 = useEffectState(notifyEffectUpdate, 0),\n    _useEffectState8 = _slicedToArray(_useEffectState7, 2),\n    restWidth = _useEffectState8[0],\n    setRestWidth = _useEffectState8[1];\n  var _useEffectState9 = useEffectState(notifyEffectUpdate, 0),\n    _useEffectState10 = _slicedToArray(_useEffectState9, 2),\n    suffixWidth = _useEffectState10[0],\n    setSuffixWidth = _useEffectState10[1];\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    suffixFixedStart = _useState2[0],\n    setSuffixFixedStart = _useState2[1];\n  var _useState3 = useState(null),\n    _useState4 = _slicedToArray(_useState3, 2),\n    displayCount = _useState4[0],\n    setDisplayCount = _useState4[1];\n  var mergedDisplayCount = React.useMemo(function () {\n    if (displayCount === null && fullySSR) {\n      return Number.MAX_SAFE_INTEGER;\n    }\n    return displayCount || 0;\n  }, [displayCount, containerWidth]);\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    restReady = _useState6[0],\n    setRestReady = _useState6[1];\n  var itemPrefixCls = \"\".concat(prefixCls, \"-item\");\n\n  // Always use the max width to avoid blink\n  var mergedRestWidth = Math.max(prevRestWidth, restWidth);\n\n  // ================================= Data =================================\n  var isResponsive = maxCount === RESPONSIVE;\n  var shouldResponsive = data.length && isResponsive;\n  var invalidate = maxCount === INVALIDATE;\n\n  /**\n   * When is `responsive`, we will always render rest node to get the real width of it for calculation\n   */\n  var showRest = shouldResponsive || typeof maxCount === 'number' && data.length > maxCount;\n  var mergedData = useMemo(function () {\n    var items = data;\n    if (shouldResponsive) {\n      if (containerWidth === null && fullySSR) {\n        items = data;\n      } else {\n        items = data.slice(0, Math.min(data.length, mergedContainerWidth / itemWidth));\n      }\n    } else if (typeof maxCount === 'number') {\n      items = data.slice(0, maxCount);\n    }\n    return items;\n  }, [data, itemWidth, containerWidth, maxCount, shouldResponsive]);\n  var omittedItems = useMemo(function () {\n    if (shouldResponsive) {\n      return data.slice(mergedDisplayCount + 1);\n    }\n    return data.slice(mergedData.length);\n  }, [data, mergedData, shouldResponsive, mergedDisplayCount]);\n\n  // ================================= Item =================================\n  var getKey = useCallback(function (item, index) {\n    var _ref;\n    if (typeof itemKey === 'function') {\n      return itemKey(item);\n    }\n    return (_ref = itemKey && (item === null || item === void 0 ? void 0 : item[itemKey])) !== null && _ref !== void 0 ? _ref : index;\n  }, [itemKey]);\n  var mergedRenderItem = useCallback(renderItem || function (item) {\n    return item;\n  }, [renderItem]);\n  function updateDisplayCount(count, suffixFixedStartVal, notReady) {\n    // React 18 will sync render even when the value is same in some case.\n    // We take `mergedData` as deps which may cause dead loop if it's dynamic generate.\n    // ref: https://github.com/ant-design/ant-design/issues/36559\n    if (displayCount === count && (suffixFixedStartVal === undefined || suffixFixedStartVal === suffixFixedStart)) {\n      return;\n    }\n    setDisplayCount(count);\n    if (!notReady) {\n      setRestReady(count < data.length - 1);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(count);\n    }\n    if (suffixFixedStartVal !== undefined) {\n      setSuffixFixedStart(suffixFixedStartVal);\n    }\n  }\n\n  // ================================= Size =================================\n  function onOverflowResize(_, element) {\n    setContainerWidth(element.clientWidth);\n  }\n  function registerSize(key, width) {\n    setItemWidths(function (origin) {\n      var clone = new Map(origin);\n      if (width === null) {\n        clone.delete(key);\n      } else {\n        clone.set(key, width);\n      }\n      return clone;\n    });\n  }\n  function registerOverflowSize(_, width) {\n    setRestWidth(width);\n    setPrevRestWidth(restWidth);\n  }\n  function registerSuffixSize(_, width) {\n    setSuffixWidth(width);\n  }\n\n  // ================================ Effect ================================\n  function getItemWidth(index) {\n    return itemWidths.get(getKey(mergedData[index], index));\n  }\n  useLayoutEffect(function () {\n    if (mergedContainerWidth && typeof mergedRestWidth === 'number' && mergedData) {\n      var totalWidth = suffixWidth;\n      var len = mergedData.length;\n      var lastIndex = len - 1;\n\n      // When data count change to 0, reset this since not loop will reach\n      if (!len) {\n        updateDisplayCount(0, null);\n        return;\n      }\n      for (var i = 0; i < len; i += 1) {\n        var currentItemWidth = getItemWidth(i);\n\n        // Fully will always render\n        if (fullySSR) {\n          currentItemWidth = currentItemWidth || 0;\n        }\n\n        // Break since data not ready\n        if (currentItemWidth === undefined) {\n          updateDisplayCount(i - 1, undefined, true);\n          break;\n        }\n\n        // Find best match\n        totalWidth += currentItemWidth;\n        if (\n        // Only one means `totalWidth` is the final width\n        lastIndex === 0 && totalWidth <= mergedContainerWidth ||\n        // Last two width will be the final width\n        i === lastIndex - 1 && totalWidth + getItemWidth(lastIndex) <= mergedContainerWidth) {\n          // Additional check if match the end\n          updateDisplayCount(lastIndex, null);\n          break;\n        } else if (totalWidth + mergedRestWidth > mergedContainerWidth) {\n          // Can not hold all the content to show rest\n          updateDisplayCount(i - 1, totalWidth - currentItemWidth - suffixWidth + restWidth);\n          break;\n        }\n      }\n      if (suffix && getItemWidth(0) + suffixWidth > mergedContainerWidth) {\n        setSuffixFixedStart(null);\n      }\n    }\n  }, [mergedContainerWidth, itemWidths, restWidth, suffixWidth, getKey, mergedData]);\n\n  // ================================ Render ================================\n  var displayRest = restReady && !!omittedItems.length;\n  var suffixStyle = {};\n  if (suffixFixedStart !== null && shouldResponsive) {\n    suffixStyle = {\n      position: 'absolute',\n      left: suffixFixedStart,\n      top: 0\n    };\n  }\n  var itemSharedProps = {\n    prefixCls: itemPrefixCls,\n    responsive: shouldResponsive,\n    component: itemComponent,\n    invalidate: invalidate\n  };\n\n  // >>>>> Choice render fun by `renderRawItem`\n  var internalRenderItemNode = renderRawItem ? function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n      key: key,\n      value: _objectSpread(_objectSpread({}, itemSharedProps), {}, {\n        order: index,\n        item: item,\n        itemKey: key,\n        registerSize: registerSize,\n        display: index <= mergedDisplayCount\n      })\n    }, renderRawItem(item, index));\n  } : function (item, index) {\n    var key = getKey(item, index);\n    return /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, {\n      order: index,\n      key: key,\n      item: item,\n      renderItem: mergedRenderItem,\n      itemKey: key,\n      registerSize: registerSize,\n      display: index <= mergedDisplayCount\n    }));\n  };\n\n  // >>>>> Rest node\n  var restContextProps = {\n    order: displayRest ? mergedDisplayCount : Number.MAX_SAFE_INTEGER,\n    className: \"\".concat(itemPrefixCls, \"-rest\"),\n    registerSize: registerOverflowSize,\n    display: displayRest\n  };\n  var mergedRenderRest = renderRest || defaultRenderRest;\n  var restNode = renderRawRest ? /*#__PURE__*/React.createElement(OverflowContext.Provider, {\n    value: _objectSpread(_objectSpread({}, itemSharedProps), restContextProps)\n  }, renderRawRest(omittedItems)) : /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, restContextProps), typeof mergedRenderRest === 'function' ? mergedRenderRest(omittedItems) : mergedRenderRest);\n  var overflowNode = /*#__PURE__*/React.createElement(Component, _extends({\n    className: classNames(!invalidate && prefixCls, className),\n    style: style,\n    ref: ref\n  }, restProps), mergedData.map(internalRenderItemNode), showRest ? restNode : null, suffix && /*#__PURE__*/React.createElement(Item, _extends({}, itemSharedProps, {\n    responsive: isResponsive,\n    responsiveDisabled: !shouldResponsive,\n    order: mergedDisplayCount,\n    className: \"\".concat(itemPrefixCls, \"-suffix\"),\n    registerSize: registerSuffixSize,\n    display: true,\n    style: suffixStyle\n  }), suffix));\n  return isResponsive ? /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onOverflowResize,\n    disabled: !shouldResponsive\n  }, overflowNode) : overflowNode;\n}\nvar ForwardOverflow = /*#__PURE__*/React.forwardRef(Overflow);\nForwardOverflow.displayName = 'Overflow';\nForwardOverflow.Item = RawItem;\nForwardOverflow.RESPONSIVE = RESPONSIVE;\nForwardOverflow.INVALIDATE = INVALIDATE;\n\n// Convert to generic type\nexport default ForwardOverflow;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY,EAAE,eAAe,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe,EAAE,iBAAiB,CAAC;AAC/N,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,OAAO;AACtD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,cAAc,IAAIC,UAAU,QAAQ,wBAAwB;AACnE,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,eAAe,QAAQ,WAAW;AAC3C,IAAIC,UAAU,GAAG,YAAY;AAC7B,IAAIC,UAAU,GAAG,YAAY;AAC7B,SAASF,eAAe,QAAQ,WAAW;AAC3C,SAASG,iBAAiBA,CAACC,YAAY,EAAE;EACvC,OAAO,IAAI,CAACC,MAAM,CAACD,YAAY,CAACE,MAAM,EAAE,MAAM,CAAC;AACjD;AACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC5B,IAAIC,gBAAgB,GAAGF,KAAK,CAACG,SAAS;IACpCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,gBAAgB;IAC1EE,WAAW,GAAGJ,KAAK,CAACK,IAAI;IACxBA,IAAI,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,WAAW;IAChDE,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,aAAa,GAAGP,KAAK,CAACO,aAAa;IACnCC,OAAO,GAAGR,KAAK,CAACQ,OAAO;IACvBC,gBAAgB,GAAGT,KAAK,CAACU,SAAS;IAClCA,SAAS,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,gBAAgB;IAC/DE,GAAG,GAAGX,KAAK,CAACW,GAAG;IACfC,KAAK,GAAGZ,KAAK,CAACY,KAAK;IACnBC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,QAAQ,GAAGd,KAAK,CAACc,QAAQ;IACzBC,UAAU,GAAGf,KAAK,CAACe,UAAU;IAC7BC,aAAa,GAAGhB,KAAK,CAACgB,aAAa;IACnCC,MAAM,GAAGjB,KAAK,CAACiB,MAAM;IACrBC,gBAAgB,GAAGlB,KAAK,CAACmB,SAAS;IAClCC,SAAS,GAAGF,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IAClEG,aAAa,GAAGrB,KAAK,CAACqB,aAAa;IACnCC,eAAe,GAAGtB,KAAK,CAACsB,eAAe;IACvCC,SAAS,GAAG5C,wBAAwB,CAACqB,KAAK,EAAEpB,SAAS,CAAC;EACxD,IAAI4C,QAAQ,GAAGb,GAAG,KAAK,MAAM;EAC7B,IAAIc,kBAAkB,GAAGnC,UAAU,CAAC,CAAC;EACrC,IAAIoC,eAAe,GAAGrC,cAAc,CAACoC,kBAAkB,EAAE,IAAI,CAAC;IAC5DE,gBAAgB,GAAGjD,cAAc,CAACgD,eAAe,EAAE,CAAC,CAAC;IACrDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzC,IAAIG,oBAAoB,GAAGF,cAAc,IAAI,CAAC;EAC9C,IAAIG,gBAAgB,GAAG1C,cAAc,CAACoC,kBAAkB,EAAE,IAAIO,GAAG,CAAC,CAAC,CAAC;IAClEC,gBAAgB,GAAGvD,cAAc,CAACqD,gBAAgB,EAAE,CAAC,CAAC;IACtDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,gBAAgB,GAAG/C,cAAc,CAACoC,kBAAkB,EAAE,CAAC,CAAC;IAC1DY,gBAAgB,GAAG3D,cAAc,CAAC0D,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIG,gBAAgB,GAAGnD,cAAc,CAACoC,kBAAkB,EAAE,CAAC,CAAC;IAC1DgB,gBAAgB,GAAG/D,cAAc,CAAC8D,gBAAgB,EAAE,CAAC,CAAC;IACtDE,SAAS,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,gBAAgB,GAAGvD,cAAc,CAACoC,kBAAkB,EAAE,CAAC,CAAC;IAC1DoB,iBAAiB,GAAGnE,cAAc,CAACkE,gBAAgB,EAAE,CAAC,CAAC;IACvDE,WAAW,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IAClCE,cAAc,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EACvC,IAAIG,SAAS,GAAGlE,QAAQ,CAAC,IAAI,CAAC;IAC5BmE,UAAU,GAAGvE,cAAc,CAACsE,SAAS,EAAE,CAAC,CAAC;IACzCE,gBAAgB,GAAGD,UAAU,CAAC,CAAC,CAAC;IAChCE,mBAAmB,GAAGF,UAAU,CAAC,CAAC,CAAC;EACrC,IAAIG,UAAU,GAAGtE,QAAQ,CAAC,IAAI,CAAC;IAC7BuE,UAAU,GAAG3E,cAAc,CAAC0E,UAAU,EAAE,CAAC,CAAC;IAC1CE,YAAY,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5BE,eAAe,GAAGF,UAAU,CAAC,CAAC,CAAC;EACjC,IAAIG,kBAAkB,GAAG3E,KAAK,CAACE,OAAO,CAAC,YAAY;IACjD,IAAIuE,YAAY,KAAK,IAAI,IAAI9B,QAAQ,EAAE;MACrC,OAAOiC,MAAM,CAACC,gBAAgB;IAChC;IACA,OAAOJ,YAAY,IAAI,CAAC;EAC1B,CAAC,EAAE,CAACA,YAAY,EAAE1B,cAAc,CAAC,CAAC;EAClC,IAAI+B,UAAU,GAAG7E,QAAQ,CAAC,KAAK,CAAC;IAC9B8E,UAAU,GAAGlF,cAAc,CAACiF,UAAU,EAAE,CAAC,CAAC;IAC1CE,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzBE,YAAY,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC9B,IAAIG,aAAa,GAAG,EAAE,CAAClE,MAAM,CAACM,SAAS,EAAE,OAAO,CAAC;;EAEjD;EACA,IAAI6D,eAAe,GAAGC,IAAI,CAACC,GAAG,CAAC5B,aAAa,EAAEI,SAAS,CAAC;;EAExD;EACA,IAAIyB,YAAY,GAAGrD,QAAQ,KAAKrB,UAAU;EAC1C,IAAI2E,gBAAgB,GAAG/D,IAAI,CAACP,MAAM,IAAIqE,YAAY;EAClD,IAAIE,UAAU,GAAGvD,QAAQ,KAAKpB,UAAU;;EAExC;AACF;AACA;EACE,IAAI4E,QAAQ,GAAGF,gBAAgB,IAAI,OAAOtD,QAAQ,KAAK,QAAQ,IAAIT,IAAI,CAACP,MAAM,GAAGgB,QAAQ;EACzF,IAAIyD,UAAU,GAAGxF,OAAO,CAAC,YAAY;IACnC,IAAIyF,KAAK,GAAGnE,IAAI;IAChB,IAAI+D,gBAAgB,EAAE;MACpB,IAAIxC,cAAc,KAAK,IAAI,IAAIJ,QAAQ,EAAE;QACvCgD,KAAK,GAAGnE,IAAI;MACd,CAAC,MAAM;QACLmE,KAAK,GAAGnE,IAAI,CAACoE,KAAK,CAAC,CAAC,EAAER,IAAI,CAACS,GAAG,CAACrE,IAAI,CAACP,MAAM,EAAEgC,oBAAoB,GAAGpB,SAAS,CAAC,CAAC;MAChF;IACF,CAAC,MAAM,IAAI,OAAOI,QAAQ,KAAK,QAAQ,EAAE;MACvC0D,KAAK,GAAGnE,IAAI,CAACoE,KAAK,CAAC,CAAC,EAAE3D,QAAQ,CAAC;IACjC;IACA,OAAO0D,KAAK;EACd,CAAC,EAAE,CAACnE,IAAI,EAAEK,SAAS,EAAEkB,cAAc,EAAEd,QAAQ,EAAEsD,gBAAgB,CAAC,CAAC;EACjE,IAAIxE,YAAY,GAAGb,OAAO,CAAC,YAAY;IACrC,IAAIqF,gBAAgB,EAAE;MACpB,OAAO/D,IAAI,CAACoE,KAAK,CAACjB,kBAAkB,GAAG,CAAC,CAAC;IAC3C;IACA,OAAOnD,IAAI,CAACoE,KAAK,CAACF,UAAU,CAACzE,MAAM,CAAC;EACtC,CAAC,EAAE,CAACO,IAAI,EAAEkE,UAAU,EAAEH,gBAAgB,EAAEZ,kBAAkB,CAAC,CAAC;;EAE5D;EACA,IAAImB,MAAM,GAAG3F,WAAW,CAAC,UAAU4F,IAAI,EAAEC,KAAK,EAAE;IAC9C,IAAIC,IAAI;IACR,IAAI,OAAOtE,OAAO,KAAK,UAAU,EAAE;MACjC,OAAOA,OAAO,CAACoE,IAAI,CAAC;IACtB;IACA,OAAO,CAACE,IAAI,GAAGtE,OAAO,KAAKoE,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACpE,OAAO,CAAC,CAAC,MAAM,IAAI,IAAIsE,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAGD,KAAK;EACnI,CAAC,EAAE,CAACrE,OAAO,CAAC,CAAC;EACb,IAAIuE,gBAAgB,GAAG/F,WAAW,CAACsB,UAAU,IAAI,UAAUsE,IAAI,EAAE;IAC/D,OAAOA,IAAI;EACb,CAAC,EAAE,CAACtE,UAAU,CAAC,CAAC;EAChB,SAAS0E,kBAAkBA,CAACC,KAAK,EAAEC,mBAAmB,EAAEC,QAAQ,EAAE;IAChE;IACA;IACA;IACA,IAAI7B,YAAY,KAAK2B,KAAK,KAAKC,mBAAmB,KAAKE,SAAS,IAAIF,mBAAmB,KAAKhC,gBAAgB,CAAC,EAAE;MAC7G;IACF;IACAK,eAAe,CAAC0B,KAAK,CAAC;IACtB,IAAI,CAACE,QAAQ,EAAE;MACbrB,YAAY,CAACmB,KAAK,GAAG5E,IAAI,CAACP,MAAM,GAAG,CAAC,CAAC;MACrCwB,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,IAAIA,eAAe,CAAC2D,KAAK,CAAC;IAClF;IACA,IAAIC,mBAAmB,KAAKE,SAAS,EAAE;MACrCjC,mBAAmB,CAAC+B,mBAAmB,CAAC;IAC1C;EACF;;EAEA;EACA,SAASG,gBAAgBA,CAACC,CAAC,EAAEC,OAAO,EAAE;IACpC1D,iBAAiB,CAAC0D,OAAO,CAACC,WAAW,CAAC;EACxC;EACA,SAASC,YAAYA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAChCxD,aAAa,CAAC,UAAUyD,MAAM,EAAE;MAC9B,IAAIC,KAAK,GAAG,IAAI7D,GAAG,CAAC4D,MAAM,CAAC;MAC3B,IAAID,KAAK,KAAK,IAAI,EAAE;QAClBE,KAAK,CAACC,MAAM,CAACJ,GAAG,CAAC;MACnB,CAAC,MAAM;QACLG,KAAK,CAACE,GAAG,CAACL,GAAG,EAAEC,KAAK,CAAC;MACvB;MACA,OAAOE,KAAK;IACd,CAAC,CAAC;EACJ;EACA,SAASG,oBAAoBA,CAACV,CAAC,EAAEK,KAAK,EAAE;IACtChD,YAAY,CAACgD,KAAK,CAAC;IACnBpD,gBAAgB,CAACG,SAAS,CAAC;EAC7B;EACA,SAASuD,kBAAkBA,CAACX,CAAC,EAAEK,KAAK,EAAE;IACpC5C,cAAc,CAAC4C,KAAK,CAAC;EACvB;;EAEA;EACA,SAASO,YAAYA,CAACrB,KAAK,EAAE;IAC3B,OAAO3C,UAAU,CAACiE,GAAG,CAACxB,MAAM,CAACJ,UAAU,CAACM,KAAK,CAAC,EAAEA,KAAK,CAAC,CAAC;EACzD;EACA1F,eAAe,CAAC,YAAY;IAC1B,IAAI2C,oBAAoB,IAAI,OAAOkC,eAAe,KAAK,QAAQ,IAAIO,UAAU,EAAE;MAC7E,IAAI6B,UAAU,GAAGtD,WAAW;MAC5B,IAAIuD,GAAG,GAAG9B,UAAU,CAACzE,MAAM;MAC3B,IAAIwG,SAAS,GAAGD,GAAG,GAAG,CAAC;;MAEvB;MACA,IAAI,CAACA,GAAG,EAAE;QACRrB,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAAC;QAC3B;MACF;MACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;QAC/B,IAAIC,gBAAgB,GAAGN,YAAY,CAACK,CAAC,CAAC;;QAEtC;QACA,IAAI/E,QAAQ,EAAE;UACZgF,gBAAgB,GAAGA,gBAAgB,IAAI,CAAC;QAC1C;;QAEA;QACA,IAAIA,gBAAgB,KAAKpB,SAAS,EAAE;UAClCJ,kBAAkB,CAACuB,CAAC,GAAG,CAAC,EAAEnB,SAAS,EAAE,IAAI,CAAC;UAC1C;QACF;;QAEA;QACAgB,UAAU,IAAII,gBAAgB;QAC9B;QACA;QACAF,SAAS,KAAK,CAAC,IAAIF,UAAU,IAAItE,oBAAoB;QACrD;QACAyE,CAAC,KAAKD,SAAS,GAAG,CAAC,IAAIF,UAAU,GAAGF,YAAY,CAACI,SAAS,CAAC,IAAIxE,oBAAoB,EAAE;UACnF;UACAkD,kBAAkB,CAACsB,SAAS,EAAE,IAAI,CAAC;UACnC;QACF,CAAC,MAAM,IAAIF,UAAU,GAAGpC,eAAe,GAAGlC,oBAAoB,EAAE;UAC9D;UACAkD,kBAAkB,CAACuB,CAAC,GAAG,CAAC,EAAEH,UAAU,GAAGI,gBAAgB,GAAG1D,WAAW,GAAGJ,SAAS,CAAC;UAClF;QACF;MACF;MACA,IAAIzB,MAAM,IAAIiF,YAAY,CAAC,CAAC,CAAC,GAAGpD,WAAW,GAAGhB,oBAAoB,EAAE;QAClEqB,mBAAmB,CAAC,IAAI,CAAC;MAC3B;IACF;EACF,CAAC,EAAE,CAACrB,oBAAoB,EAAEI,UAAU,EAAEQ,SAAS,EAAEI,WAAW,EAAE6B,MAAM,EAAEJ,UAAU,CAAC,CAAC;;EAElF;EACA,IAAIkC,WAAW,GAAG5C,SAAS,IAAI,CAAC,CAACjE,YAAY,CAACE,MAAM;EACpD,IAAI4G,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIxD,gBAAgB,KAAK,IAAI,IAAIkB,gBAAgB,EAAE;IACjDsC,WAAW,GAAG;MACZC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE1D,gBAAgB;MACtB2D,GAAG,EAAE;IACP,CAAC;EACH;EACA,IAAIC,eAAe,GAAG;IACpB3G,SAAS,EAAE4D,aAAa;IACxBgD,UAAU,EAAE3C,gBAAgB;IAC5BjD,SAAS,EAAEE,aAAa;IACxBgD,UAAU,EAAEA;EACd,CAAC;;EAED;EACA,IAAI2C,sBAAsB,GAAGzG,aAAa,GAAG,UAAUqE,IAAI,EAAEC,KAAK,EAAE;IAClE,IAAIa,GAAG,GAAGf,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC7B,OAAO,aAAahG,KAAK,CAACoI,aAAa,CAACzH,eAAe,CAAC0H,QAAQ,EAAE;MAChExB,GAAG,EAAEA,GAAG;MACRyB,KAAK,EAAE1I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqI,eAAe,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3DM,KAAK,EAAEvC,KAAK;QACZD,IAAI,EAAEA,IAAI;QACVpE,OAAO,EAAEkF,GAAG;QACZD,YAAY,EAAEA,YAAY;QAC1B4B,OAAO,EAAExC,KAAK,IAAIrB;MACpB,CAAC;IACH,CAAC,EAAEjD,aAAa,CAACqE,IAAI,EAAEC,KAAK,CAAC,CAAC;EAChC,CAAC,GAAG,UAAUD,IAAI,EAAEC,KAAK,EAAE;IACzB,IAAIa,GAAG,GAAGf,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC;IAC7B,OAAO,aAAahG,KAAK,CAACoI,aAAa,CAAC7H,IAAI,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEsI,eAAe,EAAE;MAC1EM,KAAK,EAAEvC,KAAK;MACZa,GAAG,EAAEA,GAAG;MACRd,IAAI,EAAEA,IAAI;MACVtE,UAAU,EAAEyE,gBAAgB;MAC5BvE,OAAO,EAAEkF,GAAG;MACZD,YAAY,EAAEA,YAAY;MAC1B4B,OAAO,EAAExC,KAAK,IAAIrB;IACpB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,IAAI8D,gBAAgB,GAAG;IACrBF,KAAK,EAAEX,WAAW,GAAGjD,kBAAkB,GAAGC,MAAM,CAACC,gBAAgB;IACjE7C,SAAS,EAAE,EAAE,CAAChB,MAAM,CAACkE,aAAa,EAAE,OAAO,CAAC;IAC5C0B,YAAY,EAAEO,oBAAoB;IAClCqB,OAAO,EAAEZ;EACX,CAAC;EACD,IAAIc,gBAAgB,GAAGxG,UAAU,IAAIpB,iBAAiB;EACtD,IAAI6H,QAAQ,GAAGxG,aAAa,GAAG,aAAanC,KAAK,CAACoI,aAAa,CAACzH,eAAe,CAAC0H,QAAQ,EAAE;IACxFC,KAAK,EAAE1I,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqI,eAAe,CAAC,EAAEQ,gBAAgB;EAC3E,CAAC,EAAEtG,aAAa,CAACpB,YAAY,CAAC,CAAC,GAAG,aAAaf,KAAK,CAACoI,aAAa,CAAC7H,IAAI,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEsI,eAAe,EAAEQ,gBAAgB,CAAC,EAAE,OAAOC,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAAC3H,YAAY,CAAC,GAAG2H,gBAAgB,CAAC;EACrN,IAAIE,YAAY,GAAG,aAAa5I,KAAK,CAACoI,aAAa,CAAC7F,SAAS,EAAE5C,QAAQ,CAAC;IACtEqC,SAAS,EAAE5B,UAAU,CAAC,CAACoF,UAAU,IAAIlE,SAAS,EAAEU,SAAS,CAAC;IAC1DD,KAAK,EAAEA,KAAK;IACZX,GAAG,EAAEA;EACP,CAAC,EAAEsB,SAAS,CAAC,EAAEgD,UAAU,CAACmD,GAAG,CAACV,sBAAsB,CAAC,EAAE1C,QAAQ,GAAGkD,QAAQ,GAAG,IAAI,EAAEvG,MAAM,IAAI,aAAapC,KAAK,CAACoI,aAAa,CAAC7H,IAAI,EAAEZ,QAAQ,CAAC,CAAC,CAAC,EAAEsI,eAAe,EAAE;IAChKC,UAAU,EAAE5C,YAAY;IACxBwD,kBAAkB,EAAE,CAACvD,gBAAgB;IACrCgD,KAAK,EAAE5D,kBAAkB;IACzB3C,SAAS,EAAE,EAAE,CAAChB,MAAM,CAACkE,aAAa,EAAE,SAAS,CAAC;IAC9C0B,YAAY,EAAEQ,kBAAkB;IAChCoB,OAAO,EAAE,IAAI;IACbzG,KAAK,EAAE8F;EACT,CAAC,CAAC,EAAEzF,MAAM,CAAC,CAAC;EACZ,OAAOkD,YAAY,GAAG,aAAatF,KAAK,CAACoI,aAAa,CAAC/H,cAAc,EAAE;IACrE0I,QAAQ,EAAEvC,gBAAgB;IAC1BwC,QAAQ,EAAE,CAACzD;EACb,CAAC,EAAEqD,YAAY,CAAC,GAAGA,YAAY;AACjC;AACA,IAAIK,eAAe,GAAG,aAAajJ,KAAK,CAACkJ,UAAU,CAAChI,QAAQ,CAAC;AAC7D+H,eAAe,CAACE,WAAW,GAAG,UAAU;AACxCF,eAAe,CAAC1I,IAAI,GAAGG,OAAO;AAC9BuI,eAAe,CAACrI,UAAU,GAAGA,UAAU;AACvCqI,eAAe,CAACpI,UAAU,GAAGA,UAAU;;AAEvC;AACA,eAAeoI,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}