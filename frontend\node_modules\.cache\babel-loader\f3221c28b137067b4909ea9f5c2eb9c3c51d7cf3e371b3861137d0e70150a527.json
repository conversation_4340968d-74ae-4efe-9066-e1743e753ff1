{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\VolumeManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Card, Button, Space, Typography, Row, Col, Progress, Tag, Descriptions, message, Collapse } from 'antd';\nimport { EditOutlined, ExportOutlined, CaretRightOutlined, FileTextOutlined, BookOutlined, PlusOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TabPane\n} = Tabs;\nconst {\n  Panel\n} = Collapse;\nconst VolumeManagement = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [volumes, setVolumes] = useState([]);\n\n  // 模拟数据\n  const mockVolumes = [{\n    id: 1,\n    title: '第一卷：初入江湖',\n    description: '主角初入江湖的故事',\n    chapterCount: 12,\n    wordCount: 120000,\n    status: '已完成',\n    progress: 100\n  }, {\n    id: 2,\n    title: '第二卷：修炼之路',\n    description: '主角踏上修炼之路',\n    chapterCount: 8,\n    wordCount: 80000,\n    status: '进行中',\n    progress: 60\n  }];\n  useEffect(() => {\n    loadVolumes();\n  }, [id]);\n  const loadVolumes = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setVolumes(mockVolumes);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      message.error('加载卷宗列表失败');\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '24px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {\n          style: {\n            marginRight: '8px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), \"\\u5377\\u5B97\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: \"\\u7BA1\\u7406\\u5C0F\\u8BF4\\u7684\\u5377\\u5B97\\u548C\\u7AE0\\u8282\\u7ED3\\u6784\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      defaultActiveKey: ['volumes', 'chapters'],\n      expandIcon: ({\n        isActive\n      }) => /*#__PURE__*/_jsxDEV(CaretRightOutlined, {\n        rotate: isActive ? 90 : 0\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 39\n      }, this),\n      ghost: true,\n      children: [/*#__PURE__*/_jsxDEV(Panel, {\n        header: \"\\u5377\\u5B97\\u5217\\u8868\",\n        extra: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 51\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 42\n            }, this),\n            children: \"\\u65B0\\u5EFA\\u5377\\u5B97\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: mockVolumes.map(volume => /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: volume.title,\n              extra: /*#__PURE__*/_jsxDEV(Space, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 49\n                  }, this),\n                  size: \"small\",\n                  children: \"\\u7F16\\u8F91\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 21\n              }, this),\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                children: \"\\u7AE0\\u8282\\u7BA1\\u7406\"\n              }, \"chapters\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                children: \"\\u5BFC\\u51FA\"\n              }, \"export\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 21\n              }, this)],\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '12px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: volume.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 118,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u7AE0\\u8282\\u6570\\u91CF\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: volume.chapterCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u5B57\\u6570\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 127,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: volume.wordCount.toLocaleString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 126,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u72B6\\u6001\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 131,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                    color: volume.status === '已完成' ? 'green' : 'blue',\n                    children: volume.status\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u8FDB\\u5EA6\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 137,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                    percent: volume.progress,\n                    size: \"small\",\n                    style: {\n                      marginTop: '4px'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 121,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 17\n            }, this)\n          }, volume.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this)]\n      }, \"volumes\", true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Panel, {\n        header: \"\\u7AE0\\u8282\\u7BA1\\u7406\",\n        extra: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 52\n        }, this),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 42\n            }, this),\n            children: \"\\u65B0\\u5EFA\\u7AE0\\u8282\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Descriptions, {\n            title: \"\\u7AE0\\u8282\\u7EDF\\u8BA1\",\n            bordered: true,\n            column: 3,\n            children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u603B\\u7AE0\\u8282\\u6570\",\n              children: \"20\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5DF2\\u5B8C\\u6210\",\n              children: \"15\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u8FDB\\u884C\\u4E2D\",\n              children: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u603B\\u5B57\\u6570\",\n              children: \"200,000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5E73\\u5747\\u5B57\\u6570\",\n              children: \"10,000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n              label: \"\\u5B8C\\u6210\\u5EA6\",\n              children: \"75%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u7AE0\\u8282\\u8BE6\\u7EC6\\u7BA1\\u7406\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)]\n      }, \"chapters\", true, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Panel, {\n        header: \"\\u5BFC\\u51FA\\u4E0E\\u53D1\\u5E03\",\n        extra: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 51\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u5BFC\\u51FA\\u683C\\u5F0F\",\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                children: \"\\u5BFC\\u51FA\"\n              }, \"export\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 19\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  block: true,\n                  children: \"\\u5BFC\\u51FA\\u4E3A TXT\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  block: true,\n                  children: \"\\u5BFC\\u51FA\\u4E3A DOCX\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  block: true,\n                  children: \"\\u5BFC\\u51FA\\u4E3A PDF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  block: true,\n                  children: \"\\u5BFC\\u51FA\\u4E3A EPUB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u53D1\\u5E03\\u5E73\\u53F0\",\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                children: \"\\u53D1\\u5E03\"\n              }, \"publish\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  block: true,\n                  children: \"\\u8D77\\u70B9\\u4E2D\\u6587\\u7F51\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  block: true,\n                  children: \"\\u664B\\u6C5F\\u6587\\u5B66\\u57CE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  block: true,\n                  children: \"\\u7EB5\\u6A2A\\u4E2D\\u6587\\u7F51\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  block: true,\n                  children: \"\\u81EA\\u5B9A\\u4E49\\u5E73\\u53F0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: \"\\u7248\\u672C\\u7BA1\\u7406\",\n              actions: [/*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                children: \"\\u7BA1\\u7406\"\n              }, \"version\", false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 19\n              }, this)],\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u5F53\\u524D\\u7248\\u672C\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: \"v1.2.0\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 220,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    children: \"\\u6700\\u540E\\u66F4\\u65B0\\uFF1A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    children: \"2024-01-15\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  block: true,\n                  children: \"\\u521B\\u5EFA\\u65B0\\u7248\\u672C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  block: true,\n                  children: \"\\u7248\\u672C\\u5386\\u53F2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)\n      }, \"export\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 75,\n    columnNumber: 5\n  }, this);\n};\n_s(VolumeManagement, \"8cNT2U+a3C3X+qagrvF4JaF4Lvc=\", false, function () {\n  return [useParams];\n});\n_c = VolumeManagement;\nexport default VolumeManagement;\nvar _c;\n$RefreshReg$(_c, \"VolumeManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Card", "<PERSON><PERSON>", "Space", "Typography", "Row", "Col", "Progress", "Tag", "Descriptions", "message", "Collapse", "EditOutlined", "ExportOutlined", "CaretRightOutlined", "FileTextOutlined", "BookOutlined", "PlusOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TabPane", "Tabs", "Panel", "VolumeManagement", "_s", "id", "loading", "setLoading", "volumes", "setVolumes", "mockVolumes", "title", "description", "chapterCount", "wordCount", "status", "progress", "loadVolumes", "setTimeout", "error", "style", "padding", "children", "marginBottom", "level", "marginRight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "defaultActiveKey", "expandIcon", "isActive", "rotate", "ghost", "header", "extra", "icon", "gutter", "map", "volume", "xs", "sm", "md", "size", "actions", "direction", "width", "display", "justifyContent", "strong", "toLocaleString", "color", "percent", "marginTop", "bordered", "column", "<PERSON><PERSON>", "label", "block", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/VolumeManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport {\n  Card,\n  Button,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Progress,\n  Tag,\n  Descriptions,\n  message,\n  Collapse\n} from 'antd';\nimport {\n  EditOutlined,\n  ExportOutlined,\n  CaretRightOutlined,\n  FileTextOutlined,\n  BookOutlined,\n  PlusOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TabPane } = Tabs;\nconst { Panel } = Collapse;\n\nconst VolumeManagement = () => {\n  const { id } = useParams();\n  const [loading, setLoading] = useState(false);\n  const [volumes, setVolumes] = useState([]);\n\n  // 模拟数据\n  const mockVolumes = [\n    {\n      id: 1,\n      title: '第一卷：初入江湖',\n      description: '主角初入江湖的故事',\n      chapterCount: 12,\n      wordCount: 120000,\n      status: '已完成',\n      progress: 100\n    },\n    {\n      id: 2,\n      title: '第二卷：修炼之路',\n      description: '主角踏上修炼之路',\n      chapterCount: 8,\n      wordCount: 80000,\n      status: '进行中',\n      progress: 60\n    }\n  ];\n\n  useEffect(() => {\n    loadVolumes();\n  }, [id]);\n\n  const loadVolumes = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setVolumes(mockVolumes);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      message.error('加载卷宗列表失败');\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2}>\n          <FileTextOutlined style={{ marginRight: '8px' }} />\n          卷宗管理\n        </Title>\n        <Text type=\"secondary\">管理小说的卷宗和章节结构</Text>\n      </div>\n\n      <Collapse\n        defaultActiveKey={['volumes', 'chapters']}\n        expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}\n        ghost\n      >\n        <Panel header=\"卷宗列表\" key=\"volumes\" extra={<BookOutlined />}>\n          <div style={{ marginBottom: '16px' }}>\n            <Button type=\"primary\" icon={<PlusOutlined />}>\n              新建卷宗\n            </Button>\n          </div>\n\n          <Row gutter={[16, 16]}>\n            {mockVolumes.map(volume => (\n              <Col xs={24} sm={12} md={8} key={volume.id}>\n                <Card\n                  title={volume.title}\n                  extra={\n                    <Space>\n                      <Button type=\"link\" icon={<EditOutlined />} size=\"small\">\n                        编辑\n                      </Button>\n                    </Space>\n                  }\n                  actions={[\n                    <Button type=\"link\" key=\"chapters\">\n                      章节管理\n                    </Button>,\n                    <Button type=\"link\" key=\"export\">\n                      导出\n                    </Button>\n                  ]}\n                >\n                  <div style={{ marginBottom: '12px' }}>\n                    <Text type=\"secondary\">{volume.description}</Text>\n                  </div>\n\n                  <Space direction=\"vertical\" style={{ width: '100%' }}>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <Text>章节数量：</Text>\n                      <Text strong>{volume.chapterCount}</Text>\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <Text>字数：</Text>\n                      <Text strong>{volume.wordCount.toLocaleString()}</Text>\n                    </div>\n                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                      <Text>状态：</Text>\n                      <Tag color={volume.status === '已完成' ? 'green' : 'blue'}>\n                        {volume.status}\n                      </Tag>\n                    </div>\n                    <div>\n                      <Text>进度：</Text>\n                      <Progress\n                        percent={volume.progress}\n                        size=\"small\"\n                        style={{ marginTop: '4px' }}\n                      />\n                    </div>\n                  </Space>\n                </Card>\n              </Col>\n            ))}\n          </Row>\n        </Panel>\n\n        <Panel header=\"章节管理\" key=\"chapters\" extra={<FileTextOutlined />}>\n          <div style={{ marginBottom: '16px' }}>\n            <Button type=\"primary\" icon={<PlusOutlined />}>\n              新建章节\n            </Button>\n          </div>\n\n          <Card>\n            <Descriptions title=\"章节统计\" bordered column={3}>\n              <Descriptions.Item label=\"总章节数\">20</Descriptions.Item>\n              <Descriptions.Item label=\"已完成\">15</Descriptions.Item>\n              <Descriptions.Item label=\"进行中\">3</Descriptions.Item>\n              <Descriptions.Item label=\"总字数\">200,000</Descriptions.Item>\n              <Descriptions.Item label=\"平均字数\">10,000</Descriptions.Item>\n              <Descriptions.Item label=\"完成度\">75%</Descriptions.Item>\n            </Descriptions>\n          </Card>\n\n          <div style={{ marginTop: '16px' }}>\n            <Text type=\"secondary\">\n              章节详细管理功能正在开发中...\n            </Text>\n          </div>\n        </Panel>\n\n        <Panel header=\"导出与发布\" key=\"export\" extra={<ExportOutlined />}>\n          <Row gutter={[16, 16]}>\n            <Col xs={24} sm={12} md={8}>\n              <Card\n                title=\"导出格式\"\n                actions={[\n                  <Button type=\"primary\" key=\"export\">导出</Button>\n                ]}\n              >\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <Button block>导出为 TXT</Button>\n                  <Button block>导出为 DOCX</Button>\n                  <Button block>导出为 PDF</Button>\n                  <Button block>导出为 EPUB</Button>\n                </Space>\n              </Card>\n            </Col>\n\n            <Col xs={24} sm={12} md={8}>\n              <Card\n                title=\"发布平台\"\n                actions={[\n                  <Button type=\"primary\" key=\"publish\">发布</Button>\n                ]}\n              >\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <Button block>起点中文网</Button>\n                  <Button block>晋江文学城</Button>\n                  <Button block>纵横中文网</Button>\n                  <Button block>自定义平台</Button>\n                </Space>\n              </Card>\n            </Col>\n\n            <Col xs={24} sm={12} md={8}>\n              <Card\n                title=\"版本管理\"\n                actions={[\n                  <Button type=\"primary\" key=\"version\">管理</Button>\n                ]}\n              >\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <Text>当前版本：</Text>\n                    <Text strong>v1.2.0</Text>\n                  </div>\n                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n                    <Text>最后更新：</Text>\n                    <Text>2024-01-15</Text>\n                  </div>\n                  <Button block>创建新版本</Button>\n                  <Button block>版本历史</Button>\n                </Space>\n              </Card>\n            </Col>\n          </Row>\n        </Panel>\n      </Collapse>\n    </div>\n  );\n};\n\nexport default VolumeManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SACEC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,QAAQ,EACRC,GAAG,EACHC,YAAY,EACZC,OAAO,EACPC,QAAQ,QACH,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,gBAAgB,EAChBC,YAAY,EACZC,YAAY,QACP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGjB,UAAU;AAClC,MAAM;EAAEkB;AAAQ,CAAC,GAAGC,IAAI;AACxB,MAAM;EAAEC;AAAM,CAAC,GAAGb,QAAQ;AAE1B,MAAMc,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM;IAAEC;EAAG,CAAC,GAAG3B,SAAS,CAAC,CAAC;EAC1B,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAMkC,WAAW,GAAG,CAClB;IACEL,EAAE,EAAE,CAAC;IACLM,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,WAAW;IACxBC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLM,KAAK,EAAE,UAAU;IACjBC,WAAW,EAAE,UAAU;IACvBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,KAAK;IAChBC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE;EACZ,CAAC,CACF;EAEDvC,SAAS,CAAC,MAAM;IACdwC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACZ,EAAE,CAAC,CAAC;EAER,MAAMY,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BV,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACAW,UAAU,CAAC,MAAM;QACfT,UAAU,CAACC,WAAW,CAAC;QACvBH,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOY,KAAK,EAAE;MACd/B,OAAO,CAAC+B,KAAK,CAAC,UAAU,CAAC;MACzBZ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAKuB,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9BzB,OAAA;MAAKuB,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAO,CAAE;MAAAD,QAAA,gBACnCzB,OAAA,CAACC,KAAK;QAAC0B,KAAK,EAAE,CAAE;QAAAF,QAAA,gBACdzB,OAAA,CAACJ,gBAAgB;UAAC2B,KAAK,EAAE;YAAEK,WAAW,EAAE;UAAM;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAErD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRhC,OAAA,CAACE,IAAI;QAAC+B,IAAI,EAAC,WAAW;QAAAR,QAAA,EAAC;MAAY;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAENhC,OAAA,CAACR,QAAQ;MACP0C,gBAAgB,EAAE,CAAC,SAAS,EAAE,UAAU,CAAE;MAC1CC,UAAU,EAAEA,CAAC;QAAEC;MAAS,CAAC,kBAAKpC,OAAA,CAACL,kBAAkB;QAAC0C,MAAM,EAAED,QAAQ,GAAG,EAAE,GAAG;MAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAChFM,KAAK;MAAAb,QAAA,gBAELzB,OAAA,CAACK,KAAK;QAACkC,MAAM,EAAC,0BAAM;QAAeC,KAAK,eAAExC,OAAA,CAACH,YAAY;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAP,QAAA,gBACzDzB,OAAA;UAAKuB,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAD,QAAA,eACnCzB,OAAA,CAACjB,MAAM;YAACkD,IAAI,EAAC,SAAS;YAACQ,IAAI,eAAEzC,OAAA,CAACF,YAAY;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAP,QAAA,EAAC;UAE/C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhC,OAAA,CAACd,GAAG;UAACwD,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAjB,QAAA,EACnBZ,WAAW,CAAC8B,GAAG,CAACC,MAAM,iBACrB5C,OAAA,CAACb,GAAG;YAAC0D,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACzBzB,OAAA,CAAClB,IAAI;cACHgC,KAAK,EAAE8B,MAAM,CAAC9B,KAAM;cACpB0B,KAAK,eACHxC,OAAA,CAAChB,KAAK;gBAAAyC,QAAA,eACJzB,OAAA,CAACjB,MAAM;kBAACkD,IAAI,EAAC,MAAM;kBAACQ,IAAI,eAAEzC,OAAA,CAACP,YAAY;oBAAAoC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAACgB,IAAI,EAAC,OAAO;kBAAAvB,QAAA,EAAC;gBAEzD;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CACR;cACDiB,OAAO,EAAE,cACPjD,OAAA,CAACjB,MAAM;gBAACkD,IAAI,EAAC,MAAM;gBAAAR,QAAA,EAAgB;cAEnC,GAFwB,UAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE1B,CAAC,eACThC,OAAA,CAACjB,MAAM;gBAACkD,IAAI,EAAC,MAAM;gBAAAR,QAAA,EAAc;cAEjC,GAFwB,QAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAExB,CAAC,CACT;cAAAP,QAAA,gBAEFzB,OAAA;gBAAKuB,KAAK,EAAE;kBAAEG,YAAY,EAAE;gBAAO,CAAE;gBAAAD,QAAA,eACnCzB,OAAA,CAACE,IAAI;kBAAC+B,IAAI,EAAC,WAAW;kBAAAR,QAAA,EAAEmB,MAAM,CAAC7B;gBAAW;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eAENhC,OAAA,CAAChB,KAAK;gBAACkE,SAAS,EAAC,UAAU;gBAAC3B,KAAK,EAAE;kBAAE4B,KAAK,EAAE;gBAAO,CAAE;gBAAA1B,QAAA,gBACnDzB,OAAA;kBAAKuB,KAAK,EAAE;oBAAE6B,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE;kBAAgB,CAAE;kBAAA5B,QAAA,gBAC/DzB,OAAA,CAACE,IAAI;oBAAAuB,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBhC,OAAA,CAACE,IAAI;oBAACoD,MAAM;oBAAA7B,QAAA,EAAEmB,MAAM,CAAC5B;kBAAY;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC,eACNhC,OAAA;kBAAKuB,KAAK,EAAE;oBAAE6B,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE;kBAAgB,CAAE;kBAAA5B,QAAA,gBAC/DzB,OAAA,CAACE,IAAI;oBAAAuB,QAAA,EAAC;kBAAG;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChBhC,OAAA,CAACE,IAAI;oBAACoD,MAAM;oBAAA7B,QAAA,EAAEmB,MAAM,CAAC3B,SAAS,CAACsC,cAAc,CAAC;kBAAC;oBAAA1B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACNhC,OAAA;kBAAKuB,KAAK,EAAE;oBAAE6B,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE;kBAAgB,CAAE;kBAAA5B,QAAA,gBAC/DzB,OAAA,CAACE,IAAI;oBAAAuB,QAAA,EAAC;kBAAG;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChBhC,OAAA,CAACX,GAAG;oBAACmE,KAAK,EAAEZ,MAAM,CAAC1B,MAAM,KAAK,KAAK,GAAG,OAAO,GAAG,MAAO;oBAAAO,QAAA,EACpDmB,MAAM,CAAC1B;kBAAM;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACX,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNhC,OAAA;kBAAAyB,QAAA,gBACEzB,OAAA,CAACE,IAAI;oBAAAuB,QAAA,EAAC;kBAAG;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAChBhC,OAAA,CAACZ,QAAQ;oBACPqE,OAAO,EAAEb,MAAM,CAACzB,QAAS;oBACzB6B,IAAI,EAAC,OAAO;oBACZzB,KAAK,EAAE;sBAAEmC,SAAS,EAAE;oBAAM;kBAAE;oBAAA7B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC,GA/CwBY,MAAM,CAACpC,EAAE;YAAAqB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgDrC,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,GA3DiB,SAAS;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA4D3B,CAAC,eAERhC,OAAA,CAACK,KAAK;QAACkC,MAAM,EAAC,0BAAM;QAAgBC,KAAK,eAAExC,OAAA,CAACJ,gBAAgB;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAP,QAAA,gBAC9DzB,OAAA;UAAKuB,KAAK,EAAE;YAAEG,YAAY,EAAE;UAAO,CAAE;UAAAD,QAAA,eACnCzB,OAAA,CAACjB,MAAM;YAACkD,IAAI,EAAC,SAAS;YAACQ,IAAI,eAAEzC,OAAA,CAACF,YAAY;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAP,QAAA,EAAC;UAE/C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAENhC,OAAA,CAAClB,IAAI;UAAA2C,QAAA,eACHzB,OAAA,CAACV,YAAY;YAACwB,KAAK,EAAC,0BAAM;YAAC6C,QAAQ;YAACC,MAAM,EAAE,CAAE;YAAAnC,QAAA,gBAC5CzB,OAAA,CAACV,YAAY,CAACuE,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArC,QAAA,EAAC;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eACtDhC,OAAA,CAACV,YAAY,CAACuE,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAArC,QAAA,EAAC;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eACrDhC,OAAA,CAACV,YAAY,CAACuE,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAArC,QAAA,EAAC;YAAC;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eACpDhC,OAAA,CAACV,YAAY,CAACuE,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAArC,QAAA,EAAC;YAAO;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAC1DhC,OAAA,CAACV,YAAY,CAACuE,IAAI;cAACC,KAAK,EAAC,0BAAM;cAAArC,QAAA,EAAC;YAAM;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC,eAC1DhC,OAAA,CAACV,YAAY,CAACuE,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAArC,QAAA,EAAC;YAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAEPhC,OAAA;UAAKuB,KAAK,EAAE;YAAEmC,SAAS,EAAE;UAAO,CAAE;UAAAjC,QAAA,eAChCzB,OAAA,CAACE,IAAI;YAAC+B,IAAI,EAAC,WAAW;YAAAR,QAAA,EAAC;UAEvB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA,GAtBiB,UAAU;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuB5B,CAAC,eAERhC,OAAA,CAACK,KAAK;QAACkC,MAAM,EAAC,gCAAO;QAAcC,KAAK,eAAExC,OAAA,CAACN,cAAc;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAP,QAAA,eAC3DzB,OAAA,CAACd,GAAG;UAACwD,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAjB,QAAA,gBACpBzB,OAAA,CAACb,GAAG;YAAC0D,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACzBzB,OAAA,CAAClB,IAAI;cACHgC,KAAK,EAAC,0BAAM;cACZmC,OAAO,EAAE,cACPjD,OAAA,CAACjB,MAAM;gBAACkD,IAAI,EAAC,SAAS;gBAAAR,QAAA,EAAc;cAAE,GAAX,QAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,CAC/C;cAAAP,QAAA,eAEFzB,OAAA,CAAChB,KAAK;gBAACkE,SAAS,EAAC,UAAU;gBAAC3B,KAAK,EAAE;kBAAE4B,KAAK,EAAE;gBAAO,CAAE;gBAAA1B,QAAA,gBACnDzB,OAAA,CAACjB,MAAM;kBAACgF,KAAK;kBAAAtC,QAAA,EAAC;gBAAO;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9BhC,OAAA,CAACjB,MAAM;kBAACgF,KAAK;kBAAAtC,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/BhC,OAAA,CAACjB,MAAM;kBAACgF,KAAK;kBAAAtC,QAAA,EAAC;gBAAO;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9BhC,OAAA,CAACjB,MAAM;kBAACgF,KAAK;kBAAAtC,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENhC,OAAA,CAACb,GAAG;YAAC0D,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACzBzB,OAAA,CAAClB,IAAI;cACHgC,KAAK,EAAC,0BAAM;cACZmC,OAAO,EAAE,cACPjD,OAAA,CAACjB,MAAM;gBAACkD,IAAI,EAAC,SAAS;gBAAAR,QAAA,EAAe;cAAE,GAAZ,SAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,CAChD;cAAAP,QAAA,eAEFzB,OAAA,CAAChB,KAAK;gBAACkE,SAAS,EAAC,UAAU;gBAAC3B,KAAK,EAAE;kBAAE4B,KAAK,EAAE;gBAAO,CAAE;gBAAA1B,QAAA,gBACnDzB,OAAA,CAACjB,MAAM;kBAACgF,KAAK;kBAAAtC,QAAA,EAAC;gBAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BhC,OAAA,CAACjB,MAAM;kBAACgF,KAAK;kBAAAtC,QAAA,EAAC;gBAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BhC,OAAA,CAACjB,MAAM;kBAACgF,KAAK;kBAAAtC,QAAA,EAAC;gBAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BhC,OAAA,CAACjB,MAAM;kBAACgF,KAAK;kBAAAtC,QAAA,EAAC;gBAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAENhC,OAAA,CAACb,GAAG;YAAC0D,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtB,QAAA,eACzBzB,OAAA,CAAClB,IAAI;cACHgC,KAAK,EAAC,0BAAM;cACZmC,OAAO,EAAE,cACPjD,OAAA,CAACjB,MAAM;gBAACkD,IAAI,EAAC,SAAS;gBAAAR,QAAA,EAAe;cAAE,GAAZ,SAAS;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC,CAChD;cAAAP,QAAA,eAEFzB,OAAA,CAAChB,KAAK;gBAACkE,SAAS,EAAC,UAAU;gBAAC3B,KAAK,EAAE;kBAAE4B,KAAK,EAAE;gBAAO,CAAE;gBAAA1B,QAAA,gBACnDzB,OAAA;kBAAKuB,KAAK,EAAE;oBAAE6B,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE;kBAAgB,CAAE;kBAAA5B,QAAA,gBAC/DzB,OAAA,CAACE,IAAI;oBAAAuB,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBhC,OAAA,CAACE,IAAI;oBAACoD,MAAM;oBAAA7B,QAAA,EAAC;kBAAM;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACNhC,OAAA;kBAAKuB,KAAK,EAAE;oBAAE6B,OAAO,EAAE,MAAM;oBAAEC,cAAc,EAAE;kBAAgB,CAAE;kBAAA5B,QAAA,gBAC/DzB,OAAA,CAACE,IAAI;oBAAAuB,QAAA,EAAC;kBAAK;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAClBhC,OAAA,CAACE,IAAI;oBAAAuB,QAAA,EAAC;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACNhC,OAAA,CAACjB,MAAM;kBAACgF,KAAK;kBAAAtC,QAAA,EAAC;gBAAK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5BhC,OAAA,CAACjB,MAAM;kBAACgF,KAAK;kBAAAtC,QAAA,EAAC;gBAAI;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAvDkB,QAAQ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwD3B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEV,CAAC;AAACzB,EAAA,CA/MID,gBAAgB;EAAA,QACLzB,SAAS;AAAA;AAAAmF,EAAA,GADpB1D,gBAAgB;AAiNtB,eAAeA,gBAAgB;AAAC,IAAA0D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}