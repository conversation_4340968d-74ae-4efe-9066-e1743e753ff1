{"ast": null, "code": "export default function useInputReadOnly(formatList, inputReadOnly, multiple) {\n  if (typeof formatList[0] === 'function' || multiple) {\n    return true;\n  }\n  return inputReadOnly;\n}", "map": {"version": 3, "names": ["useInputReadOnly", "formatList", "inputReadOnly", "multiple"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/rc-picker/es/PickerInput/hooks/useInputReadOnly.js"], "sourcesContent": ["export default function useInputReadOnly(formatList, inputReadOnly, multiple) {\n  if (typeof formatList[0] === 'function' || multiple) {\n    return true;\n  }\n  return inputReadOnly;\n}"], "mappings": "AAAA,eAAe,SAASA,gBAAgBA,CAACC,UAAU,EAAEC,aAAa,EAAEC,QAAQ,EAAE;EAC5E,IAAI,OAAOF,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU,IAAIE,QAAQ,EAAE;IACnD,OAAO,IAAI;EACb;EACA,OAAOD,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}