# 小说管理系统 - NovelCraft 软件架构设计

## 1. 系统概述

NovelCraft是一个全方位的小说创作与管理系统，旨在帮助作者从多个维度管理小说设定、剧情发展、人物关系等内容，并通过AI辅助生成、分析和续写功能，确保故事的连贯性和完整性。

## 2. 核心模块

### 2.1 数据管理模块
- **小说项目管理**：创建、编辑、删除、导入导出小说项目
- **版本控制系统**：追踪小说各版本的变更历史
- **数据存储与同步**：本地存储与云端备份同步
- **AI生成及补全、续写**AI通过访问数据库中已有的设定和剧情，生成新的设定、内容或续写现有内容。
### 2.2 设定管理模块
- **世界观设定**：地理、历史、文化、自然法则等
- **修炼体系**：能力体系、等级划分、修炼方法
- **政治体系**：国家、势力、组织结构、权力分配、法律体系
- **货币体系**：货币制度、汇率体系、金融机构、支付方式
- **商业体系**：贸易路线、商会组织、市场机制、商业法规
- **种族类别**：各类种族特性、分布、关系、文化差异
- **功法体系**：功法分类、修炼方法、技能招式、传承体系
- **装备体系**：装备分类、强化系统、套装效果、制作配方
- **宠物体系**：宠物分类、进化系统、技能培养、繁殖机制
- **地图结构**：地图层级、地形特征、区域划分、特殊地点
- **维度结构**：多维度世界、空间层次、维度法则、传送机制
- **灵宝体系**：灵宝品级、器灵系统、炼制方法、传承历史
- **生民体系**：人口统计、社会阶层、生活方式、文化习俗、不同维度的人口分布
- **司法体系**：法院体系、审判程序、执法机构、法律条文、不同维度的司法制度
- **职业体系**：职业分类、技能要求、晋升路径、行业组织、不同维度的职业结构
- **经济系统**：资源分配、交易方式、经济政策
- **关系网络**：势力、家族、组织间的关系图谱

### 2.3 内容管理模块
- **资源分布**：各种资源在世界中的地理分布、开采难度、经济价值
- **种族分布**：种族在地理上的分布情况、人口统计、势力范围
- **秘境分布**：秘境、副本、特殊区域的分布、危险等级、奖励机制

### 2.4 人物管理模块
- **人物档案**：基础信息、性格特点、能力值、背景故事
- **人物关系网**：人物间的关系可视化
- **人物成长轨迹**：随剧情发展的人物变化追踪
- **人物画像生成**：基于描述的AI人物形象生成

### 2.4 项目管理模块
- **项目概览**：项目基本信息、统计数据、进度跟踪
- **模块导航**：提供卷宗管理、内容管理、设定管理、工具的统一入口

### 2.5 卷宗管理模块
- **卷宗结构管理**：卷宗的创建、编辑、删除和层级管理
- **章节管理**：在对应卷宗下管理章节内容、大纲、摘要
- **独立章节管理**：管理未分配到卷宗的独立章节
- **写作进度跟踪**：整体创作进度和各卷宗完成情况
- **AI辅助写作**：AI续写、大纲生成等智能创作功能
- **内容导出**：卷宗和章节内容的导出功能

### 2.6 剧情管理模块
- **主线剧情**：核心故事线索管理
- **支线剧情**：次要故事线索管理
- **时间线管理**：事件发生的时间顺序可视化
- **剧情冲突检测**：自动检测剧情矛盾和逻辑问题

### 2.7 AI辅助模块
- **设定生成器**：自动生成世界观、修炼体系等设定
- **人物生成器**：创建符合设定的NPC和主要角色
- **剧情生成器**：基于已有设定生成新剧情
- **章节摘要器**：自动总结章节内容要点
- **续写引擎**：基于历史设定和剧情自动续写
- **设定一致性检查**：检查新内容是否符合已有设定

### 2.8 用户界面模块

#### 导航结构
```
仪表盘
│
项目管理
│
├─ 卷宗管理（独立页面）
├─ 内容管理（独立页面）
├─ 设定管理（独立页面）
│
工具
├─ Agent-AI助手
├─ Agent-AI测试
└─ 系统设置
```

#### 页面模块
- **项目仪表盘**：项目概览和快速访问
- **项目管理界面**：项目基本信息、统计数据、进度展示
- **卷宗管理页面**：独立的卷宗和章节层级管理模块
  - 卷宗列表：卷宗创建、编辑、管理
  - 章节管理：章节统计、创建、编辑
  - 导出与发布：多格式导出、平台发布、版本管理
- **内容管理页面**：可折叠分组管理
  - 角色与势力管理：人物、势力、剧情
  - 世界分布管理：资源分布、种族分布、秘境分布
- **设定管理页面**：可折叠分组管理
  - 世界观与基础设定：世界设定、地图结构、维度结构、种族类别
  - 力量与战斗体系：修炼体系、功法体系、装备体系、灵宝体系、宠物体系
  - 社会与政治体系：政治体系、司法体系、生民体系、职业体系
  - 经济与商业体系：货币体系、商业体系
- **工具模块**：
  - Agent-AI助手：智能对话、内容生成
  - Agent-AI测试：AI功能测试
  - 系统设置：全局配置管理
- **专用编辑器**：
  - 卷宗编辑器：卷宗和章节的专用编辑界面
  - 设定编辑器：各类设定的编辑界面
  - 剧情编辑器：支持富文本和Markdown的编辑器
- **可视化组件**：
  - 关系图谱可视化：人物、势力关系的可视化展示
  - 时间线可视化：剧情发展时间线的可视化展示

## 3. 技术架构

### 3.1 前端技术
- **框架**：Electron (跨平台桌面应用)
- **UI库**：React + Ant Design
- **可视化**：D3.js (关系图谱、时间线可视化)
- **编辑器**：Monaco Editor (代码风格编辑) + Slate.js (富文本编辑)

### 3.2 后端技术
- **语言**：Python 3.9+
- **AI模型**：基于大型语言模型(LLM)的自定义微调模型
- **数据库**：SQLite (本地存储) + MongoDB (云端存储)
- **API服务**：FastAPI

### 3.3 AI引擎
- **文本生成**：基于GPT或类似模型的文本生成引擎
- **内容分析**：NLP技术进行文本分析、摘要和关键信息提取
- **一致性检查**：基于规则和语义理解的内容一致性验证

## 4. 数据流程

### 4.1 创作流程
1. 创建项目 → 设定基础世界观 → 构建人物关系 → 规划主线剧情 → 编写章节内容
2. AI辅助生成设定 → 人工审核修改 → 保存确认
3. 编写剧情 → AI检查一致性 → 修正问题 → 完成章节

### 4.2 续写流程
1. 导入历史设定和剧情 → AI分析关键元素 → 生成续写建议
2. 选择续写方向 → AI生成初稿 → 人工修改 → 整合到项目

### 4.3 数据同步流程
1. 本地编辑 → 自动保存 → 定时云端同步
2. 多设备访问 → 云端数据拉取 → 本地缓存

## 5. 系统扩展性

### 5.1 插件系统
- 支持第三方插件开发和集成
- 提供API接口供外部工具调用

### 5.2 模板系统
- 预设各类型小说的世界观模板
- 常见修炼体系、政治体系模板

### 5.3 导出功能
- 支持多种格式导出(Word, PDF, ePub等)
- 支持直接发布到各大小说平台

## 6. 安全与隐私

### 6.1 数据安全
- 本地数据加密存储
- 云端数据传输加密

### 6.2 隐私保护
- 用户可选择数据存储位置
- 明确的隐私政策和数据使用说明

## 7. 未来发展路线

### 7.1 短期目标
- 完成核心功能开发
- 优化AI生成质量
- 提升用户体验

### 7.2 中期目标
- 增加多语言支持
- 开发移动端应用
- 构建创作者社区

### 7.3 长期目标
- 开发专业版本针对出版社和专业作家
- 提供AI辅助的插画和配图功能
- 支持多媒体内容整合(音频、视频)

## 8. 详细模块设计

### 8.1 设定管理模块详细设计

#### 8.1.1 世界观设定系统
- **地理系统**
  - 世界地图编辑器：支持绘制、导入地图
  - 区域属性管理：气候、资源、人口等
  - 地点详情：城市、遗迹、特殊地点的详细信息

- **历史时间线**
  - 重大历史事件管理
  - 朝代/时代划分
  - 历史人物关联

- **文化系统**
  - 语言设定：主要语言、方言、特殊术语
  - 风俗习惯：节日、礼仪、禁忌
  - 宗教信仰：神祇体系、教义、仪式

#### 8.1.2 修炼体系设计器
- **能力体系构建**
  - 能力分类与等级划分
  - 能力获取与提升路径
  - 能力相克关系设定

- **修炼方法库**
  - 功法/技能体系
  - 修炼资源与材料
  - 境界划分与突破条件

- **特殊规则设定**
  - 天赋/血脉系统
  - 特殊状态与异常
  - 禁忌与限制

#### 8.1.3 政治体系设计器
- **政体设计**
  - 政府类型（君主制、共和制、民主制等）
  - 权力结构（中央集权、分权制、联邦制等）
  - 政府组织架构与职能分工

- **法律体系**
  - 法律条文与规则体系
  - 执法机构与司法制度
  - 合同执行与争议解决

- **权力分配与制衡**
  - 权力分配机制
  - 制衡与监督体系
  - 继承规则与政治传统

#### 8.1.4 货币体系设计器
- **货币制度**
  - 货币类型（金属、纸币、数字等）
  - 货币制度（金本位、法定货币等）
  - 基础货币与汇率体系

- **金融体系**
  - 发行机构与货币政策
  - 银行体系与金融机构
  - 支付方式与信贷体系

- **经济指标**
  - 通胀率与利率管理
  - 货币供应量控制
  - 国际贸易与外汇市场

#### 8.1.5 商业体系设计器
- **贸易体系**
  - 贸易路线与商业网络
  - 贸易协议与法规制度
  - 商品分类与服务行业

- **商业组织**
  - 商会公会与贸易公司
  - 商人组织与行业协会
  - 市场竞争与定价机制

- **基础设施**
  - 运输体系与通信网络
  - 仓储设施与物流管理
  - 税收制度与关税体系

#### 8.1.6 种族类别管理器
- **种族分类**
  - 种族类型（类人、兽族、元素族等）
  - 生理特征与外貌特点
  - 寿命类别与成长阶段

- **种族能力**
  - 种族天赋与特殊能力
  - 属性修正与技能加成
  - 文化传统与社会结构

- **种族关系**
  - 友好、敌对、中立关系
  - 历史冲突与合作事件
  - 领域分布与迁徙模式

#### 8.1.7 功法体系设计器
- **功法分类**
  - 功法类型（内功、外功、身法等）
  - 功法品级（基础、高级、传说等）
  - 元素亲和与能量类型

- **修炼体系**
  - 修炼前提与境界要求
  - 修炼方法与训练阶段
  - 资源消耗与时间投入

- **技能招式**
  - 招式技能与连招组合
  - 绝招与终极技能
  - 传承谱系与禁忌组合

#### 8.1.8 生民体系设计器
- **人口统计**
  - 总人口与人口密度
  - 年龄分布与性别分布
  - 人口增长率与迁徙模式

- **社会阶层**
  - 社会阶层结构与等级制度
  - 阶层流动性与社会地位
  - 特权与责任分配

- **生活方式**
  - 生活方式类型与特征
  - 生活条件与日常作息
  - 文化习俗与传统节日

- **维度差异**
  - 不同维度的人口分布特征
  - 跨维度的文化交流与冲突
  - 维度特有的社会结构

#### 8.1.9 司法体系设计器
- **法院体系**
  - 法院结构与层级划分
  - 专门法院与管辖范围
  - 审判程序与上诉机制

- **法律条文**
  - 法律体系类型与特征
  - 宪法、刑法、民法、行政法
  - 法律条文与执行标准

- **执法机构**
  - 警察体系与调查机构
  - 执法权力与监督机制
  - 司法人员培训与管理

- **维度特性**
  - 不同维度的法律制度差异
  - 跨维度案件的管辖权处理
  - 维度间的司法协作机制

#### 8.1.10 职业体系设计器
- **职业分类**
  - 职业类别与等级划分
  - 职业关系与相互依存
  - 职业发展路径与转换

- **技能体系**
  - 技能框架与要求标准
  - 技能发展与培训体系
  - 技能认证与评估机制

- **行业组织**
  - 公会组织与工会制度
  - 行业协会与专业团体
  - 组织服务与会员管理

- **维度适应**
  - 不同维度的职业结构差异
  - 跨维度的职业技能转移
  - 维度特有的职业类型

### 8.2 AI辅助模块详细设计

#### 8.2.1 设定生成引擎
- **基于模板生成**
  - 预设模板库(奇幻、武侠、科幻等)
  - 模板参数化调整
  - 随机变异与创新点生成

- **基于关键词生成**
  - 用户输入关键词/概念
  - AI扩展与丰富设定
  - 多方案比较与选择

- **设定一致性验证**
  - 内部逻辑检查
  - 与已有设定的兼容性检查
  - 潜在问题预警

#### 8.2.2 剧情生成与分析系统
- **剧情结构生成**
  - 基于经典故事结构(三幕剧、英雄之旅等)
  - 冲突设计与高潮安排
  - 伏笔与悬念规划

- **章节内容分析**
  - 关键情节提取
  - 人物情感与动机分析
  - 设定元素使用统计

- **剧情续写引擎**
  - 基于历史剧情的延续性分析
  - 多线索整合与推进
  - 角色行为合理性预测

#### 8.2.3 人物系统
- **人物生成器**
  - 基于角色原型生成
  - 性格特质平衡与冲突设计
  - 成长轨迹规划

- **人物关系动态模拟**
  - 基于性格的互动预测
  - 关系演变模拟
  - 潜在冲突与合作点分析

- **人物画像生成**
  - 基于文字描述的视觉形象生成
  - 表情与姿态库
  - 风格化调整

### 8.3 用户界面模块详细设计

#### 8.3.1 多视图工作区
- **仪表盘视图**
  - 项目概览与进度
  - 最近编辑内容
  - 待办任务与提醒

- **设定百科视图**
  - 分类浏览设定内容
  - 搜索与过滤功能
  - 关联内容推荐

- **写作工作区**
  - 大纲与卷宗章节管理
  - 富文本编辑器
  - 实时设定参考面板

#### 8.3.2 可视化工具
- **关系图谱编辑器**
  - 交互式节点与连接创建
  - 关系类型与强度设定
  - 时间维度变化展示

- **时间线编辑器**
  - 事件创建与排序
  - 并行时间线管理
  - 关键节点高亮与注释

- **地图交互系统**
  - 多层次地图浏览
  - 地点标记与详情
  - 角色/势力活动区域可视化

## 9. 数据模型设计

### 9.1 核心数据实体

#### 9.1.1 项目(Project)
- 基本信息(id, 名称, 创建时间, 最后修改时间)
- 元数据(类型, 标签, 简介)
- 全局设置(使用的模板, AI设置)

#### 9.1.2 世界设定(WorldSetting)
- 基本信息(id, 项目id, 名称)
- 地理数据(地图, 区域, 地点)
- 历史数据(时间线, 事件)
- 规则数据(自然法则, 特殊规则)

#### 9.1.3 修炼体系(CultivationSystem)
- 基本信息(id, 项目id, 名称)
- 能力分类与等级
- 修炼方法与资源
- 特殊规则与限制

#### 9.1.4 人物(Character)
- 基本信息(id, 项目id, 名称, 类型)
- 属性数据(性格, 能力, 背景)
- 关系数据(与其他人物的关系)
- 成长数据(状态变化, 重要事件)

#### 9.1.5 势力(Faction)
- 基本信息(id, 项目id, 名称, 类型)
- 结构数据(组织结构, 成员)
- 资源数据(领地, 财富, 特殊资源)
- 关系数据(与其他势力的关系)

#### 9.1.6 剧情(Plot)
- 基本信息(id, 项目id, 名称, 类型)
- 结构数据(主线, 支线, 章节)
- 事件数据(关键事件, 转折点)
- 参与者数据(相关人物, 势力)

#### 9.1.7 政治体系(PoliticalSystem)
- 基本信息(id, 项目id, 名称, 政府类型)
- 政府组织(政府结构, 领导层, 政治机构)
- 法律体系(法律条文, 执法机构, 司法制度)
- 权力分配(权力分配, 制衡机制, 继承规则)

#### 9.1.8 货币体系(CurrencySystem)
- 基本信息(id, 项目id, 名称, 货币制度)
- 货币种类(货币列表, 汇率体系, 面额体系)
- 金融体系(发行机构, 银行体系, 支付方式)
- 经济指标(通胀率, 利率, 货币供应量)

#### 9.1.9 商业体系(CommerceSystem)
- 基本信息(id, 项目id, 名称, 经济制度)
- 贸易体系(贸易路线, 贸易协议, 商品分类)
- 商业组织(商会公会, 贸易公司, 商人组织)
- 市场机制(定价机制, 供需关系, 市场竞争)

#### 9.1.10 种族类别(RaceSystem)
- 基本信息(id, 项目id, 名称, 种族类型)
- 生理特征(外貌特征, 体型分类, 寿命类别)
- 种族能力(种族天赋, 属性修正, 特殊能力)
- 文化社会(文化特征, 社会结构, 种族关系)

#### 9.1.11 功法体系(MartialArtsSystem)
- 基本信息(id, 项目id, 名称, 功法类型)
- 功法属性(功法品级, 力量来源, 元素亲和)
- 修炼体系(修炼前提, 修炼方法, 训练阶段)
- 技能招式(招式技能, 连招组合, 绝招技能)

#### 9.1.12 装备体系(EquipmentSystem)
- 基本信息(id, 项目id, 名称, 装备类型)
- 装备属性(装备品级, 基础属性, 特殊效果)
- 强化系统(强化等级, 强化材料, 套装效果)
- 制作信息(制作配方, 制作材料, 获取方式)

#### 9.1.13 宠物体系(PetSystem)
- 基本信息(id, 项目id, 名称, 宠物类型)
- 生物特征(物种, 体型, 寿命, 栖息地)
- 能力系统(基础属性, 技能系统, 进化链)
- 培养系统(喂食偏好, 训练方法, 繁殖系统)

#### 9.1.14 地图结构(MapStructure)
- 基本信息(id, 项目id, 名称, 地图类型)
- 地理信息(坐标, 面积, 海拔, 边界)
- 地形特征(地形类型, 气候, 自然地标)
- 人文地理(定居点, 交通网络, 政治控制)

#### 9.1.15 维度结构(DimensionStructure)
- 基本信息(id, 项目id, 名称, 维度类型)
- 维度属性(维度法则, 物理属性, 时间流速)
- 空间结构(空间维数, 尺度大小, 边界信息)
- 连接信息(传送门, 裂隙点, 旅行方式)

#### 9.1.16 灵宝体系(SpiritualTreasureSystem)
- 基本信息(id, 项目id, 名称, 灵宝类型)
- 灵宝属性(灵宝品级, 灵性等级, 特殊能力)
- 炼制信息(炼制材料, 炼制方法, 成功率)
- 器灵系统(器灵意识, 器灵能力, 沟通方式)

#### 9.1.17 资源分布(ResourceDistribution)
- 基本信息(id, 项目id, 资源名称, 资源类型)
- 分布信息(分布模式, 集中区域, 地理区域)
- 数量信息(总储量, 年产量, 再生率)
- 经济价值(市场价值, 贸易路线, 控制势力)

#### 9.1.18 种族分布(RaceDistribution)
- 基本信息(id, 项目id, 种族名称, 人口密度)
- 人口统计(总人口, 增长率, 年龄分布)
- 地理分布(主要领土, 次要区域, 迁徙路线)
- 社会结构(社会组织, 领导结构, 种族关系)

#### 9.1.19 秘境分布(SecretRealmDistribution)
- 基本信息(id, 项目id, 名称, 秘境类型)
- 位置信息(位置坐标, 地理区域, 隐藏程度)
- 结构信息(规模大小, 内部结构, 层数)
- 探索信息(危险等级, 奖励资源, 探索状态)

#### 9.1.20 生民体系(CivilianSystem)
- 基本信息(id, 项目id, 名称, 维度id, 区域名称)
- 人口统计(总人口, 人口密度, 人口增长率, 年龄分布, 性别分布)
- 社会阶层(社会阶层结构, 阶层流动性, 社会等级制度)
- 生活方式(生活方式类型, 生活条件, 日常作息)
- 文化习俗(文化习俗, 传统节日, 风俗习惯, 语言文字)
- 教育体系(教育制度, 识字率, 教育机构)
- 宗教信仰(宗教信仰, 宗教仪式, 宗教机构)
- 社会保障(社会保障制度, 医疗保健体系, 社会服务)
- 家庭结构(家庭结构, 婚姻习俗, 继承规则)
- 社会问题(社会问题, 犯罪率统计, 社会冲突)
- 维度特性(维度特有特征, 跨维度关系)

#### 9.1.21 司法体系(JudicialSystem)
- 基本信息(id, 项目id, 名称, 维度id, 司法管辖区名称, 法律体系类型)
- 法院体系(法院结构, 法院层级, 专门法院)
- 审判程序(审判程序, 程序类型, 上诉程序)
- 执法机构(执法机构, 警察体系, 调查机构)
- 法律条文(法律法规, 宪法, 刑法, 民法, 行政法)
- 司法人员(司法人员, 法官制度, 检察官制度, 律师制度)
- 刑罚制度(刑罚制度, 量刑指导, 改造项目)
- 司法监督(司法监督, 问责机制, 透明度措施)
- 替代性争议解决(替代性争议解决, 调解制度, 仲裁制度)
- 司法统计(案件统计, 定罪率, 上诉成功率)
- 维度特性(维度特有法律, 跨维度管辖权, 维度执法机制)
- 司法改革(改革历史, 正在进行的改革, 未来规划)

#### 9.1.22 职业体系(ProfessionSystem)
- 基本信息(id, 项目id, 名称, 维度id, 经济背景)
- 职业分类(职业分类, 职业等级, 职业关系)
- 技能体系(技能框架, 技能要求, 技能发展)
- 晋升路径(职业发展路径, 晋升标准, 晋升障碍)
- 行业组织(行业组织, 公会组织, 工会组织)
- 教育培训(培训体系, 学徒制度, 认证体系)
- 薪酬福利(薪酬结构, 福利制度, 经济激励)
- 工作环境(工作条件, 职场文化, 安全标准)
- 职业流动性(流动模式, 职业转换, 社会流动)
- 专业伦理(职业伦理, 行为准则, 纪律措施)
- 技术创新(技术影响, 创新趋势, 未来技能)
- 维度特性(维度特有职业, 跨维度职业, 维度技能转移)
- 市场需求(劳动力市场, 需求趋势, 供给分析)

### 9.2 关系模型

#### 9.2.1 人物关系(CharacterRelation)
- 基本信息(id, 项目id)
- 关系双方(人物A, 人物B)
- 关系属性(类型, 强度, 开始时间, 结束时间)
- 关系事件(形成原因, 重要互动)

#### 9.2.2 势力关系(FactionRelation)
- 基本信息(id, 项目id)
- 关系双方(势力A, 势力B)
- 关系属性(类型, 强度, 开始时间, 结束时间)
- 关系事件(形成原因, 重要互动)

#### 9.2.3 事件关联(EventAssociation)
- 基本信息(id, 项目id)
- 事件数据(事件id, 类型)
- 关联实体(人物id, 势力id, 地点id)
- 关联属性(角色, 影响)

## 10. 系统交互流程

### 10.1 创作流程详细步骤

1. **项目初始化**
   - 创建新项目 → 选择类型模板 → 设置基本信息
   - AI生成初始世界设定建议 → 用户选择与修改
   - 确认基础设定 → 系统生成项目骨架

2. **世界构建**
   - 细化地理环境 → 设计政治体系 → 构建经济系统
   - 设计修炼/能力体系 → 定义规则与限制
   - AI检查设定一致性 → 修正冲突点

3. **人物创建**
   - 设计主要角色 → 定义性格与能力 → 设置成长轨迹
   - 创建次要角色 → 建立人物关系网
   - AI生成人物画像 → 完善人物细节

4. **剧情规划**
   - 设计主线剧情 → 规划关键转折点
   - 创建支线剧情 → 与主线整合
   - 设计章节结构 → 安排伏笔与悬念

5. **内容创作**
   - 编写章节内容 → 实时参考设定资料
   - AI辅助内容生成 → 用户修改调整
   - 章节完成 → AI自动总结与归档

### 10.2 AI辅助流程

1. **设定生成**
   - 用户输入关键概念 → AI生成多个设定方案
   - 用户选择基础方案 → 调整参数与细节
   - AI完善设定细节 → 生成完整设定文档

2. **剧情分析与建议**
   - 系统分析已有内容 → 识别剧情发展模式
   - 检测潜在逻辑问题 → 提供修正建议
   - 预测可能的发展方向 → 提供剧情建议

3. **续写辅助**
   - 分析历史剧情与设定 → 提取关键元素
   - 用户设定续写方向 → AI生成大纲建议
   - 用户确认大纲 → AI辅助内容生成

## 11. 技术实现细节

### 11.1 前端实现

#### 11.1.1 应用框架
- **Electron**：跨平台桌面应用框架
  - 主进程：负责文件系统访问、数据库操作
  - 渲染进程：负责UI渲染和用户交互
  - IPC通信：进程间数据传输

#### 11.1.2 UI组件
- **React**：用户界面库
  - 函数式组件 + Hooks架构
  - Context API用于状态管理
  - 自定义Hook封装业务逻辑
- **Ant Design**：UI组件库
  - 定制主题与样式
  - 响应式布局
  - 暗黑模式支持

#### 11.1.3 编辑器实现
- **富文本编辑器**
  - Slate.js作为核心框架
  - 自定义插件支持特殊内容(设定引用、角色标记)
  - 实时保存与历史记录
- **可视化编辑器**
  - D3.js实现关系图谱
  - Timeline.js实现时间线
  - Leaflet.js实现地图交互

### 11.2 后端实现

#### 11.2.1 本地服务
- **数据存储**
  - SQLite数据库：结构化数据存储
  - 文件系统：大型文本内容和资源文件
  - IndexedDB：客户端缓存
- **本地API服务**
  - Express.js提供RESTful API
  - WebSocket支持实时通信
  - 本地缓存机制

#### 11.2.2 云服务
- **数据同步**
  - MongoDB Atlas：云端数据存储
  - Firebase Realtime Database：实时数据同步
  - S3兼容存储：资源文件存储
- **用户认证**
  - OAuth 2.0认证
  - JWT令牌管理
  - 权限控制系统

#### 11.2.3 AI服务
- **模型部署**
  - 云端API：高计算量任务
  - 本地轻量级模型：基础任务
  - 混合推理策略
- **API集成**
  - OpenAI API：文本生成与分析
  - Stable Diffusion API：图像生成
  - 自定义微调模型API

### 11.3 数据库设计

#### 11.3.1 关系型数据库(SQLite)
- **表结构**
  - 项目表(projects)
  - 设定表(settings)
  - 人物表(characters)
  - 势力表(factions)
  - 关系表(relations)
  - 事件表(events)
  - 章节表(chapters)
- **索引优化**
  - 复合索引：提高查询效率
  - 全文索引：支持内容搜索
  - 外键约束：保证数据完整性

#### 11.3.2 文档型数据库(MongoDB)
- **集合设计**
  - 项目集合(projects)

额外补充参考

一、Agent-AI智能对话系统设计（核心功能）

### 1.1 智能对话引擎（Conversation Engine）
**功能**：通过多轮对话理解用户创作意图，智能引导用户完成小说基础设施构建
**核心特性**：
- 上下文记忆与理解
- 意图识别与分析
- 智能问题生成
- 对话状态管理
- 多模态交互支持

### 1.2 创作意图分析模块（Intent Analysis）
**功能**：分析用户输入，识别创作需求和偏好
**输入**：用户自然语言描述、关键词、参考资料
**输出**：结构化创作需求、风格偏好、题材分类
**处理流程**：
1. 语义理解与实体提取
2. 创作类型识别（奇幻、武侠、科幻、现代等）
3. 风格偏好分析（轻松、严肃、黑暗、治愈等）
4. 复杂度评估（简单、中等、复杂）

### 1.3 渐进式引导系统（Progressive Guidance）
**功能**：根据用户回答智能调整问题深度和方向
**引导策略**：
- 从宏观到微观：世界观 → 体系设定 → 具体细节
- 从核心到边缘：主要元素 → 次要元素 → 补充内容
- 从简单到复杂：基础概念 → 深层逻辑 → 特殊规则

二、AI助手功能模块设计（高扩展性）

### 2.1 编剧AI（Director AI）
**功能**：根据用户给定的方向和命题，生成详细的大纲、世界观、人物设定、剧情主线等
**输入**：命题、方向、主题关键词、对话历史
**输出**：大纲、设定、主线剧情、章节规划
**智能特性**：
- 基于对话历史的个性化生成
- 多方案对比与推荐
- 设定完整性检查

### 2.2 剧情写作AI（Writer AI，RWKV为主）
**功能**：结合主题、大纲、时间线、设定等，分章节生成详细剧情文本
**输入**：大纲、章节规划、设定、时间线、用户偏好
**输出**：章节详细内容、场景描述、对话文本
**智能特性**：
- 风格一致性保持
- 伏笔与呼应自动处理
- 情节紧张度控制

### 2.3 总结AI（Summarizer AI）
**功能**：对每章节、每卷（部）进行总结，生成下一卷宗的前言
**输入**：章节内容、卷内容、关键情节
**输出**：章节总结、卷总结、前言、关键信息提取
**智能特性**：
- 多层次总结（简要、详细、关键点）
- 情节连贯性分析
- 重要信息标记

### 2.4 读者AI（Reader AI）
**功能**：模拟读者对内容进行正向/负向评价，提出建议，辅助编剧AI和写作AI调整剧情和文风
**输入**：章节内容、卷内容、总结、目标读者群体
**输出**：评价、建议、反馈、改进方案
**智能特性**：
- 多角度评价（情节、人物、文笔、逻辑）
- 读者群体模拟（年龄、性别、偏好）
- 市场接受度预测

### 2.5 设定管理AI（Setting Manager AI）
**功能**：智能管理和维护所有设定信息，确保一致性和完整性
**输入**：各类设定信息、新增内容、修改请求
**输出**：设定更新、冲突检测、补充建议
**智能特性**：
- 设定关联性分析
- 逻辑冲突自动检测
- 缺失信息智能补全

### 2.6 AI协作与反馈机制
**功能**：各AI模块之间可自动传递内容，形成闭环反馈，支持人工干预和二次编辑
**输入/输出**：各AI模块的输入输出、协作状态、反馈信息
**协作特性**：
- 任务自动分配与调度
- 结果质量评估与优化
- 人机协作无缝切换

三、智能对话创作流程设计（核心工作流）

### 3.1 初始对话阶段（主题确定）
**目标**：通过多轮对话明确小说主题和基本方向
**对话流程**：
1. **开放式询问**：
   - "您想创作什么类型的小说？"
   - "有什么特别的灵感或想法吗？"
   - "希望传达什么样的主题或情感？"

2. **意图深化**：
   - 根据用户回答，AI识别关键词和创作意图
   - 提出针对性问题深化理解
   - 确认题材类型（奇幻、武侠、科幻、现代等）

3. **主题确认**：
   - 总结用户的创作意图
   - 生成主题概述供用户确认
   - 记录到项目基础信息

### 3.2 世界设定构建阶段
**目标**：逐步构建完整的世界观设定
**对话流程**：
1. **世界观框架**：
   - "这个故事发生在什么样的世界？"
   - "是现实世界还是虚构世界？"
   - "有什么特殊的自然法则或规律吗？"

2. **地理环境**：
   - "主要故事发生在哪些地方？"
   - "这些地方有什么特色？"
   - "地理环境如何影响故事发展？"

3. **历史背景**：
   - "这个世界有什么重要的历史事件？"
   - "当前的时代背景是什么？"
   - "历史如何影响现在的故事？"

4. **自动生成与确认**：
   - AI根据对话生成详细世界设定
   - 用户确认或修改
   - 保存到世界观设定数据页

### 3.3 体系设定构建阶段
**目标**：建立货币、政权、修炼等核心体系
**对话流程**：
1. **政权体系**：
   - "这个世界的政治结构是怎样的？"
   - "有哪些主要的国家或势力？"
   - "权力是如何分配和运作的？"

2. **货币体系**：
   - "这个世界使用什么货币？"
   - "经济体系是如何运作的？"
   - "有什么特殊的交易方式吗？"

3. **修炼/能力体系**：
   - "角色有什么特殊能力或修炼体系？"
   - "能力是如何获得和提升的？"
   - "有什么等级划分或限制？"

4. **体系整合**：
   - AI生成完整的体系设定
   - 检查各体系间的逻辑一致性
   - 分类保存到对应数据管理页

### 3.4 人物角色构建阶段
**目标**：创建主要角色和人物关系网络
**对话流程**：
1. **主角设定**：
   - "主角是什么样的人？"
   - "主角有什么特殊背景或能力？"
   - "主角的性格特点和动机是什么？"

2. **重要配角**：
   - "有哪些重要的配角？"
   - "他们与主角是什么关系？"
   - "他们在故事中扮演什么角色？"

3. **反派角色**：
   - "主要的对手或反派是谁？"
   - "他们的动机和目标是什么？"
   - "与主角有什么冲突？"

4. **关系网络**：
   - AI分析人物间的关系
   - 生成人物关系图谱
   - 保存到人物管理数据页

### 3.5 势力分布构建阶段
**目标**：建立各种势力组织和其相互关系
**对话流程**：
1. **主要势力**：
   - "故事中有哪些重要的组织或势力？"
   - "这些势力的目标和理念是什么？"
   - "它们如何影响故事发展？"

2. **势力关系**：
   - "这些势力之间是什么关系？"
   - "有联盟、敌对或中立关系吗？"
   - "势力关系如何随故事发展变化？"

3. **势力影响**：
   - AI分析势力对剧情的影响
   - 生成势力关系图谱
   - 保存到势力管理数据页

### 3.6 时间线构建阶段
**目标**：建立故事的时间框架和重要事件
**对话流程**：
1. **故事时间跨度**：
   - "整个故事大概持续多长时间？"
   - "有哪些重要的时间节点？"
   - "故事是线性发展还是有时间跳跃？"

2. **关键事件**：
   - "故事中有哪些重要事件？"
   - "这些事件的发生顺序是什么？"
   - "事件之间有什么因果关系？"

3. **时间线整合**：
   - AI生成完整时间线
   - 标记关键事件和转折点
   - 保存到时间线管理页

### 3.7 基础设施完成确认
**目标**：确认所有基础设定已完备
**确认流程**：
1. **完整性检查**：
   - AI检查所有必要设定是否完整
   - 识别缺失或不完善的部分
   - 提醒用户补充

2. **一致性验证**：
   - 检查各设定间的逻辑一致性
   - 发现并提示潜在冲突
   - 建议修正方案

3. **设定总结**：
   - 生成完整的设定总结报告
   - 用户最终确认
   - 标记基础设施构建完成

### 3.8 卷宗章节架构设计阶段
**目标**：与用户讨论并确定小说的整体结构
**对话流程**：
1. **整体结构规划**：
   - "您希望小说分为几个部分或卷？"
   - "每个部分大概要讲什么内容？"
   - "整体的故事发展节奏是怎样的？"

2. **卷宗设计**：
   - "第一卷的主要内容和目标是什么？"
   - "每一卷之间是如何连接的？"
   - "每一卷的高潮和结尾如何设计？"

3. **章节规划**：
   - "每一卷大概需要多少章？"
   - "每章的篇幅大概多长？"
   - "章节之间的节奏如何安排？"

4. **架构确认**：
   - AI生成完整的卷宗章节架构
   - 用户确认或调整
   - 保存到卷宗管理页和章节管理页

### 3.9 章节剧情走向设计阶段
**目标**：确定每个章节的大致剧情发展方向
**对话流程**：
1. **章节主题确定**：
   - "第X章主要要表达什么？"
   - "这一章的核心冲突是什么？"
   - "这一章如何推进整体剧情？"

2. **情节要点规划**：
   - "这一章的开头如何设计？"
   - "中间有什么重要情节？"
   - "结尾要达到什么效果？"

3. **人物发展**：
   - "这一章中哪些角色会出场？"
   - "角色在这一章有什么变化？"
   - "角色关系如何发展？"

4. **章节连接**：
   - "这一章如何与前后章节连接？"
   - "有什么伏笔需要埋下？"
   - "有什么之前的伏笔需要回应？"

### 3.10 剧情细化完成阶段
**目标**：AI自主完成详细的章节剧情创作
**工作流程**：
1. **剧情大纲生成**：
   - AI根据章节走向生成详细大纲
   - 包含场景、对话、动作等要素
   - 确保与整体设定的一致性

2. **内容创作**：
   - AI基于大纲创作完整章节内容
   - 保持人物性格和文风一致性
   - 自动处理伏笔和呼应关系

3. **质量检查**：
   - 自动检查逻辑一致性
   - 验证设定符合性
   - 评估内容质量

4. **用户审核**：
   - 用户审阅AI生成的内容
   - 提出修改意见和建议
   - AI根据反馈进行调整

### 3.11 智能对话状态管理
**功能**：管理整个对话创作过程的状态和进度
**状态类型**：
1. **对话阶段状态**：
   - 当前处于哪个创作阶段
   - 已完成的步骤和待完成的任务
   - 用户偏好和选择记录

2. **内容完整性状态**：
   - 各类设定的完成度
   - 缺失信息的标记
   - 需要用户确认的内容

3. **AI协作状态**：
   - 各AI模块的工作状态
   - 任务分配和执行进度
   - 协作结果和反馈信息

### 3.12 智能问题生成策略
**功能**：根据用户回答智能生成后续问题
**生成策略**：
1. **上下文相关性**：
   - 基于用户之前的回答
   - 考虑已有的设定信息
   - 避免重复或冲突的问题

2. **深度递进**：
   - 从概括到具体
   - 从简单到复杂
   - 从核心到细节

3. **个性化适应**：
   - 根据用户的回答风格调整问题类型
   - 适应用户的创作偏好
   - 考虑用户的专业程度

四、系统架构扩展建议

### 4.1 AI服务抽象层设计
**功能**：所有AI助手通过统一接口调用，便于后续扩展新AI类型
**技术实现**：
- 统一的AI Provider接口
- 插件化的AI模型管理
- 动态加载和配置机制
- 多模型并行处理支持

### 4.2 任务队列与流程引擎
**功能**：支持多AI协作的任务流转与状态管理
**核心组件**：
- 任务调度器：管理AI任务的分配和执行
- 状态机：跟踪对话和创作流程状态
- 消息队列：处理AI间的异步通信
- 工作流引擎：定义和执行复杂的创作流程

### 4.3 可视化流程管理
**功能**：前端可视化展示AI协作流程、内容流转和反馈
**界面设计**：
- 流程图展示：显示当前创作进度和AI协作状态
- 实时监控：监控各AI模块的工作状态
- 交互控制：允许用户干预和调整AI协作流程
- 历史追踪：记录和回放创作过程

### 4.4 智能对话系统技术架构
**核心技术栈**：
- 自然语言处理：意图识别、实体提取、情感分析
- 对话管理：多轮对话状态跟踪、上下文管理
- 知识图谱：存储和管理创作相关的知识结构
- 推理引擎：基于规则和机器学习的智能推理

### 4.5 数据管理扩展
**智能数据组织**：
- 自动分类：AI自动将生成内容分类到对应数据页
- 关联分析：分析不同数据间的关联关系
- 版本控制：跟踪所有设定和内容的变更历史
- 智能搜索：基于语义的内容搜索和推荐

五、README.md结构补充建议

## 致谢

感谢所有为NovelCraft项目做出贡献的开发者和用户。

## Agent-AI编剧协作系统

### 功能概述

本系统集成多种AI助手，协同完成小说从命题到完本的全过程创作。各AI助手分工明确，互相协作，支持人工干预和反馈优化。

### 主要Agent-AI助手模块

1. **编剧AI（Director AI）**
   - 负责根据命题生成大纲、设定、主线剧情。
   - 输入：命题、方向、主题
   - 输出：大纲、设定、主线剧情

2. **剧情写作AI（Writer AI，RWKV）**
   - 负责分章节生成详细剧情。
   - 输入：大纲、章节规划、设定、时间线
   - 输出：章节内容

3. **总结AI（Summarizer AI）**
   - 负责已完成的章节、卷的总结，生成后续章节、卷宗的前言。
   - 输入：章节/卷内容
   - 输出：总结、前言

4. **读者AI（Reader AI）**
   - 负责模拟读者评价，提出正负反馈和建议。
   - 输入：章节/卷内容、总结
   - 输出：评价、建议

5. **AI协作与反馈机制**
   - 各AI模块自动流转内容，支持人工干预和二次编辑。

### 工作流程

1. 用户输入命题/方向
2. 编剧AI生成大纲、设定、主线
3. 剧情写作AI分章节生成内容
4. 总结AI对章节/卷进行总结，生成前言
5. 读者AI给出评价和建议
6. 编剧AI/写作AI根据反馈调整内容
7. 循环迭代，直至小说完本

### 扩展性说明

- 所有AI助手通过统一接口调用，便于扩展新AI类型
- 支持多AI协作、任务流转、人工干预
- 可视化流程管理，便于内容追踪和优化

### 主要调用方式

- 通过系统设置界面配置各AI助手参数
- 在创作流程中选择AI助手自动/手动生成内容
- 支持一键生成、逐步生成、人工编辑与反馈

### Agent-代码结构建议
ai_engine/
├── director/ # 编剧AI相关逻辑（deepseek-r1）
│ └── director_ai.py
├── writer/ # 剧情写作AI（RWKV7-G1）
│ └── writer_ai.py
├── summarizer/ # 总结AI（GLM4-LONG）
│ └── summarizer_ai.py
├── reader/ # 读者AI（QWEN3）
│ └── reader_ai.py
├── workflow/ # AI协作与流程管理（Agent）
│ └── workflow_engine.py
└── utils/ # 工具函数


- 每个AI助手为独立子模块，便于维护和扩展
- 统一接口，支持多模型、多平台切换
- 支持任务队列和流程引擎，便于多AI协作

### 智能对话系统代码结构建议
```
conversation_engine/
├── core/                    # 核心对话引擎
│   ├── conversation_manager.py    # 对话管理器
│   ├── intent_analyzer.py         # 意图分析器
│   ├── context_manager.py         # 上下文管理器
│   └── state_machine.py           # 状态机
├── stages/                  # 对话阶段模块
│   ├── theme_stage.py             # 主题确定阶段
│   ├── world_stage.py             # 世界设定阶段
│   ├── system_stage.py            # 体系设定阶段
│   ├── character_stage.py         # 人物角色阶段
│   ├── faction_stage.py           # 势力分布阶段
│   ├── timeline_stage.py          # 时间线阶段
│   ├── structure_stage.py         # 卷宗章节阶段
│   └── plot_stage.py              # 剧情走向阶段
├── generators/              # 智能生成器
│   ├── question_generator.py      # 问题生成器
│   ├── content_generator.py       # 内容生成器
│   └── suggestion_generator.py    # 建议生成器
├── validators/              # 验证器
│   ├── completeness_validator.py  # 完整性验证
│   ├── consistency_validator.py   # 一致性验证
│   └── quality_validator.py       # 质量验证
└── utils/                   # 工具函数
    ├── nlp_utils.py               # 自然语言处理工具
    ├── data_utils.py              # 数据处理工具
    └── template_utils.py          # 模板工具
```

## 12. 系统整体架构图

### 12.1 完整系统架构层级

```
仪表盘
 │
项目管理(Agent-AI助手-可控-可读写)
 │
 ├──轮回·千面劫 (Agent-AI助手-可控-可读写)
 │          ├─ 项目概览 (Agent-AI助手-可控-可读写)
 │          ├─ 卷宗管理 ← 独立的顶级模块 (Agent-AI助手-可控-可读写)
 │          ├─ 内容管理 ← 独立的顶级模块 (Agent-AI助手-可控-可读写)
 │          ├─ 设定管理 ← 独立的顶级模块 (Agent-AI助手-可控-可读写)
 │          └──工具
 │
 ├──仙剑奇缘 (Agent-AI助手-可控-可读写)
 │          ├─ 项目概览  (Agent-AI助手-可控-可读写)
 │          ├─ 卷宗管理 ← 独立的顶级模块 (Agent-AI助手-可控-可读写)
 │          ├─ 内容管理 ← 独立的顶级模块 (Agent-AI助手-可控-可读写)
 │          ├─ 设定管理 ← 独立的顶级模块 (Agent-AI助手-可控-可读写)
 │          └──工具
 │
 ├──诛仙 (Agent-AI助手-可控-可读写)
 │          ├─ 项目概览 (Agent-AI助手-可控-可读写)
 │          ├─ 卷宗管理 ← 独立的顶级模块 (Agent-AI助手-可控-可读写)
 │          ├─ 内容管理 ← 独立的顶级模块 (Agent-AI助手-可控-可读写)
 │          ├─ 设定管理 ← 独立的顶级模块 (Agent-AI助手-可控-可读写)
 │          └──工具
 │
 └──工具
             ├─ Agent-AI助手
             ├─ Agent-AI测试
             └─ 系统设置
```

### 12.2 架构设计原则

#### 12.2.1 Agent-AI助手权限设计
- **项目级权限**：Agent-AI助手对每个项目都拥有完整的可控、可读写权限
- **模块级权限**：可以访问和操作项目下的所有模块（项目概览、卷宗管理、内容管理、设定管理）
- **数据级权限**：可以读取、创建、修改、删除项目内的所有数据
- **跨项目隔离**：不同项目间的数据完全隔离，Agent只能访问指定项目的数据

#### 12.2.2 模块独立性
- **卷宗管理**：作为独立的顶级模块，与内容管理、设定管理平级
- **内容管理**：专注于故事要素和世界内容的管理
- **设定管理**：专注于世界观和各种体系设定的管理
- **工具模块**：提供跨项目的通用工具和系统功能

#### 12.2.3 数据流转机制
- **项目内流转**：各模块间可以自由引用和关联数据
- **Agent协作**：Agent-AI助手可以跨模块整合和分析数据
- **智能建议**：基于全项目数据提供智能创作建议
- **一致性检查**：自动检查跨模块数据的一致性和逻辑性

### 智能对话工作流程实现
1. **对话初始化**：
   - 创建对话会话
   - 初始化用户画像
   - 设置对话目标和阶段

2. **意图识别与分析**：
   - 分析用户输入的语义
   - 提取关键信息和实体
   - 判断用户的创作意图和偏好

3. **智能问题生成**：
   - 基于当前阶段和用户回答生成问题
   - 考虑上下文相关性和深度递进
   - 个性化调整问题风格和难度

4. **内容自动生成**：
   - 根据用户回答生成相应设定内容
   - 确保生成内容的逻辑一致性
   - 自动分类保存到对应数据页

5. **质量验证与优化**：
   - 检查生成内容的完整性和一致性
   - 识别潜在问题并提供修正建议
   - 持续优化生成质量

### 技术实现要点
- **多轮对话状态管理**：使用状态机模式管理复杂的对话流程
- **上下文记忆机制**：维护长期和短期记忆，确保对话连贯性
- **个性化适应**：根据用户行为和偏好调整对话策略
- **实时反馈机制**：支持用户随时修正和调整AI的理解
- **可扩展架构**：便于添加新的对话阶段和功能模块

### 备注

- 推荐优先支持ollama、RWKV等本地模型，保障数据安全和隐私
- 支持zhipuAI、deepseek、硅基流动等云端模型作为补充
- 后续可扩展更多AI助手类型和协作模式
- 要求Agent-AI助手对整个项目有最高调用权、编辑权限
- 智能对话系统应具备学习能力，能够从用户交互中不断优化对话策略
- 支持多语言对话，适应不同地区用户的使用习惯
- 提供对话历史回放和分析功能，帮助用户回顾创作过程

### 系统集成说明
智能对话系统作为NovelCraft的核心功能，需要与以下模块深度集成：
- **数据管理模块**：自动保存和组织对话生成的内容
- **AI助手模块**：协调各AI助手的协作和任务分配
- **用户界面模块**：提供友好的对话交互界面
- **项目管理模块**：跟踪项目进度和完成状态

通过这种深度集成，确保用户能够通过自然的对话方式完成从创意到完整小说的全过程创作。

2025年5月29日 10:52:17追加补充功能：
Agent支持遍历用户输入的小说章节，并从中拆解出本管理系统所需的架构数据，并逐一创建写入对应的数据库中
