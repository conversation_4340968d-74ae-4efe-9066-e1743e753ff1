{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { useRef } from 'react';\nimport useColorDrag from \"../hooks/useColorDrag\";\nimport Palette from \"./Palette\";\nimport classNames from 'classnames';\nimport { useEvent } from 'rc-util';\nimport { Color } from \"../color\";\nimport { calcOffset, calculateColor } from \"../util\";\nimport Gradient from \"./Gradient\";\nimport Handler from \"./Handler\";\nimport Transform from \"./Transform\";\nvar Slider = function Slider(props) {\n  var prefixCls = props.prefixCls,\n    colors = props.colors,\n    disabled = props.disabled,\n    onChange = props.onChange,\n    onChangeComplete = props.onChangeComplete,\n    color = props.color,\n    type = props.type;\n  var sliderRef = useRef();\n  var transformRef = useRef();\n  var colorRef = useRef(color);\n  var getValue = function getValue(c) {\n    return type === 'hue' ? c.getHue() : c.a * 100;\n  };\n  var onDragChange = useEvent(function (offsetValue) {\n    var calcColor = calculateColor({\n      offset: offsetValue,\n      targetRef: transformRef,\n      containerRef: sliderRef,\n      color: color,\n      type: type\n    });\n    colorRef.current = calcColor;\n    onChange(getValue(calcColor));\n  });\n  var _useColorDrag = useColorDrag({\n      color: color,\n      targetRef: transformRef,\n      containerRef: sliderRef,\n      calculate: function calculate() {\n        return calcOffset(color, type);\n      },\n      onDragChange: onDragChange,\n      onDragChangeComplete: function onDragChangeComplete() {\n        onChangeComplete(getValue(colorRef.current));\n      },\n      direction: 'x',\n      disabledDrag: disabled\n    }),\n    _useColorDrag2 = _slicedToArray(_useColorDrag, 2),\n    offset = _useColorDrag2[0],\n    dragStartHandle = _useColorDrag2[1];\n  var handleColor = React.useMemo(function () {\n    if (type === 'hue') {\n      var hsb = color.toHsb();\n      hsb.s = 1;\n      hsb.b = 1;\n      hsb.a = 1;\n      var lightColor = new Color(hsb);\n      return lightColor;\n    }\n    return color;\n  }, [color, type]);\n\n  // ========================= Gradient =========================\n  var gradientList = React.useMemo(function () {\n    return colors.map(function (info) {\n      return \"\".concat(info.color, \" \").concat(info.percent, \"%\");\n    });\n  }, [colors]);\n\n  // ========================== Render ==========================\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: sliderRef,\n    className: classNames(\"\".concat(prefixCls, \"-slider\"), \"\".concat(prefixCls, \"-slider-\").concat(type)),\n    onMouseDown: dragStartHandle,\n    onTouchStart: dragStartHandle\n  }, /*#__PURE__*/React.createElement(Palette, {\n    prefixCls: prefixCls\n  }, /*#__PURE__*/React.createElement(Transform, {\n    x: offset.x,\n    y: offset.y,\n    ref: transformRef\n  }, /*#__PURE__*/React.createElement(Handler, {\n    size: \"small\",\n    color: handleColor.toHexString(),\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(Gradient, {\n    colors: gradientList,\n    type: type,\n    prefixCls: prefixCls\n  })));\n};\nexport default Slider;", "map": {"version": 3, "names": ["_slicedToArray", "React", "useRef", "useColorDrag", "Palette", "classNames", "useEvent", "Color", "calcOffset", "calculateColor", "Gradient", "Handler", "Transform", "Slide<PERSON>", "props", "prefixCls", "colors", "disabled", "onChange", "onChangeComplete", "color", "type", "sliderRef", "transformRef", "colorRef", "getValue", "c", "getHue", "a", "onDragChange", "offsetValue", "calcColor", "offset", "targetRef", "containerRef", "current", "_useColorDrag", "calculate", "onDragChangeComplete", "direction", "disabledDrag", "_useColorDrag2", "dragStartHandle", "handleColor", "useMemo", "hsb", "toHsb", "s", "b", "lightColor", "gradientList", "map", "info", "concat", "percent", "createElement", "ref", "className", "onMouseDown", "onTouchStart", "x", "y", "size", "toHexString"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/@rc-component/color-picker/es/components/Slider.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport React, { useRef } from 'react';\nimport useColorDrag from \"../hooks/useColorDrag\";\nimport Palette from \"./Palette\";\nimport classNames from 'classnames';\nimport { useEvent } from 'rc-util';\nimport { Color } from \"../color\";\nimport { calcOffset, calculateColor } from \"../util\";\nimport Gradient from \"./Gradient\";\nimport Handler from \"./Handler\";\nimport Transform from \"./Transform\";\nvar Slider = function Slider(props) {\n  var prefixCls = props.prefixCls,\n    colors = props.colors,\n    disabled = props.disabled,\n    onChange = props.onChange,\n    onChangeComplete = props.onChangeComplete,\n    color = props.color,\n    type = props.type;\n  var sliderRef = useRef();\n  var transformRef = useRef();\n  var colorRef = useRef(color);\n  var getValue = function getValue(c) {\n    return type === 'hue' ? c.getHue() : c.a * 100;\n  };\n  var onDragChange = useEvent(function (offsetValue) {\n    var calcColor = calculateColor({\n      offset: offsetValue,\n      targetRef: transformRef,\n      containerRef: sliderRef,\n      color: color,\n      type: type\n    });\n    colorRef.current = calcColor;\n    onChange(getValue(calcColor));\n  });\n  var _useColorDrag = useColorDrag({\n      color: color,\n      targetRef: transformRef,\n      containerRef: sliderRef,\n      calculate: function calculate() {\n        return calcOffset(color, type);\n      },\n      onDragChange: onDragChange,\n      onDragChangeComplete: function onDragChangeComplete() {\n        onChangeComplete(getValue(colorRef.current));\n      },\n      direction: 'x',\n      disabledDrag: disabled\n    }),\n    _useColorDrag2 = _slicedToArray(_useColorDrag, 2),\n    offset = _useColorDrag2[0],\n    dragStartHandle = _useColorDrag2[1];\n  var handleColor = React.useMemo(function () {\n    if (type === 'hue') {\n      var hsb = color.toHsb();\n      hsb.s = 1;\n      hsb.b = 1;\n      hsb.a = 1;\n      var lightColor = new Color(hsb);\n      return lightColor;\n    }\n    return color;\n  }, [color, type]);\n\n  // ========================= Gradient =========================\n  var gradientList = React.useMemo(function () {\n    return colors.map(function (info) {\n      return \"\".concat(info.color, \" \").concat(info.percent, \"%\");\n    });\n  }, [colors]);\n\n  // ========================== Render ==========================\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: sliderRef,\n    className: classNames(\"\".concat(prefixCls, \"-slider\"), \"\".concat(prefixCls, \"-slider-\").concat(type)),\n    onMouseDown: dragStartHandle,\n    onTouchStart: dragStartHandle\n  }, /*#__PURE__*/React.createElement(Palette, {\n    prefixCls: prefixCls\n  }, /*#__PURE__*/React.createElement(Transform, {\n    x: offset.x,\n    y: offset.y,\n    ref: transformRef\n  }, /*#__PURE__*/React.createElement(Handler, {\n    size: \"small\",\n    color: handleColor.toHexString(),\n    prefixCls: prefixCls\n  })), /*#__PURE__*/React.createElement(Gradient, {\n    colors: gradientList,\n    type: type,\n    prefixCls: prefixCls\n  })));\n};\nexport default Slider;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,QAAQ,SAAS;AAClC,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,UAAU,EAAEC,cAAc,QAAQ,SAAS;AACpD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,SAAS,MAAM,aAAa;AACnC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;EAClC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACrBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,gBAAgB,GAAGL,KAAK,CAACK,gBAAgB;IACzCC,KAAK,GAAGN,KAAK,CAACM,KAAK;IACnBC,IAAI,GAAGP,KAAK,CAACO,IAAI;EACnB,IAAIC,SAAS,GAAGpB,MAAM,CAAC,CAAC;EACxB,IAAIqB,YAAY,GAAGrB,MAAM,CAAC,CAAC;EAC3B,IAAIsB,QAAQ,GAAGtB,MAAM,CAACkB,KAAK,CAAC;EAC5B,IAAIK,QAAQ,GAAG,SAASA,QAAQA,CAACC,CAAC,EAAE;IAClC,OAAOL,IAAI,KAAK,KAAK,GAAGK,CAAC,CAACC,MAAM,CAAC,CAAC,GAAGD,CAAC,CAACE,CAAC,GAAG,GAAG;EAChD,CAAC;EACD,IAAIC,YAAY,GAAGvB,QAAQ,CAAC,UAAUwB,WAAW,EAAE;IACjD,IAAIC,SAAS,GAAGtB,cAAc,CAAC;MAC7BuB,MAAM,EAAEF,WAAW;MACnBG,SAAS,EAAEV,YAAY;MACvBW,YAAY,EAAEZ,SAAS;MACvBF,KAAK,EAAEA,KAAK;MACZC,IAAI,EAAEA;IACR,CAAC,CAAC;IACFG,QAAQ,CAACW,OAAO,GAAGJ,SAAS;IAC5Bb,QAAQ,CAACO,QAAQ,CAACM,SAAS,CAAC,CAAC;EAC/B,CAAC,CAAC;EACF,IAAIK,aAAa,GAAGjC,YAAY,CAAC;MAC7BiB,KAAK,EAAEA,KAAK;MACZa,SAAS,EAAEV,YAAY;MACvBW,YAAY,EAAEZ,SAAS;MACvBe,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;QAC9B,OAAO7B,UAAU,CAACY,KAAK,EAAEC,IAAI,CAAC;MAChC,CAAC;MACDQ,YAAY,EAAEA,YAAY;MAC1BS,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;QACpDnB,gBAAgB,CAACM,QAAQ,CAACD,QAAQ,CAACW,OAAO,CAAC,CAAC;MAC9C,CAAC;MACDI,SAAS,EAAE,GAAG;MACdC,YAAY,EAAEvB;IAChB,CAAC,CAAC;IACFwB,cAAc,GAAGzC,cAAc,CAACoC,aAAa,EAAE,CAAC,CAAC;IACjDJ,MAAM,GAAGS,cAAc,CAAC,CAAC,CAAC;IAC1BC,eAAe,GAAGD,cAAc,CAAC,CAAC,CAAC;EACrC,IAAIE,WAAW,GAAG1C,KAAK,CAAC2C,OAAO,CAAC,YAAY;IAC1C,IAAIvB,IAAI,KAAK,KAAK,EAAE;MAClB,IAAIwB,GAAG,GAAGzB,KAAK,CAAC0B,KAAK,CAAC,CAAC;MACvBD,GAAG,CAACE,CAAC,GAAG,CAAC;MACTF,GAAG,CAACG,CAAC,GAAG,CAAC;MACTH,GAAG,CAACjB,CAAC,GAAG,CAAC;MACT,IAAIqB,UAAU,GAAG,IAAI1C,KAAK,CAACsC,GAAG,CAAC;MAC/B,OAAOI,UAAU;IACnB;IACA,OAAO7B,KAAK;EACd,CAAC,EAAE,CAACA,KAAK,EAAEC,IAAI,CAAC,CAAC;;EAEjB;EACA,IAAI6B,YAAY,GAAGjD,KAAK,CAAC2C,OAAO,CAAC,YAAY;IAC3C,OAAO5B,MAAM,CAACmC,GAAG,CAAC,UAAUC,IAAI,EAAE;MAChC,OAAO,EAAE,CAACC,MAAM,CAACD,IAAI,CAAChC,KAAK,EAAE,GAAG,CAAC,CAACiC,MAAM,CAACD,IAAI,CAACE,OAAO,EAAE,GAAG,CAAC;IAC7D,CAAC,CAAC;EACJ,CAAC,EAAE,CAACtC,MAAM,CAAC,CAAC;;EAEZ;EACA,OAAO,aAAaf,KAAK,CAACsD,aAAa,CAAC,KAAK,EAAE;IAC7CC,GAAG,EAAElC,SAAS;IACdmC,SAAS,EAAEpD,UAAU,CAAC,EAAE,CAACgD,MAAM,CAACtC,SAAS,EAAE,SAAS,CAAC,EAAE,EAAE,CAACsC,MAAM,CAACtC,SAAS,EAAE,UAAU,CAAC,CAACsC,MAAM,CAAChC,IAAI,CAAC,CAAC;IACrGqC,WAAW,EAAEhB,eAAe;IAC5BiB,YAAY,EAAEjB;EAChB,CAAC,EAAE,aAAazC,KAAK,CAACsD,aAAa,CAACnD,OAAO,EAAE;IAC3CW,SAAS,EAAEA;EACb,CAAC,EAAE,aAAad,KAAK,CAACsD,aAAa,CAAC3C,SAAS,EAAE;IAC7CgD,CAAC,EAAE5B,MAAM,CAAC4B,CAAC;IACXC,CAAC,EAAE7B,MAAM,CAAC6B,CAAC;IACXL,GAAG,EAAEjC;EACP,CAAC,EAAE,aAAatB,KAAK,CAACsD,aAAa,CAAC5C,OAAO,EAAE;IAC3CmD,IAAI,EAAE,OAAO;IACb1C,KAAK,EAAEuB,WAAW,CAACoB,WAAW,CAAC,CAAC;IAChChD,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,EAAE,aAAad,KAAK,CAACsD,aAAa,CAAC7C,QAAQ,EAAE;IAC9CM,MAAM,EAAEkC,YAAY;IACpB7B,IAAI,EAAEA,IAAI;IACVN,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,CAAC;AACN,CAAC;AACD,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}