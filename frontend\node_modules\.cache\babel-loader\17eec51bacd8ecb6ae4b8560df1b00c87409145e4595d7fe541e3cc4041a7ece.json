{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\VolumeList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Progress, Tooltip, Row, Col, Statistic, Collapse, List, Divider, Tabs, Empty } from 'antd';\nimport { PlusOutlined, FileTextOutlined, EditOutlined, DeleteOutlined, EyeOutlined, RobotOutlined, BookOutlined, ClockCircleOutlined, CheckCircleOutlined, FolderOutlined, OrderedListOutlined, BarChartOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  Panel\n} = Collapse;\nconst {\n  TabPane\n} = Tabs;\nconst VolumeList = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const navigate = useNavigate();\n  const [volumes, setVolumes] = useState([]);\n  const [chapters, setChapters] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [volumeModalVisible, setVolumeModalVisible] = useState(false);\n  const [chapterModalVisible, setChapterModalVisible] = useState(false);\n  const [assignModalVisible, setAssignModalVisible] = useState(false);\n  const [editingVolume, setEditingVolume] = useState(null);\n  const [editingChapter, setEditingChapter] = useState(null);\n  const [assigningChapter, setAssigningChapter] = useState(null);\n  const [selectedVolumeId, setSelectedVolumeId] = useState(null);\n  const [activeTab, setActiveTab] = useState('volumes');\n  const [volumeForm] = Form.useForm();\n  const [chapterForm] = Form.useForm();\n  const [assignForm] = Form.useForm();\n\n  // 状态配置\n  const volumeStatusConfig = {\n    planning: {\n      color: 'blue',\n      text: '规划中'\n    },\n    writing: {\n      color: 'orange',\n      text: '写作中'\n    },\n    completed: {\n      color: 'green',\n      text: '已完成'\n    },\n    reviewing: {\n      color: 'purple',\n      text: '审阅中'\n    },\n    revised: {\n      color: 'cyan',\n      text: '已修订'\n    },\n    published: {\n      color: 'success',\n      text: '已发布'\n    }\n  };\n  const chapterStatusConfig = {\n    planning: {\n      color: 'blue',\n      text: '规划中'\n    },\n    draft: {\n      color: 'orange',\n      text: '草稿'\n    },\n    writing: {\n      color: 'processing',\n      text: '写作中'\n    },\n    completed: {\n      color: 'green',\n      text: '已完成'\n    },\n    published: {\n      color: 'success',\n      text: '已发布'\n    }\n  };\n\n  // 模拟卷宗数据\n  const mockVolumes = [{\n    id: 1,\n    title: '第一卷：初入修仙界',\n    volumeNumber: 1,\n    status: 'writing',\n    summary: '主角初次踏入修仙世界，遇到师父，开始修炼之路',\n    totalChapters: 10,\n    completedChapters: 6,\n    totalWords: 45000,\n    targetWords: 80000,\n    progress: 60,\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-20'\n  }, {\n    id: 2,\n    title: '第二卷：宗门试炼',\n    volumeNumber: 2,\n    status: 'planning',\n    summary: '主角参加宗门入门试炼，展现天赋，结识同门',\n    totalChapters: 8,\n    completedChapters: 0,\n    totalWords: 0,\n    targetWords: 60000,\n    progress: 0,\n    createdAt: '2024-01-21',\n    updatedAt: '2024-01-21'\n  }];\n\n  // 模拟章节数据\n  const mockChapters = [{\n    id: 1,\n    volumeId: 1,\n    title: '第一章：觉醒',\n    chapterNumber: 1,\n    content: '在这个充满灵气的世界里，少年踏上了修仙之路...',\n    wordCount: 3500,\n    status: 'published',\n    outline: '主角初次接触修仙世界，遇到第一位师父',\n    createdAt: '2024-01-15',\n    updatedAt: '2024-01-16'\n  }, {\n    id: 2,\n    volumeId: 1,\n    title: '第二章：师父',\n    chapterNumber: 2,\n    content: '经过数月的修炼，主角终于感受到了灵气的存在...',\n    wordCount: 4200,\n    status: 'completed',\n    outline: '主角开始正式修炼，学习基础功法',\n    createdAt: '2024-01-17',\n    updatedAt: '2024-01-18'\n  }, {\n    id: 3,\n    volumeId: 1,\n    title: '第三章：修炼',\n    chapterNumber: 3,\n    content: '',\n    wordCount: 0,\n    status: 'planning',\n    outline: '主角参加宗门入门试炼，展现天赋',\n    createdAt: '2024-01-19',\n    updatedAt: '2024-01-19'\n  }, {\n    id: 4,\n    volumeId: 2,\n    title: '第四章：试炼开始',\n    chapterNumber: 1,\n    content: '',\n    wordCount: 0,\n    status: 'planning',\n    outline: '宗门试炼正式开始，各路天才汇聚',\n    createdAt: '2024-01-20',\n    updatedAt: '2024-01-20'\n  }, {\n    id: 5,\n    volumeId: null,\n    // 独立章节，未分配到卷宗\n    title: '番外：师父的过往',\n    chapterNumber: null,\n    content: '很久以前，师父也是一个普通的修炼者...',\n    wordCount: 2800,\n    status: 'draft',\n    outline: '讲述师父的背景故事',\n    createdAt: '2024-01-21',\n    updatedAt: '2024-01-21'\n  }];\n  useEffect(() => {\n    setVolumes(mockVolumes);\n    setChapters(mockChapters);\n  }, []);\n\n  // 统计数据\n  const totalVolumes = volumes.length;\n  const totalChapters = chapters.length;\n  const completedChapters = chapters.filter(c => c.status === 'completed' || c.status === 'published').length;\n  const totalWords = chapters.reduce((sum, chapter) => sum + (chapter.wordCount || 0), 0);\n  const independentChapters = chapters.filter(c => !c.volumeId).length;\n\n  // 卷宗表格列配置\n  const volumeColumns = [{\n    title: '卷宗标题',\n    dataIndex: 'title',\n    key: 'title',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: [\"\\u7B2C\", record.volumeNumber, \"\\u5377\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '进度',\n    dataIndex: 'progress',\n    key: 'progress',\n    render: (progress, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Progress, {\n        percent: progress,\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        type: \"secondary\",\n        children: [record.completedChapters, \"/\", record.totalChapters, \" \\u7AE0\\u8282\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.progress - b.progress\n  }, {\n    title: '字数',\n    dataIndex: 'totalWords',\n    key: 'totalWords',\n    render: (words, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        children: [words.toLocaleString(), \" \\u5B57\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this), record.targetWords && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: [\"\\u76EE\\u6807: \", record.targetWords.toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.totalWords - b.totalWords\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: volumeStatusConfig[status].color,\n      children: volumeStatusConfig[status].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 242,\n      columnNumber: 9\n    }, this),\n    filters: Object.entries(volumeStatusConfig).map(([key, config]) => ({\n      text: config.text,\n      value: key\n    })),\n    onFilter: (value, record) => record.status === value\n  }, {\n    title: '更新时间',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt',\n    sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u7AE0\\u8282\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleViewVolumeChapters(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\\u5377\\u5B97\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEditVolume(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u6DFB\\u52A0\\u7AE0\\u8282\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAddChapter(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"AI\\u751F\\u6210\\u5927\\u7EB2\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAIGenerateOutline(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u5377\\u5B97\\u5417\\uFF1F\\u8FD9\\u5C06\\u540C\\u65F6\\u5220\\u9664\\u5176\\u4E0B\\u6240\\u6709\\u7AE0\\u8282\\u3002\",\n        onConfirm: () => handleDeleteVolume(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 章节表格列配置\n  const chapterColumns = [{\n    title: '章节标题',\n    dataIndex: 'title',\n    key: 'title',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 11\n      }, this), record.chapterNumber && /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: [\"\\u7B2C\", record.chapterNumber, \"\\u7AE0\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 317,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '所属卷宗',\n    dataIndex: 'volumeId',\n    key: 'volumeId',\n    render: volumeId => {\n      if (!volumeId) {\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"orange\",\n          children: \"\\u72EC\\u7ACB\\u7AE0\\u8282\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 18\n        }, this);\n      }\n      const volume = volumes.find(v => v.id === volumeId);\n      return volume ? /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"blue\",\n        children: volume.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Tag, {\n        color: \"red\",\n        children: \"\\u672A\\u77E5\\u5377\\u5B97\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 11\n      }, this);\n    },\n    filters: [{\n      text: '独立章节',\n      value: null\n    }, ...volumes.map(v => ({\n      text: v.title,\n      value: v.id\n    }))],\n    onFilter: (value, record) => record.volumeId === value\n  }, {\n    title: '字数',\n    dataIndex: 'wordCount',\n    key: 'wordCount',\n    render: count => /*#__PURE__*/_jsxDEV(Text, {\n      type: count === 0 ? 'secondary' : 'default',\n      children: [count.toLocaleString(), \" \\u5B57\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 352,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.wordCount - b.wordCount\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: chapterStatusConfig[status].color,\n      children: chapterStatusConfig[status].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 9\n    }, this),\n    filters: Object.entries(chapterStatusConfig).map(([key, config]) => ({\n      text: config.text,\n      value: key\n    })),\n    onFilter: (value, record) => record.status === value\n  }, {\n    title: '更新时间',\n    dataIndex: 'updatedAt',\n    key: 'updatedAt',\n    sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleViewChapter(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEditChapter(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"AI\\u7EED\\u5199\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAIContinueChapter(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u5206\\u914D\\u5230\\u5377\\u5B97\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAssignToVolume(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 405,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u7AE0\\u8282\\u5417\\uFF1F\",\n        onConfirm: () => handleDeleteChapter(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 383,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理函数\n  const handleCreateVolume = () => {\n    setEditingVolume(null);\n    volumeForm.resetFields();\n    setVolumeModalVisible(true);\n  };\n  const handleEditVolume = volume => {\n    setEditingVolume(volume);\n    volumeForm.setFieldsValue(volume);\n    setVolumeModalVisible(true);\n  };\n  const handleViewVolumeChapters = volume => {\n    setSelectedVolumeId(volume.id);\n    message.info(`查看卷宗《${volume.title}》的章节`);\n  };\n  const handleAddChapter = volume => {\n    setSelectedVolumeId(volume.id);\n    setEditingChapter(null);\n    chapterForm.resetFields();\n    chapterForm.setFieldsValue({\n      volumeId: volume.id\n    });\n    setChapterModalVisible(true);\n  };\n  const handleAIGenerateOutline = volume => {\n    message.info(`AI生成卷宗《${volume.title}》大纲功能`);\n  };\n  const handleDeleteVolume = async id => {\n    try {\n      // 调用后端API删除卷宗\n      await axios.delete(`/api/project-data/projects/${projectId}/data/volume/${id}`);\n\n      // 删除成功后从列表中移除\n      setVolumes(volumes.filter(v => v.id !== id));\n      setChapters(chapters.filter(c => c.volumeId !== id));\n      message.success('卷宗删除成功');\n    } catch (error) {\n      console.error('删除卷宗失败:', error);\n      message.error('删除卷宗失败');\n    }\n  };\n  const handleVolumeModalOk = async () => {\n    try {\n      const values = await volumeForm.validateFields();\n      setLoading(true);\n      if (editingVolume) {\n        // 编辑卷宗\n        setVolumes(volumes.map(v => v.id === editingVolume.id ? {\n          ...v,\n          ...values,\n          updatedAt: new Date().toISOString().split('T')[0]\n        } : v));\n        message.success('卷宗更新成功');\n      } else {\n        // 新建卷宗\n        const newVolume = {\n          id: Date.now(),\n          ...values,\n          totalChapters: 0,\n          completedChapters: 0,\n          totalWords: 0,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setVolumes([...volumes, newVolume]);\n        message.success('卷宗创建成功');\n      }\n      setVolumeModalVisible(false);\n      volumeForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleChapterModalOk = async () => {\n    try {\n      const values = await chapterForm.validateFields();\n      setLoading(true);\n      if (editingChapter) {\n        // 编辑章节\n        setChapters(chapters.map(c => c.id === editingChapter.id ? {\n          ...c,\n          ...values,\n          updatedAt: new Date().toISOString().split('T')[0]\n        } : c));\n        message.success('章节更新成功');\n      } else {\n        // 新建章节\n        const newChapter = {\n          id: Date.now(),\n          ...values,\n          wordCount: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setChapters([...chapters, newChapter]);\n        message.success('章节创建成功');\n      }\n      setChapterModalVisible(false);\n      chapterForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 章节相关处理函数\n  const handleCreateChapter = () => {\n    setEditingChapter(null);\n    chapterForm.resetFields();\n    chapterForm.setFieldsValue({\n      volumeId: null\n    }); // 默认为独立章节\n    setChapterModalVisible(true);\n  };\n  const handleViewChapter = chapter => {\n    const volumeId = chapter.volumeId || 'independent';\n    navigate(`/projects/${projectId}/volumes/${volumeId}/chapters/${chapter.id}`);\n  };\n  const handleEditChapter = chapter => {\n    setEditingChapter(chapter);\n    chapterForm.setFieldsValue(chapter);\n    setChapterModalVisible(true);\n  };\n  const handleAIContinueChapter = chapter => {\n    message.info(`AI续写功能：${chapter.title}`);\n  };\n  const handleAssignToVolume = chapter => {\n    setAssigningChapter(chapter);\n    assignForm.setFieldsValue({\n      volumeId: chapter.volumeId\n    });\n    setAssignModalVisible(true);\n  };\n  const handleAssignModalOk = async () => {\n    try {\n      const values = await assignForm.validateFields();\n      setLoading(true);\n      setChapters(chapters.map(c => c.id === assigningChapter.id ? {\n        ...c,\n        volumeId: values.volumeId,\n        updatedAt: new Date().toISOString().split('T')[0]\n      } : c));\n      message.success('章节分配成功');\n      setAssignModalVisible(false);\n      assignForm.resetFields();\n    } catch (error) {\n      console.error('分配失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteChapter = async id => {\n    try {\n      // 调用后端API删除章节\n      await axios.delete(`/api/project-data/projects/${projectId}/data/chapter/${id}`);\n\n      // 删除成功后从列表中移除\n      setChapters(chapters.filter(c => c.id !== id));\n      message.success('章节删除成功');\n    } catch (error) {\n      console.error('删除章节失败:', error);\n      message.error('删除章节失败');\n    }\n  };\n\n  // 获取指定卷宗的章节\n  const getVolumeChapters = volumeId => {\n    return chapters.filter(c => c.volumeId === volumeId);\n  };\n\n  // 获取独立章节\n  const getIndependentChapters = () => {\n    return chapters.filter(c => !c.volumeId);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u5377\\u5B97\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 626,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 625,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5377\\u5B97\\u6570\",\n            value: totalVolumes,\n            prefix: /*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 631,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u7AE0\\u8282\\u6570\",\n            value: totalChapters,\n            prefix: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u72EC\\u7ACB\\u7AE0\\u8282\",\n            value: independentChapters,\n            prefix: /*#__PURE__*/_jsxDEV(FileTextOutlined, {\n              style: {\n                color: '#fa8c16'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 649,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5B57\\u6570\",\n            value: totalWords,\n            suffix: \"\\u5B57\",\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 630,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onChange: setActiveTab,\n        items: [{\n          key: 'volumes',\n          label: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(FolderOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 19\n            }, this), \"\\u5377\\u5B97\\u7BA1\\u7406\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"toolbar\",\n              style: {\n                marginBottom: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"toolbar-left\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 689,\n                    columnNumber: 31\n                  }, this),\n                  onClick: handleCreateVolume,\n                  children: \"\\u65B0\\u5EFA\\u5377\\u5B97\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 687,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 686,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 685,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Table, {\n              columns: volumeColumns,\n              dataSource: volumes,\n              rowKey: \"id\",\n              loading: loading,\n              pagination: {\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: total => `共 ${total} 个卷宗`\n              },\n              expandable: {\n                expandedRowRender: volume => {\n                  const volumeChapters = getVolumeChapters(volume.id);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      margin: 0\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Divider, {\n                      orientation: \"left\",\n                      children: \"\\u7AE0\\u8282\\u5217\\u8868\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 712,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(List, {\n                      size: \"small\",\n                      dataSource: volumeChapters,\n                      renderItem: chapter => /*#__PURE__*/_jsxDEV(List.Item, {\n                        actions: [/*#__PURE__*/_jsxDEV(Button, {\n                          type: \"link\",\n                          size: \"small\",\n                          onClick: () => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`),\n                          children: \"\\u7F16\\u8F91\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 719,\n                          columnNumber: 37\n                        }, this), /*#__PURE__*/_jsxDEV(Button, {\n                          type: \"link\",\n                          size: \"small\",\n                          onClick: () => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`),\n                          children: \"\\u67E5\\u770B\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 726,\n                          columnNumber: 37\n                        }, this)],\n                        children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                          avatar: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 736,\n                            columnNumber: 45\n                          }, this),\n                          title: /*#__PURE__*/_jsxDEV(Space, {\n                            children: [chapter.title, /*#__PURE__*/_jsxDEV(Tag, {\n                              color: chapterStatusConfig[chapter.status].color,\n                              children: chapterStatusConfig[chapter.status].text\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 740,\n                              columnNumber: 41\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 738,\n                            columnNumber: 39\n                          }, this),\n                          description: `字数: ${chapter.wordCount} | 更新: ${chapter.updatedAt}`\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 735,\n                          columnNumber: 35\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 717,\n                        columnNumber: 33\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 713,\n                      columnNumber: 29\n                    }, this), volumeChapters.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        textAlign: 'center',\n                        padding: '20px',\n                        color: '#999'\n                      },\n                      children: \"\\u6682\\u65E0\\u7AE0\\u8282\\uFF0C\\u70B9\\u51FB\\u4E0A\\u65B9\\\"\\u6DFB\\u52A0\\u7AE0\\u8282\\\"\\u6309\\u94AE\\u521B\\u5EFA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 751,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 711,\n                    columnNumber: 27\n                  }, this);\n                },\n                rowExpandable: () => true\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 684,\n            columnNumber: 17\n          }, this)\n        }, {\n          key: 'chapters',\n          label: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 19\n            }, this), \"\\u7AE0\\u8282\\u7BA1\\u7406\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 17\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"toolbar\",\n              style: {\n                marginBottom: 16\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"toolbar-left\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 778,\n                    columnNumber: 31\n                  }, this),\n                  onClick: handleCreateChapter,\n                  children: \"\\u65B0\\u5EFA\\u7AE0\\u8282\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 776,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 775,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 774,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Table, {\n              columns: chapterColumns,\n              dataSource: chapters,\n              rowKey: \"id\",\n              loading: loading,\n              pagination: {\n                showSizeChanger: true,\n                showQuickJumper: true,\n                showTotal: total => `共 ${total} 个章节`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 786,\n              columnNumber: 19\n            }, this), getIndependentChapters().length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: 24\n              },\n              children: [/*#__PURE__*/_jsxDEV(Divider, {\n                orientation: \"left\",\n                children: \"\\u72EC\\u7ACB\\u7AE0\\u8282\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 800,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(List, {\n                dataSource: getIndependentChapters(),\n                renderItem: chapter => /*#__PURE__*/_jsxDEV(List.Item, {\n                  actions: [/*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    onClick: () => handleViewChapter(chapter),\n                    children: \"\\u67E5\\u770B\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 806,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    onClick: () => handleEditChapter(chapter),\n                    children: \"\\u7F16\\u8F91\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 812,\n                    columnNumber: 31\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    type: \"link\",\n                    onClick: () => handleAssignToVolume(chapter),\n                    children: \"\\u5206\\u914D\\u5230\\u5377\\u5B97\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 818,\n                    columnNumber: 31\n                  }, this)],\n                  children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                    avatar: /*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 827,\n                      columnNumber: 39\n                    }, this),\n                    title: /*#__PURE__*/_jsxDEV(Space, {\n                      children: [chapter.title, /*#__PURE__*/_jsxDEV(Tag, {\n                        color: chapterStatusConfig[chapter.status].color,\n                        children: chapterStatusConfig[chapter.status].text\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 831,\n                        columnNumber: 35\n                      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                        color: \"orange\",\n                        children: \"\\u72EC\\u7ACB\\u7AE0\\u8282\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 834,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 829,\n                      columnNumber: 33\n                    }, this),\n                    description: `字数: ${chapter.wordCount} | 更新: ${chapter.updatedAt}`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 826,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 804,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 801,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 799,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 773,\n            columnNumber: 17\n          }, this)\n        }]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 671,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 670,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingVolume ? '编辑卷宗' : '新建卷宗',\n      open: volumeModalVisible,\n      onOk: handleVolumeModalOk,\n      onCancel: () => {\n        setVolumeModalVisible(false);\n        volumeForm.resetFields();\n      },\n      confirmLoading: loading,\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: volumeForm,\n        layout: \"vertical\",\n        initialValues: {\n          status: 'planning'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 16,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u5377\\u5B97\\u6807\\u9898\",\n              rules: [{\n                required: true,\n                message: '请输入卷宗标题'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5377\\u5B97\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 875,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"volumeNumber\",\n              label: \"\\u5377\\u5E8F\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入卷序号'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 868,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"summary\",\n          label: \"\\u5377\\u5B97\\u6458\\u8981\",\n          rules: [{\n            required: true,\n            message: '请输入卷宗摘要'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u7B80\\u8981\\u63CF\\u8FF0\\u672C\\u5377\\u7684\\u4E3B\\u8981\\u5185\\u5BB9\\u548C\\u5267\\u60C5\\u53D1\\u5C55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 894,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 889,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择卷宗状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: Object.entries(volumeStatusConfig).map(([key, config]) => /*#__PURE__*/_jsxDEV(Option, {\n                  value: key,\n                  children: config.text\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 909,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"targetWords\",\n              label: \"\\u76EE\\u6807\\u5B57\\u6570\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"80000\",\n                suffix: \"\\u5B57\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 919,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 915,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 900,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"outline\",\n          label: \"\\u5377\\u5B97\\u5927\\u7EB2\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 6,\n            placeholder: \"\\u8BE6\\u7EC6\\u63CF\\u8FF0\\u672C\\u5377\\u7684\\u7AE0\\u8282\\u5B89\\u6392\\u548C\\u5267\\u60C5\\u53D1\\u5C55...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 928,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 924,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 863,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 852,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingChapter ? '编辑章节' : '新建章节',\n      open: chapterModalVisible,\n      onOk: handleChapterModalOk,\n      onCancel: () => {\n        setChapterModalVisible(false);\n        chapterForm.resetFields();\n      },\n      confirmLoading: loading,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: chapterForm,\n        layout: \"vertical\",\n        initialValues: {\n          status: 'planning'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"volumeId\",\n          label: \"\\u6240\\u5C5E\\u5377\\u5B97\",\n          help: \"\\u7559\\u7A7A\\u5219\\u521B\\u5EFA\\u4E3A\\u72EC\\u7ACB\\u7AE0\\u8282\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5377\\u5B97\\uFF08\\u53EF\\u7559\\u7A7A\\u521B\\u5EFA\\u72EC\\u7ACB\\u7AE0\\u8282\\uFF09\",\n            allowClear: true,\n            children: volumes.map(volume => /*#__PURE__*/_jsxDEV(Option, {\n              value: volume.id,\n              children: volume.title\n            }, volume.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 960,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 958,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 953,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 16,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u7AE0\\u8282\\u6807\\u9898\",\n              rules: [{\n                required: true,\n                message: '请输入章节标题'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7AE0\\u8282\\u6807\\u9898\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 974,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 968,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"chapterNumber\",\n              label: \"\\u7AE0\\u8282\\u5E8F\\u53F7\",\n              help: \"\\u72EC\\u7ACB\\u7AE0\\u8282\\u53EF\\u7559\\u7A7A\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"number\",\n                placeholder: \"1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 978,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 977,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 967,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择章节状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            children: Object.entries(chapterStatusConfig).map(([key, config]) => /*#__PURE__*/_jsxDEV(Option, {\n              value: key,\n              children: config.text\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 995,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"outline\",\n          label: \"\\u7AE0\\u8282\\u5927\\u7EB2\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u7B80\\u8981\\u63CF\\u8FF0\\u672C\\u7AE0\\u8282\\u7684\\u4E3B\\u8981\\u5185\\u5BB9\\u548C\\u60C5\\u8282\\u53D1\\u5C55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1004,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1000,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 948,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 937,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u5206\\u914D\\u7AE0\\u8282\\u5230\\u5377\\u5B97\",\n      open: assignModalVisible,\n      onOk: handleAssignModalOk,\n      onCancel: () => {\n        setAssignModalVisible(false);\n        assignForm.resetFields();\n      },\n      confirmLoading: loading,\n      width: 400,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: assignForm,\n        layout: \"vertical\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            strong: true,\n            children: \"\\u7AE0\\u8282\\uFF1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1029,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Text, {\n            children: assigningChapter === null || assigningChapter === void 0 ? void 0 : assigningChapter.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1030,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1028,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"volumeId\",\n          label: \"\\u76EE\\u6807\\u5377\\u5B97\",\n          help: \"\\u9009\\u62E9\\u8981\\u5206\\u914D\\u5230\\u7684\\u5377\\u5B97\\uFF0C\\u7559\\u7A7A\\u5219\\u8BBE\\u4E3A\\u72EC\\u7ACB\\u7AE0\\u8282\",\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5377\\u5B97\",\n            allowClear: true,\n            children: volumes.map(volume => /*#__PURE__*/_jsxDEV(Option, {\n              value: volume.id,\n              children: volume.title\n            }, volume.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1040,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1038,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1033,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1024,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1013,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 624,\n    columnNumber: 5\n  }, this);\n};\n_s(VolumeList, \"zG2YAMuBfcqc/J3hy7qbfwwkwCk=\", false, function () {\n  return [useParams, useNavigate, Form.useForm, Form.useForm, Form.useForm];\n});\n_c = VolumeList;\nexport default VolumeList;\nvar _c;\n$RefreshReg$(_c, \"VolumeList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "axios", "Card", "Typography", "<PERSON><PERSON>", "Table", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "Progress", "<PERSON><PERSON><PERSON>", "Row", "Col", "Statistic", "Collapse", "List", "Divider", "Tabs", "Empty", "PlusOutlined", "FileTextOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "RobotOutlined", "BookOutlined", "ClockCircleOutlined", "CheckCircleOutlined", "FolderOutlined", "OrderedListOutlined", "BarChartOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "Panel", "TabPane", "VolumeList", "_s", "id", "projectId", "navigate", "volumes", "setVolumes", "chapters", "setChapters", "loading", "setLoading", "volumeModalVisible", "setVolumeModalVisible", "chapterModalVisible", "setChapterModalVisible", "assignModalVisible", "setAssignModalVisible", "editingVolume", "setEditingVolume", "editing<PERSON><PERSON>pter", "setEditingChapter", "assigning<PERSON><PERSON><PERSON><PERSON>", "setAssigningChapter", "selectedVolumeId", "setSelectedVolumeId", "activeTab", "setActiveTab", "volumeForm", "useForm", "chapterForm", "assignForm", "volumeStatusConfig", "planning", "color", "text", "writing", "completed", "reviewing", "revised", "published", "chapterStatusConfig", "draft", "mockVolumes", "title", "volumeNumber", "status", "summary", "totalChapters", "completedChapters", "totalWords", "targetWords", "progress", "createdAt", "updatedAt", "mockChapters", "volumeId", "chapterNumber", "content", "wordCount", "outline", "totalVolumes", "length", "filter", "c", "reduce", "sum", "chapter", "independentChapters", "volumeColumns", "dataIndex", "key", "render", "record", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "strong", "percent", "size", "type", "sorter", "a", "b", "words", "toLocaleString", "filters", "Object", "entries", "map", "config", "value", "onFilter", "Date", "_", "icon", "onClick", "handleViewVolumeChapters", "handleEditVolume", "handleAddChapter", "handleAIGenerateOutline", "onConfirm", "handleDeleteVolume", "okText", "cancelText", "danger", "chapterColumns", "volume", "find", "v", "count", "handleViewChapter", "handleEditChapter", "handleAIContinueChapter", "handleAssignToVolume", "handleDeleteChapter", "handleCreateVolume", "resetFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "info", "delete", "success", "error", "console", "handleVolumeModalOk", "values", "validateFields", "toISOString", "split", "newVolume", "now", "handleChapterModalOk", "newChapter", "handleCreateChapter", "handleAssignModalOk", "getVolumeChapters", "getIndependentChapters", "className", "level", "gutter", "style", "marginBottom", "span", "prefix", "suffix", "active<PERSON><PERSON>", "onChange", "items", "label", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "expandable", "expandedRowRender", "volumeChapters", "margin", "orientation", "renderItem", "<PERSON><PERSON>", "actions", "Meta", "avatar", "description", "textAlign", "padding", "rowExpandable", "marginTop", "open", "onOk", "onCancel", "confirmLoading", "width", "form", "layout", "initialValues", "name", "rules", "required", "placeholder", "rows", "help", "allowClear", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/VolumeList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  Card,\n  Typography,\n  Button,\n  Table,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Progress,\n  Tooltip,\n  Row,\n  Col,\n  Statistic,\n  Collapse,\n  List,\n  Divider,\n  Tabs,\n  Empty\n} from 'antd';\nimport {\n  PlusOutlined,\n  FileTextOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  RobotOutlined,\n  BookOutlined,\n  ClockCircleOutlined,\n  CheckCircleOutlined,\n  FolderOutlined,\n  OrderedListOutlined,\n  BarChartOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\nconst { Panel } = Collapse;\nconst { TabPane } = Tabs;\n\nconst VolumeList = () => {\n  const { id: projectId } = useParams();\n  const navigate = useNavigate();\n  const [volumes, setVolumes] = useState([]);\n  const [chapters, setChapters] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [volumeModalVisible, setVolumeModalVisible] = useState(false);\n  const [chapterModalVisible, setChapterModalVisible] = useState(false);\n  const [assignModalVisible, setAssignModalVisible] = useState(false);\n  const [editingVolume, setEditingVolume] = useState(null);\n  const [editingChapter, setEditingChapter] = useState(null);\n  const [assigningChapter, setAssigningChapter] = useState(null);\n  const [selectedVolumeId, setSelectedVolumeId] = useState(null);\n  const [activeTab, setActiveTab] = useState('volumes');\n  const [volumeForm] = Form.useForm();\n  const [chapterForm] = Form.useForm();\n  const [assignForm] = Form.useForm();\n\n  // 状态配置\n  const volumeStatusConfig = {\n    planning: { color: 'blue', text: '规划中' },\n    writing: { color: 'orange', text: '写作中' },\n    completed: { color: 'green', text: '已完成' },\n    reviewing: { color: 'purple', text: '审阅中' },\n    revised: { color: 'cyan', text: '已修订' },\n    published: { color: 'success', text: '已发布' }\n  };\n\n  const chapterStatusConfig = {\n    planning: { color: 'blue', text: '规划中' },\n    draft: { color: 'orange', text: '草稿' },\n    writing: { color: 'processing', text: '写作中' },\n    completed: { color: 'green', text: '已完成' },\n    published: { color: 'success', text: '已发布' }\n  };\n\n  // 模拟卷宗数据\n  const mockVolumes = [\n    {\n      id: 1,\n      title: '第一卷：初入修仙界',\n      volumeNumber: 1,\n      status: 'writing',\n      summary: '主角初次踏入修仙世界，遇到师父，开始修炼之路',\n      totalChapters: 10,\n      completedChapters: 6,\n      totalWords: 45000,\n      targetWords: 80000,\n      progress: 60,\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-20'\n    },\n    {\n      id: 2,\n      title: '第二卷：宗门试炼',\n      volumeNumber: 2,\n      status: 'planning',\n      summary: '主角参加宗门入门试炼，展现天赋，结识同门',\n      totalChapters: 8,\n      completedChapters: 0,\n      totalWords: 0,\n      targetWords: 60000,\n      progress: 0,\n      createdAt: '2024-01-21',\n      updatedAt: '2024-01-21'\n    }\n  ];\n\n  // 模拟章节数据\n  const mockChapters = [\n    {\n      id: 1,\n      volumeId: 1,\n      title: '第一章：觉醒',\n      chapterNumber: 1,\n      content: '在这个充满灵气的世界里，少年踏上了修仙之路...',\n      wordCount: 3500,\n      status: 'published',\n      outline: '主角初次接触修仙世界，遇到第一位师父',\n      createdAt: '2024-01-15',\n      updatedAt: '2024-01-16'\n    },\n    {\n      id: 2,\n      volumeId: 1,\n      title: '第二章：师父',\n      chapterNumber: 2,\n      content: '经过数月的修炼，主角终于感受到了灵气的存在...',\n      wordCount: 4200,\n      status: 'completed',\n      outline: '主角开始正式修炼，学习基础功法',\n      createdAt: '2024-01-17',\n      updatedAt: '2024-01-18'\n    },\n    {\n      id: 3,\n      volumeId: 1,\n      title: '第三章：修炼',\n      chapterNumber: 3,\n      content: '',\n      wordCount: 0,\n      status: 'planning',\n      outline: '主角参加宗门入门试炼，展现天赋',\n      createdAt: '2024-01-19',\n      updatedAt: '2024-01-19'\n    },\n    {\n      id: 4,\n      volumeId: 2,\n      title: '第四章：试炼开始',\n      chapterNumber: 1,\n      content: '',\n      wordCount: 0,\n      status: 'planning',\n      outline: '宗门试炼正式开始，各路天才汇聚',\n      createdAt: '2024-01-20',\n      updatedAt: '2024-01-20'\n    },\n    {\n      id: 5,\n      volumeId: null, // 独立章节，未分配到卷宗\n      title: '番外：师父的过往',\n      chapterNumber: null,\n      content: '很久以前，师父也是一个普通的修炼者...',\n      wordCount: 2800,\n      status: 'draft',\n      outline: '讲述师父的背景故事',\n      createdAt: '2024-01-21',\n      updatedAt: '2024-01-21'\n    }\n  ];\n\n  useEffect(() => {\n    setVolumes(mockVolumes);\n    setChapters(mockChapters);\n  }, []);\n\n  // 统计数据\n  const totalVolumes = volumes.length;\n  const totalChapters = chapters.length;\n  const completedChapters = chapters.filter(c => c.status === 'completed' || c.status === 'published').length;\n  const totalWords = chapters.reduce((sum, chapter) => sum + (chapter.wordCount || 0), 0);\n  const independentChapters = chapters.filter(c => !c.volumeId).length;\n\n  // 卷宗表格列配置\n  const volumeColumns = [\n    {\n      title: '卷宗标题',\n      dataIndex: 'title',\n      key: 'title',\n      render: (text, record) => (\n        <Space>\n          <FolderOutlined />\n          <Text strong>{text}</Text>\n          <Tag color=\"blue\">第{record.volumeNumber}卷</Tag>\n        </Space>\n      )\n    },\n    {\n      title: '进度',\n      dataIndex: 'progress',\n      key: 'progress',\n      render: (progress, record) => (\n        <div>\n          <Progress percent={progress} size=\"small\" />\n          <Text type=\"secondary\">\n            {record.completedChapters}/{record.totalChapters} 章节\n          </Text>\n        </div>\n      ),\n      sorter: (a, b) => a.progress - b.progress\n    },\n    {\n      title: '字数',\n      dataIndex: 'totalWords',\n      key: 'totalWords',\n      render: (words, record) => (\n        <div>\n          <Text>{words.toLocaleString()} 字</Text>\n          {record.targetWords && (\n            <div>\n              <Text type=\"secondary\">目标: {record.targetWords.toLocaleString()}</Text>\n            </div>\n          )}\n        </div>\n      ),\n      sorter: (a, b) => a.totalWords - b.totalWords\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={volumeStatusConfig[status].color}>\n          {volumeStatusConfig[status].text}\n        </Tag>\n      ),\n      filters: Object.entries(volumeStatusConfig).map(([key, config]) => ({\n        text: config.text,\n        value: key\n      })),\n      onFilter: (value, record) => record.status === value\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"查看章节\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewVolumeChapters(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑卷宗\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditVolume(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"添加章节\">\n            <Button\n              type=\"text\"\n              icon={<PlusOutlined />}\n              onClick={() => handleAddChapter(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"AI生成大纲\">\n            <Button\n              type=\"text\"\n              icon={<RobotOutlined />}\n              onClick={() => handleAIGenerateOutline(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个卷宗吗？这将同时删除其下所有章节。\"\n            onConfirm={() => handleDeleteVolume(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  // 章节表格列配置\n  const chapterColumns = [\n    {\n      title: '章节标题',\n      dataIndex: 'title',\n      key: 'title',\n      render: (text, record) => (\n        <Space>\n          <FileTextOutlined />\n          <Text strong>{text}</Text>\n          {record.chapterNumber && (\n            <Tag color=\"blue\">第{record.chapterNumber}章</Tag>\n          )}\n        </Space>\n      )\n    },\n    {\n      title: '所属卷宗',\n      dataIndex: 'volumeId',\n      key: 'volumeId',\n      render: (volumeId) => {\n        if (!volumeId) {\n          return <Tag color=\"orange\">独立章节</Tag>;\n        }\n        const volume = volumes.find(v => v.id === volumeId);\n        return volume ? (\n          <Tag color=\"blue\">{volume.title}</Tag>\n        ) : (\n          <Tag color=\"red\">未知卷宗</Tag>\n        );\n      },\n      filters: [\n        { text: '独立章节', value: null },\n        ...volumes.map(v => ({ text: v.title, value: v.id }))\n      ],\n      onFilter: (value, record) => record.volumeId === value\n    },\n    {\n      title: '字数',\n      dataIndex: 'wordCount',\n      key: 'wordCount',\n      render: (count) => (\n        <Text type={count === 0 ? 'secondary' : 'default'}>\n          {count.toLocaleString()} 字\n        </Text>\n      ),\n      sorter: (a, b) => a.wordCount - b.wordCount\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={chapterStatusConfig[status].color}>\n          {chapterStatusConfig[status].text}\n        </Tag>\n      ),\n      filters: Object.entries(chapterStatusConfig).map(([key, config]) => ({\n        text: config.text,\n        value: key\n      })),\n      onFilter: (value, record) => record.status === value\n    },\n    {\n      title: '更新时间',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n      sorter: (a, b) => new Date(a.updatedAt) - new Date(b.updatedAt)\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"查看\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleViewChapter(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEditChapter(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"AI续写\">\n            <Button\n              type=\"text\"\n              icon={<RobotOutlined />}\n              onClick={() => handleAIContinueChapter(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"分配到卷宗\">\n            <Button\n              type=\"text\"\n              icon={<FolderOutlined />}\n              onClick={() => handleAssignToVolume(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个章节吗？\"\n            onConfirm={() => handleDeleteChapter(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  // 处理函数\n  const handleCreateVolume = () => {\n    setEditingVolume(null);\n    volumeForm.resetFields();\n    setVolumeModalVisible(true);\n  };\n\n  const handleEditVolume = (volume) => {\n    setEditingVolume(volume);\n    volumeForm.setFieldsValue(volume);\n    setVolumeModalVisible(true);\n  };\n\n  const handleViewVolumeChapters = (volume) => {\n    setSelectedVolumeId(volume.id);\n    message.info(`查看卷宗《${volume.title}》的章节`);\n  };\n\n  const handleAddChapter = (volume) => {\n    setSelectedVolumeId(volume.id);\n    setEditingChapter(null);\n    chapterForm.resetFields();\n    chapterForm.setFieldsValue({ volumeId: volume.id });\n    setChapterModalVisible(true);\n  };\n\n  const handleAIGenerateOutline = (volume) => {\n    message.info(`AI生成卷宗《${volume.title}》大纲功能`);\n  };\n\n  const handleDeleteVolume = async (id) => {\n    try {\n      // 调用后端API删除卷宗\n      await axios.delete(`/api/project-data/projects/${projectId}/data/volume/${id}`);\n\n      // 删除成功后从列表中移除\n      setVolumes(volumes.filter(v => v.id !== id));\n      setChapters(chapters.filter(c => c.volumeId !== id));\n      message.success('卷宗删除成功');\n    } catch (error) {\n      console.error('删除卷宗失败:', error);\n      message.error('删除卷宗失败');\n    }\n  };\n\n  const handleVolumeModalOk = async () => {\n    try {\n      const values = await volumeForm.validateFields();\n      setLoading(true);\n\n      if (editingVolume) {\n        // 编辑卷宗\n        setVolumes(volumes.map(v =>\n          v.id === editingVolume.id\n            ? { ...v, ...values, updatedAt: new Date().toISOString().split('T')[0] }\n            : v\n        ));\n        message.success('卷宗更新成功');\n      } else {\n        // 新建卷宗\n        const newVolume = {\n          id: Date.now(),\n          ...values,\n          totalChapters: 0,\n          completedChapters: 0,\n          totalWords: 0,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setVolumes([...volumes, newVolume]);\n        message.success('卷宗创建成功');\n      }\n\n      setVolumeModalVisible(false);\n      volumeForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleChapterModalOk = async () => {\n    try {\n      const values = await chapterForm.validateFields();\n      setLoading(true);\n\n      if (editingChapter) {\n        // 编辑章节\n        setChapters(chapters.map(c =>\n          c.id === editingChapter.id\n            ? { ...c, ...values, updatedAt: new Date().toISOString().split('T')[0] }\n            : c\n        ));\n        message.success('章节更新成功');\n      } else {\n        // 新建章节\n        const newChapter = {\n          id: Date.now(),\n          ...values,\n          wordCount: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setChapters([...chapters, newChapter]);\n        message.success('章节创建成功');\n      }\n\n      setChapterModalVisible(false);\n      chapterForm.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 章节相关处理函数\n  const handleCreateChapter = () => {\n    setEditingChapter(null);\n    chapterForm.resetFields();\n    chapterForm.setFieldsValue({ volumeId: null }); // 默认为独立章节\n    setChapterModalVisible(true);\n  };\n\n  const handleViewChapter = (chapter) => {\n    const volumeId = chapter.volumeId || 'independent';\n    navigate(`/projects/${projectId}/volumes/${volumeId}/chapters/${chapter.id}`);\n  };\n\n  const handleEditChapter = (chapter) => {\n    setEditingChapter(chapter);\n    chapterForm.setFieldsValue(chapter);\n    setChapterModalVisible(true);\n  };\n\n  const handleAIContinueChapter = (chapter) => {\n    message.info(`AI续写功能：${chapter.title}`);\n  };\n\n  const handleAssignToVolume = (chapter) => {\n    setAssigningChapter(chapter);\n    assignForm.setFieldsValue({ volumeId: chapter.volumeId });\n    setAssignModalVisible(true);\n  };\n\n  const handleAssignModalOk = async () => {\n    try {\n      const values = await assignForm.validateFields();\n      setLoading(true);\n\n      setChapters(chapters.map(c =>\n        c.id === assigningChapter.id\n          ? { ...c, volumeId: values.volumeId, updatedAt: new Date().toISOString().split('T')[0] }\n          : c\n      ));\n\n      message.success('章节分配成功');\n      setAssignModalVisible(false);\n      assignForm.resetFields();\n    } catch (error) {\n      console.error('分配失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteChapter = async (id) => {\n    try {\n      // 调用后端API删除章节\n      await axios.delete(`/api/project-data/projects/${projectId}/data/chapter/${id}`);\n\n      // 删除成功后从列表中移除\n      setChapters(chapters.filter(c => c.id !== id));\n      message.success('章节删除成功');\n    } catch (error) {\n      console.error('删除章节失败:', error);\n      message.error('删除章节失败');\n    }\n  };\n\n  // 获取指定卷宗的章节\n  const getVolumeChapters = (volumeId) => {\n    return chapters.filter(c => c.volumeId === volumeId);\n  };\n\n  // 获取独立章节\n  const getIndependentChapters = () => {\n    return chapters.filter(c => !c.volumeId);\n  };\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">卷宗管理</Title>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总卷宗数\"\n              value={totalVolumes}\n              prefix={<FolderOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总章节数\"\n              value={totalChapters}\n              prefix={<BookOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"独立章节\"\n              value={independentChapters}\n              prefix={<FileTextOutlined style={{ color: '#fa8c16' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总字数\"\n              value={totalWords}\n              suffix=\"字\"\n              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Tabs\n          activeKey={activeTab}\n          onChange={setActiveTab}\n          items={[\n            {\n              key: 'volumes',\n              label: (\n                <span>\n                  <FolderOutlined />\n                  卷宗管理\n                </span>\n              ),\n              children: (\n                <div>\n                  <div className=\"toolbar\" style={{ marginBottom: 16 }}>\n                    <div className=\"toolbar-left\">\n                      <Button\n                        type=\"primary\"\n                        icon={<PlusOutlined />}\n                        onClick={handleCreateVolume}\n                      >\n                        新建卷宗\n                      </Button>\n                    </div>\n                  </div>\n\n                  <Table\n                    columns={volumeColumns}\n                    dataSource={volumes}\n                    rowKey=\"id\"\n                    loading={loading}\n                    pagination={{\n                      showSizeChanger: true,\n                      showQuickJumper: true,\n                      showTotal: (total) => `共 ${total} 个卷宗`\n                    }}\n                    expandable={{\n                      expandedRowRender: (volume) => {\n                        const volumeChapters = getVolumeChapters(volume.id);\n                        return (\n                          <div style={{ margin: 0 }}>\n                            <Divider orientation=\"left\">章节列表</Divider>\n                            <List\n                              size=\"small\"\n                              dataSource={volumeChapters}\n                              renderItem={(chapter) => (\n                                <List.Item\n                                  actions={[\n                                    <Button\n                                      type=\"link\"\n                                      size=\"small\"\n                                      onClick={() => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`)}\n                                    >\n                                      编辑\n                                    </Button>,\n                                    <Button\n                                      type=\"link\"\n                                      size=\"small\"\n                                      onClick={() => navigate(`/projects/${projectId}/volumes/${volume.id}/chapters/${chapter.id}`)}\n                                    >\n                                      查看\n                                    </Button>\n                                  ]}\n                                >\n                                  <List.Item.Meta\n                                    avatar={<FileTextOutlined />}\n                                    title={\n                                      <Space>\n                                        {chapter.title}\n                                        <Tag color={chapterStatusConfig[chapter.status].color}>\n                                          {chapterStatusConfig[chapter.status].text}\n                                        </Tag>\n                                      </Space>\n                                    }\n                                    description={`字数: ${chapter.wordCount} | 更新: ${chapter.updatedAt}`}\n                                  />\n                                </List.Item>\n                              )}\n                            />\n                            {volumeChapters.length === 0 && (\n                              <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>\n                                暂无章节，点击上方\"添加章节\"按钮创建\n                              </div>\n                            )}\n                          </div>\n                        );\n                      },\n                      rowExpandable: () => true,\n                    }}\n                  />\n                </div>\n              )\n            },\n            {\n              key: 'chapters',\n              label: (\n                <span>\n                  <FileTextOutlined />\n                  章节管理\n                </span>\n              ),\n              children: (\n                <div>\n                  <div className=\"toolbar\" style={{ marginBottom: 16 }}>\n                    <div className=\"toolbar-left\">\n                      <Button\n                        type=\"primary\"\n                        icon={<PlusOutlined />}\n                        onClick={handleCreateChapter}\n                      >\n                        新建章节\n                      </Button>\n                    </div>\n                  </div>\n\n                  <Table\n                    columns={chapterColumns}\n                    dataSource={chapters}\n                    rowKey=\"id\"\n                    loading={loading}\n                    pagination={{\n                      showSizeChanger: true,\n                      showQuickJumper: true,\n                      showTotal: (total) => `共 ${total} 个章节`\n                    }}\n                  />\n\n                  {getIndependentChapters().length > 0 && (\n                    <div style={{ marginTop: 24 }}>\n                      <Divider orientation=\"left\">独立章节</Divider>\n                      <List\n                        dataSource={getIndependentChapters()}\n                        renderItem={(chapter) => (\n                          <List.Item\n                            actions={[\n                              <Button\n                                type=\"link\"\n                                onClick={() => handleViewChapter(chapter)}\n                              >\n                                查看\n                              </Button>,\n                              <Button\n                                type=\"link\"\n                                onClick={() => handleEditChapter(chapter)}\n                              >\n                                编辑\n                              </Button>,\n                              <Button\n                                type=\"link\"\n                                onClick={() => handleAssignToVolume(chapter)}\n                              >\n                                分配到卷宗\n                              </Button>\n                            ]}\n                          >\n                            <List.Item.Meta\n                              avatar={<FileTextOutlined />}\n                              title={\n                                <Space>\n                                  {chapter.title}\n                                  <Tag color={chapterStatusConfig[chapter.status].color}>\n                                    {chapterStatusConfig[chapter.status].text}\n                                  </Tag>\n                                  <Tag color=\"orange\">独立章节</Tag>\n                                </Space>\n                              }\n                              description={`字数: ${chapter.wordCount} | 更新: ${chapter.updatedAt}`}\n                            />\n                          </List.Item>\n                        )}\n                      />\n                    </div>\n                  )}\n                </div>\n              )\n            }\n          ]}\n        />\n      </Card>\n\n      {/* 新建/编辑卷宗模态框 */}\n      <Modal\n        title={editingVolume ? '编辑卷宗' : '新建卷宗'}\n        open={volumeModalVisible}\n        onOk={handleVolumeModalOk}\n        onCancel={() => {\n          setVolumeModalVisible(false);\n          volumeForm.resetFields();\n        }}\n        confirmLoading={loading}\n        width={800}\n      >\n        <Form\n          form={volumeForm}\n          layout=\"vertical\"\n          initialValues={{ status: 'planning' }}\n        >\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"title\"\n                label=\"卷宗标题\"\n                rules={[{ required: true, message: '请输入卷宗标题' }]}\n              >\n                <Input placeholder=\"请输入卷宗标题\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"volumeNumber\"\n                label=\"卷序号\"\n                rules={[{ required: true, message: '请输入卷序号' }]}\n              >\n                <Input type=\"number\" placeholder=\"1\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"summary\"\n            label=\"卷宗摘要\"\n            rules={[{ required: true, message: '请输入卷宗摘要' }]}\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"请简要描述本卷的主要内容和剧情发展\"\n            />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择卷宗状态' }]}\n              >\n                <Select>\n                  {Object.entries(volumeStatusConfig).map(([key, config]) => (\n                    <Option key={key} value={key}>{config.text}</Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"targetWords\"\n                label=\"目标字数\"\n              >\n                <Input type=\"number\" placeholder=\"80000\" suffix=\"字\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"outline\"\n            label=\"卷宗大纲\"\n          >\n            <TextArea\n              rows={6}\n              placeholder=\"详细描述本卷的章节安排和剧情发展...\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 新建/编辑章节模态框 */}\n      <Modal\n        title={editingChapter ? '编辑章节' : '新建章节'}\n        open={chapterModalVisible}\n        onOk={handleChapterModalOk}\n        onCancel={() => {\n          setChapterModalVisible(false);\n          chapterForm.resetFields();\n        }}\n        confirmLoading={loading}\n        width={600}\n      >\n        <Form\n          form={chapterForm}\n          layout=\"vertical\"\n          initialValues={{ status: 'planning' }}\n        >\n          <Form.Item\n            name=\"volumeId\"\n            label=\"所属卷宗\"\n            help=\"留空则创建为独立章节\"\n          >\n            <Select placeholder=\"请选择卷宗（可留空创建独立章节）\" allowClear>\n              {volumes.map(volume => (\n                <Option key={volume.id} value={volume.id}>\n                  {volume.title}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={16}>\n              <Form.Item\n                name=\"title\"\n                label=\"章节标题\"\n                rules={[{ required: true, message: '请输入章节标题' }]}\n              >\n                <Input placeholder=\"请输入章节标题\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"chapterNumber\"\n                label=\"章节序号\"\n                help=\"独立章节可留空\"\n              >\n                <Input type=\"number\" placeholder=\"1\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"status\"\n            label=\"状态\"\n            rules={[{ required: true, message: '请选择章节状态' }]}\n          >\n            <Select>\n              {Object.entries(chapterStatusConfig).map(([key, config]) => (\n                <Option key={key} value={key}>{config.text}</Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"outline\"\n            label=\"章节大纲\"\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请简要描述本章节的主要内容和情节发展\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 分配章节到卷宗模态框 */}\n      <Modal\n        title=\"分配章节到卷宗\"\n        open={assignModalVisible}\n        onOk={handleAssignModalOk}\n        onCancel={() => {\n          setAssignModalVisible(false);\n          assignForm.resetFields();\n        }}\n        confirmLoading={loading}\n        width={400}\n      >\n        <Form\n          form={assignForm}\n          layout=\"vertical\"\n        >\n          <div style={{ marginBottom: 16 }}>\n            <Text strong>章节：</Text>\n            <Text>{assigningChapter?.title}</Text>\n          </div>\n\n          <Form.Item\n            name=\"volumeId\"\n            label=\"目标卷宗\"\n            help=\"选择要分配到的卷宗，留空则设为独立章节\"\n          >\n            <Select placeholder=\"请选择卷宗\" allowClear>\n              {volumes.map(volume => (\n                <Option key={volume.id} value={volume.id}>\n                  {volume.title}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default VolumeList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,QAAQ,EACRC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,IAAI,EACJC,OAAO,EACPC,IAAI,EACJC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,gBAAgB,EAChBC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,YAAY,EACZC,mBAAmB,EACnBC,mBAAmB,EACnBC,cAAc,EACdC,mBAAmB,EACnBC,gBAAgB,QACX,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpC,UAAU;AAClC,MAAM;EAAEqC;AAAS,CAAC,GAAG9B,KAAK;AAC1B,MAAM;EAAE+B;AAAO,CAAC,GAAG9B,MAAM;AACzB,MAAM;EAAE+B;AAAM,CAAC,GAAGvB,QAAQ;AAC1B,MAAM;EAAEwB;AAAQ,CAAC,GAAGrB,IAAI;AAExB,MAAMsB,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAGhD,SAAS,CAAC,CAAC;EACrC,MAAMiD,QAAQ,GAAGhD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACsD,QAAQ,EAAEC,WAAW,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACwD,OAAO,EAAEC,UAAU,CAAC,GAAGzD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC4D,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG7D,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC8D,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACkE,cAAc,EAAEC,iBAAiB,CAAC,GAAGnE,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACoE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACsE,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwE,SAAS,EAAEC,YAAY,CAAC,GAAGzE,QAAQ,CAAC,SAAS,CAAC;EACrD,MAAM,CAAC0E,UAAU,CAAC,GAAG9D,IAAI,CAAC+D,OAAO,CAAC,CAAC;EACnC,MAAM,CAACC,WAAW,CAAC,GAAGhE,IAAI,CAAC+D,OAAO,CAAC,CAAC;EACpC,MAAM,CAACE,UAAU,CAAC,GAAGjE,IAAI,CAAC+D,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAMG,kBAAkB,GAAG;IACzBC,QAAQ,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACxCC,OAAO,EAAE;MAAEF,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAM,CAAC;IACzCE,SAAS,EAAE;MAAEH,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC1CG,SAAS,EAAE;MAAEJ,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC3CI,OAAO,EAAE;MAAEL,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACvCK,SAAS,EAAE;MAAEN,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAM;EAC7C,CAAC;EAED,MAAMM,mBAAmB,GAAG;IAC1BR,QAAQ,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAM,CAAC;IACxCO,KAAK,EAAE;MAAER,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK,CAAC;IACtCC,OAAO,EAAE;MAAEF,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC7CE,SAAS,EAAE;MAAEH,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAM,CAAC;IAC1CK,SAAS,EAAE;MAAEN,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAM;EAC7C,CAAC;;EAED;EACA,MAAMQ,WAAW,GAAG,CAClB;IACExC,EAAE,EAAE,CAAC;IACLyC,KAAK,EAAE,WAAW;IAClBC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,wBAAwB;IACjCC,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE,CAAC;IACpBC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEnD,EAAE,EAAE,CAAC;IACLyC,KAAK,EAAE,UAAU;IACjBC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,UAAU;IAClBC,OAAO,EAAE,sBAAsB;IAC/BC,aAAa,EAAE,CAAC;IAChBC,iBAAiB,EAAE,CAAC;IACpBC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,KAAK;IAClBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;;EAED;EACA,MAAMC,YAAY,GAAG,CACnB;IACEpD,EAAE,EAAE,CAAC;IACLqD,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,QAAQ;IACfa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,IAAI;IACfb,MAAM,EAAE,WAAW;IACnBc,OAAO,EAAE,oBAAoB;IAC7BP,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEnD,EAAE,EAAE,CAAC;IACLqD,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,QAAQ;IACfa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,0BAA0B;IACnCC,SAAS,EAAE,IAAI;IACfb,MAAM,EAAE,WAAW;IACnBc,OAAO,EAAE,iBAAiB;IAC1BP,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEnD,EAAE,EAAE,CAAC;IACLqD,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,QAAQ;IACfa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,CAAC;IACZb,MAAM,EAAE,UAAU;IAClBc,OAAO,EAAE,iBAAiB;IAC1BP,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEnD,EAAE,EAAE,CAAC;IACLqD,QAAQ,EAAE,CAAC;IACXZ,KAAK,EAAE,UAAU;IACjBa,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,CAAC;IACZb,MAAM,EAAE,UAAU;IAClBc,OAAO,EAAE,iBAAiB;IAC1BP,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,EACD;IACEnD,EAAE,EAAE,CAAC;IACLqD,QAAQ,EAAE,IAAI;IAAE;IAChBZ,KAAK,EAAE,UAAU;IACjBa,aAAa,EAAE,IAAI;IACnBC,OAAO,EAAE,sBAAsB;IAC/BC,SAAS,EAAE,IAAI;IACfb,MAAM,EAAE,OAAO;IACfc,OAAO,EAAE,WAAW;IACpBP,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC,CACF;EAEDnG,SAAS,CAAC,MAAM;IACdoD,UAAU,CAACoC,WAAW,CAAC;IACvBlC,WAAW,CAAC8C,YAAY,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMM,YAAY,GAAGvD,OAAO,CAACwD,MAAM;EACnC,MAAMd,aAAa,GAAGxC,QAAQ,CAACsD,MAAM;EACrC,MAAMb,iBAAiB,GAAGzC,QAAQ,CAACuD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,MAAM,KAAK,WAAW,IAAIkB,CAAC,CAAClB,MAAM,KAAK,WAAW,CAAC,CAACgB,MAAM;EAC3G,MAAMZ,UAAU,GAAG1C,QAAQ,CAACyD,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,IAAIC,OAAO,CAACR,SAAS,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EACvF,MAAMS,mBAAmB,GAAG5D,QAAQ,CAACuD,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACR,QAAQ,CAAC,CAACM,MAAM;;EAEpE;EACA,MAAMO,aAAa,GAAG,CACpB;IACEzB,KAAK,EAAE,MAAM;IACb0B,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACrC,IAAI,EAAEsC,MAAM,kBACnB/E,OAAA,CAAC/B,KAAK;MAAA+G,QAAA,gBACJhF,OAAA,CAACJ,cAAc;QAAAqF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClBpF,OAAA,CAACE,IAAI;QAACmF,MAAM;QAAAL,QAAA,EAAEvC;MAAI;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1BpF,OAAA,CAAC9B,GAAG;QAACsE,KAAK,EAAC,MAAM;QAAAwC,QAAA,GAAC,QAAC,EAACD,MAAM,CAAC5B,YAAY,EAAC,QAAC;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C;EAEX,CAAC,EACD;IACElC,KAAK,EAAE,IAAI;IACX0B,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAEA,CAACpB,QAAQ,EAAEqB,MAAM,kBACvB/E,OAAA;MAAAgF,QAAA,gBACEhF,OAAA,CAACvB,QAAQ;QAAC6G,OAAO,EAAE5B,QAAS;QAAC6B,IAAI,EAAC;MAAO;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5CpF,OAAA,CAACE,IAAI;QAACsF,IAAI,EAAC,WAAW;QAAAR,QAAA,GACnBD,MAAM,CAACxB,iBAAiB,EAAC,GAAC,EAACwB,MAAM,CAACzB,aAAa,EAAC,eACnD;MAAA;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACN;IACDK,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChC,QAAQ,GAAGiC,CAAC,CAACjC;EACnC,CAAC,EACD;IACER,KAAK,EAAE,IAAI;IACX0B,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,MAAM,EAAEA,CAACc,KAAK,EAAEb,MAAM,kBACpB/E,OAAA;MAAAgF,QAAA,gBACEhF,OAAA,CAACE,IAAI;QAAA8E,QAAA,GAAEY,KAAK,CAACC,cAAc,CAAC,CAAC,EAAC,SAAE;MAAA;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EACtCL,MAAM,CAACtB,WAAW,iBACjBzD,OAAA;QAAAgF,QAAA,eACEhF,OAAA,CAACE,IAAI;UAACsF,IAAI,EAAC,WAAW;UAAAR,QAAA,GAAC,gBAAI,EAACD,MAAM,CAACtB,WAAW,CAACoC,cAAc,CAAC,CAAC;QAAA;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CACN;IACDK,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAClC,UAAU,GAAGmC,CAAC,CAACnC;EACrC,CAAC,EACD;IACEN,KAAK,EAAE,IAAI;IACX0B,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAG1B,MAAM,iBACbpD,OAAA,CAAC9B,GAAG;MAACsE,KAAK,EAAEF,kBAAkB,CAACc,MAAM,CAAC,CAACZ,KAAM;MAAAwC,QAAA,EAC1C1C,kBAAkB,CAACc,MAAM,CAAC,CAACX;IAAI;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CACN;IACDU,OAAO,EAAEC,MAAM,CAACC,OAAO,CAAC1D,kBAAkB,CAAC,CAAC2D,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEqB,MAAM,CAAC,MAAM;MAClEzD,IAAI,EAAEyD,MAAM,CAACzD,IAAI;MACjB0D,KAAK,EAAEtB;IACT,CAAC,CAAC,CAAC;IACHuB,QAAQ,EAAEA,CAACD,KAAK,EAAEpB,MAAM,KAAKA,MAAM,CAAC3B,MAAM,KAAK+C;EACjD,CAAC,EACD;IACEjD,KAAK,EAAE,MAAM;IACb0B,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBY,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIU,IAAI,CAACX,CAAC,CAAC9B,SAAS,CAAC,GAAG,IAAIyC,IAAI,CAACV,CAAC,CAAC/B,SAAS;EAChE,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACX2B,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACwB,CAAC,EAAEvB,MAAM,kBAChB/E,OAAA,CAAC/B,KAAK;MAAA+G,QAAA,gBACJhF,OAAA,CAACtB,OAAO;QAACwE,KAAK,EAAC,0BAAM;QAAA8B,QAAA,eACnBhF,OAAA,CAACjC,MAAM;UACLyH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAEvG,OAAA,CAACT,WAAW;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBoB,OAAO,EAAEA,CAAA,KAAMC,wBAAwB,CAAC1B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVpF,OAAA,CAACtB,OAAO;QAACwE,KAAK,EAAC,0BAAM;QAAA8B,QAAA,eACnBhF,OAAA,CAACjC,MAAM;UACLyH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAEvG,OAAA,CAACX,YAAY;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBoB,OAAO,EAAEA,CAAA,KAAME,gBAAgB,CAAC3B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVpF,OAAA,CAACtB,OAAO;QAACwE,KAAK,EAAC,0BAAM;QAAA8B,QAAA,eACnBhF,OAAA,CAACjC,MAAM;UACLyH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAEvG,OAAA,CAACb,YAAY;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBoB,OAAO,EAAEA,CAAA,KAAMG,gBAAgB,CAAC5B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVpF,OAAA,CAACtB,OAAO;QAACwE,KAAK,EAAC,4BAAQ;QAAA8B,QAAA,eACrBhF,OAAA,CAACjC,MAAM;UACLyH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAEvG,OAAA,CAACR,aAAa;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBoB,OAAO,EAAEA,CAAA,KAAMI,uBAAuB,CAAC7B,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVpF,OAAA,CAACxB,UAAU;QACT0E,KAAK,EAAC,4IAAyB;QAC/B2D,SAAS,EAAEA,CAAA,KAAMC,kBAAkB,CAAC/B,MAAM,CAACtE,EAAE,CAAE;QAC/CsG,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAhC,QAAA,eAEfhF,OAAA,CAACtB,OAAO;UAACwE,KAAK,EAAC,cAAI;UAAA8B,QAAA,eACjBhF,OAAA,CAACjC,MAAM;YACLyH,IAAI,EAAC,MAAM;YACXyB,MAAM;YACNV,IAAI,eAAEvG,OAAA,CAACV,cAAc;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAM8B,cAAc,GAAG,CACrB;IACEhE,KAAK,EAAE,MAAM;IACb0B,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAEA,CAACrC,IAAI,EAAEsC,MAAM,kBACnB/E,OAAA,CAAC/B,KAAK;MAAA+G,QAAA,gBACJhF,OAAA,CAACZ,gBAAgB;QAAA6F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpBpF,OAAA,CAACE,IAAI;QAACmF,MAAM;QAAAL,QAAA,EAAEvC;MAAI;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACzBL,MAAM,CAAChB,aAAa,iBACnB/D,OAAA,CAAC9B,GAAG;QAACsE,KAAK,EAAC,MAAM;QAAAwC,QAAA,GAAC,QAAC,EAACD,MAAM,CAAChB,aAAa,EAAC,QAAC;MAAA;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAChD;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAEX,CAAC,EACD;IACElC,KAAK,EAAE,MAAM;IACb0B,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGhB,QAAQ,IAAK;MACpB,IAAI,CAACA,QAAQ,EAAE;QACb,oBAAO9D,OAAA,CAAC9B,GAAG;UAACsE,KAAK,EAAC,QAAQ;UAAAwC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACvC;MACA,MAAM+B,MAAM,GAAGvG,OAAO,CAACwG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5G,EAAE,KAAKqD,QAAQ,CAAC;MACnD,OAAOqD,MAAM,gBACXnH,OAAA,CAAC9B,GAAG;QAACsE,KAAK,EAAC,MAAM;QAAAwC,QAAA,EAAEmC,MAAM,CAACjE;MAAK;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,gBAEtCpF,OAAA,CAAC9B,GAAG;QAACsE,KAAK,EAAC,KAAK;QAAAwC,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAC3B;IACH,CAAC;IACDU,OAAO,EAAE,CACP;MAAErD,IAAI,EAAE,MAAM;MAAE0D,KAAK,EAAE;IAAK,CAAC,EAC7B,GAAGvF,OAAO,CAACqF,GAAG,CAACoB,CAAC,KAAK;MAAE5E,IAAI,EAAE4E,CAAC,CAACnE,KAAK;MAAEiD,KAAK,EAAEkB,CAAC,CAAC5G;IAAG,CAAC,CAAC,CAAC,CACtD;IACD2F,QAAQ,EAAEA,CAACD,KAAK,EAAEpB,MAAM,KAAKA,MAAM,CAACjB,QAAQ,KAAKqC;EACnD,CAAC,EACD;IACEjD,KAAK,EAAE,IAAI;IACX0B,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGwC,KAAK,iBACZtH,OAAA,CAACE,IAAI;MAACsF,IAAI,EAAE8B,KAAK,KAAK,CAAC,GAAG,WAAW,GAAG,SAAU;MAAAtC,QAAA,GAC/CsC,KAAK,CAACzB,cAAc,CAAC,CAAC,EAAC,SAC1B;IAAA;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACP;IACDK,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzB,SAAS,GAAG0B,CAAC,CAAC1B;EACpC,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACX0B,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAG1B,MAAM,iBACbpD,OAAA,CAAC9B,GAAG;MAACsE,KAAK,EAAEO,mBAAmB,CAACK,MAAM,CAAC,CAACZ,KAAM;MAAAwC,QAAA,EAC3CjC,mBAAmB,CAACK,MAAM,CAAC,CAACX;IAAI;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CACN;IACDU,OAAO,EAAEC,MAAM,CAACC,OAAO,CAACjD,mBAAmB,CAAC,CAACkD,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEqB,MAAM,CAAC,MAAM;MACnEzD,IAAI,EAAEyD,MAAM,CAACzD,IAAI;MACjB0D,KAAK,EAAEtB;IACT,CAAC,CAAC,CAAC;IACHuB,QAAQ,EAAEA,CAACD,KAAK,EAAEpB,MAAM,KAAKA,MAAM,CAAC3B,MAAM,KAAK+C;EACjD,CAAC,EACD;IACEjD,KAAK,EAAE,MAAM;IACb0B,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBY,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAK,IAAIU,IAAI,CAACX,CAAC,CAAC9B,SAAS,CAAC,GAAG,IAAIyC,IAAI,CAACV,CAAC,CAAC/B,SAAS;EAChE,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACX2B,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACwB,CAAC,EAAEvB,MAAM,kBAChB/E,OAAA,CAAC/B,KAAK;MAAA+G,QAAA,gBACJhF,OAAA,CAACtB,OAAO;QAACwE,KAAK,EAAC,cAAI;QAAA8B,QAAA,eACjBhF,OAAA,CAACjC,MAAM;UACLyH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAEvG,OAAA,CAACT,WAAW;YAAA0F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBoB,OAAO,EAAEA,CAAA,KAAMe,iBAAiB,CAACxC,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVpF,OAAA,CAACtB,OAAO;QAACwE,KAAK,EAAC,cAAI;QAAA8B,QAAA,eACjBhF,OAAA,CAACjC,MAAM;UACLyH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAEvG,OAAA,CAACX,YAAY;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBoB,OAAO,EAAEA,CAAA,KAAMgB,iBAAiB,CAACzC,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVpF,OAAA,CAACtB,OAAO;QAACwE,KAAK,EAAC,gBAAM;QAAA8B,QAAA,eACnBhF,OAAA,CAACjC,MAAM;UACLyH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAEvG,OAAA,CAACR,aAAa;YAAAyF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxBoB,OAAO,EAAEA,CAAA,KAAMiB,uBAAuB,CAAC1C,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVpF,OAAA,CAACtB,OAAO;QAACwE,KAAK,EAAC,gCAAO;QAAA8B,QAAA,eACpBhF,OAAA,CAACjC,MAAM;UACLyH,IAAI,EAAC,MAAM;UACXe,IAAI,eAAEvG,OAAA,CAACJ,cAAc;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBoB,OAAO,EAAEA,CAAA,KAAMkB,oBAAoB,CAAC3C,MAAM;QAAE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVpF,OAAA,CAACxB,UAAU;QACT0E,KAAK,EAAC,8DAAY;QAClB2D,SAAS,EAAEA,CAAA,KAAMc,mBAAmB,CAAC5C,MAAM,CAACtE,EAAE,CAAE;QAChDsG,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAhC,QAAA,eAEfhF,OAAA,CAACtB,OAAO;UAACwE,KAAK,EAAC,cAAI;UAAA8B,QAAA,eACjBhF,OAAA,CAACjC,MAAM;YACLyH,IAAI,EAAC,MAAM;YACXyB,MAAM;YACNV,IAAI,eAAEvG,OAAA,CAACV,cAAc;cAAA2F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMwC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnG,gBAAgB,CAAC,IAAI,CAAC;IACtBS,UAAU,CAAC2F,WAAW,CAAC,CAAC;IACxB1G,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMuF,gBAAgB,GAAIS,MAAM,IAAK;IACnC1F,gBAAgB,CAAC0F,MAAM,CAAC;IACxBjF,UAAU,CAAC4F,cAAc,CAACX,MAAM,CAAC;IACjChG,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMsF,wBAAwB,GAAIU,MAAM,IAAK;IAC3CpF,mBAAmB,CAACoF,MAAM,CAAC1G,EAAE,CAAC;IAC9BlC,OAAO,CAACwJ,IAAI,CAAC,QAAQZ,MAAM,CAACjE,KAAK,MAAM,CAAC;EAC1C,CAAC;EAED,MAAMyD,gBAAgB,GAAIQ,MAAM,IAAK;IACnCpF,mBAAmB,CAACoF,MAAM,CAAC1G,EAAE,CAAC;IAC9BkB,iBAAiB,CAAC,IAAI,CAAC;IACvBS,WAAW,CAACyF,WAAW,CAAC,CAAC;IACzBzF,WAAW,CAAC0F,cAAc,CAAC;MAAEhE,QAAQ,EAAEqD,MAAM,CAAC1G;IAAG,CAAC,CAAC;IACnDY,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMuF,uBAAuB,GAAIO,MAAM,IAAK;IAC1C5I,OAAO,CAACwJ,IAAI,CAAC,UAAUZ,MAAM,CAACjE,KAAK,OAAO,CAAC;EAC7C,CAAC;EAED,MAAM4D,kBAAkB,GAAG,MAAOrG,EAAE,IAAK;IACvC,IAAI;MACF;MACA,MAAM7C,KAAK,CAACoK,MAAM,CAAC,8BAA8BtH,SAAS,gBAAgBD,EAAE,EAAE,CAAC;;MAE/E;MACAI,UAAU,CAACD,OAAO,CAACyD,MAAM,CAACgD,CAAC,IAAIA,CAAC,CAAC5G,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC5CM,WAAW,CAACD,QAAQ,CAACuD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAKrD,EAAE,CAAC,CAAC;MACpDlC,OAAO,CAAC0J,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3J,OAAO,CAAC2J,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAME,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMnG,UAAU,CAACoG,cAAc,CAAC,CAAC;MAChDrH,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIO,aAAa,EAAE;QACjB;QACAX,UAAU,CAACD,OAAO,CAACqF,GAAG,CAACoB,CAAC,IACtBA,CAAC,CAAC5G,EAAE,KAAKe,aAAa,CAACf,EAAE,GACrB;UAAE,GAAG4G,CAAC;UAAE,GAAGgB,MAAM;UAAEzE,SAAS,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAAE,CAAC,GACtEnB,CACN,CAAC,CAAC;QACF9I,OAAO,CAAC0J,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMQ,SAAS,GAAG;UAChBhI,EAAE,EAAE4F,IAAI,CAACqC,GAAG,CAAC,CAAC;UACd,GAAGL,MAAM;UACT/E,aAAa,EAAE,CAAC;UAChBC,iBAAiB,EAAE,CAAC;UACpBC,UAAU,EAAE,CAAC;UACbE,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,IAAI0C,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjD5E,SAAS,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACD3H,UAAU,CAAC,CAAC,GAAGD,OAAO,EAAE6H,SAAS,CAAC,CAAC;QACnClK,OAAO,CAAC0J,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEA9G,qBAAqB,CAAC,KAAK,CAAC;MAC5Be,UAAU,CAAC2F,WAAW,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRjH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0H,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACF,MAAMN,MAAM,GAAG,MAAMjG,WAAW,CAACkG,cAAc,CAAC,CAAC;MACjDrH,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIS,cAAc,EAAE;QAClB;QACAX,WAAW,CAACD,QAAQ,CAACmF,GAAG,CAAC3B,CAAC,IACxBA,CAAC,CAAC7D,EAAE,KAAKiB,cAAc,CAACjB,EAAE,GACtB;UAAE,GAAG6D,CAAC;UAAE,GAAG+D,MAAM;UAAEzE,SAAS,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAAE,CAAC,GACtElE,CACN,CAAC,CAAC;QACF/F,OAAO,CAAC0J,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMW,UAAU,GAAG;UACjBnI,EAAE,EAAE4F,IAAI,CAACqC,GAAG,CAAC,CAAC;UACd,GAAGL,MAAM;UACTpE,SAAS,EAAE,CAAC;UACZN,SAAS,EAAE,IAAI0C,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACjD5E,SAAS,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACDzH,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAE8H,UAAU,CAAC,CAAC;QACtCrK,OAAO,CAAC0J,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEA5G,sBAAsB,CAAC,KAAK,CAAC;MAC7Be,WAAW,CAACyF,WAAW,CAAC,CAAC;IAC3B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRjH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4H,mBAAmB,GAAGA,CAAA,KAAM;IAChClH,iBAAiB,CAAC,IAAI,CAAC;IACvBS,WAAW,CAACyF,WAAW,CAAC,CAAC;IACzBzF,WAAW,CAAC0F,cAAc,CAAC;MAAEhE,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,CAAC;IAChDzC,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMkG,iBAAiB,GAAI9C,OAAO,IAAK;IACrC,MAAMX,QAAQ,GAAGW,OAAO,CAACX,QAAQ,IAAI,aAAa;IAClDnD,QAAQ,CAAC,aAAaD,SAAS,YAAYoD,QAAQ,aAAaW,OAAO,CAAChE,EAAE,EAAE,CAAC;EAC/E,CAAC;EAED,MAAM+G,iBAAiB,GAAI/C,OAAO,IAAK;IACrC9C,iBAAiB,CAAC8C,OAAO,CAAC;IAC1BrC,WAAW,CAAC0F,cAAc,CAACrD,OAAO,CAAC;IACnCpD,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAMoG,uBAAuB,GAAIhD,OAAO,IAAK;IAC3ClG,OAAO,CAACwJ,IAAI,CAAC,UAAUtD,OAAO,CAACvB,KAAK,EAAE,CAAC;EACzC,CAAC;EAED,MAAMwE,oBAAoB,GAAIjD,OAAO,IAAK;IACxC5C,mBAAmB,CAAC4C,OAAO,CAAC;IAC5BpC,UAAU,CAACyF,cAAc,CAAC;MAAEhE,QAAQ,EAAEW,OAAO,CAACX;IAAS,CAAC,CAAC;IACzDvC,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMuH,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMT,MAAM,GAAG,MAAMhG,UAAU,CAACiG,cAAc,CAAC,CAAC;MAChDrH,UAAU,CAAC,IAAI,CAAC;MAEhBF,WAAW,CAACD,QAAQ,CAACmF,GAAG,CAAC3B,CAAC,IACxBA,CAAC,CAAC7D,EAAE,KAAKmB,gBAAgB,CAACnB,EAAE,GACxB;QAAE,GAAG6D,CAAC;QAAER,QAAQ,EAAEuE,MAAM,CAACvE,QAAQ;QAAEF,SAAS,EAAE,IAAIyC,IAAI,CAAC,CAAC,CAACkC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAAE,CAAC,GACtFlE,CACN,CAAC,CAAC;MAEF/F,OAAO,CAAC0J,OAAO,CAAC,QAAQ,CAAC;MACzB1G,qBAAqB,CAAC,KAAK,CAAC;MAC5Bc,UAAU,CAACwF,WAAW,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;IAC/B,CAAC,SAAS;MACRjH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0G,mBAAmB,GAAG,MAAOlH,EAAE,IAAK;IACxC,IAAI;MACF;MACA,MAAM7C,KAAK,CAACoK,MAAM,CAAC,8BAA8BtH,SAAS,iBAAiBD,EAAE,EAAE,CAAC;;MAEhF;MACAM,WAAW,CAACD,QAAQ,CAACuD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7D,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC9ClC,OAAO,CAAC0J,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B3J,OAAO,CAAC2J,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;;EAED;EACA,MAAMa,iBAAiB,GAAIjF,QAAQ,IAAK;IACtC,OAAOhD,QAAQ,CAACuD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAKA,QAAQ,CAAC;EACtD,CAAC;;EAED;EACA,MAAMkF,sBAAsB,GAAGA,CAAA,KAAM;IACnC,OAAOlI,QAAQ,CAACuD,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACR,QAAQ,CAAC;EAC1C,CAAC;EAED,oBACE9D,OAAA;IAAKiJ,SAAS,EAAC,SAAS;IAAAjE,QAAA,gBACtBhF,OAAA;MAAKiJ,SAAS,EAAC,aAAa;MAAAjE,QAAA,eAC1BhF,OAAA,CAACC,KAAK;QAACiJ,KAAK,EAAE,CAAE;QAACD,SAAS,EAAC,YAAY;QAAAjE,QAAA,EAAC;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGNpF,OAAA,CAACrB,GAAG;MAACwK,MAAM,EAAE,EAAG;MAACC,KAAK,EAAE;QAAEC,YAAY,EAAE;MAAG,CAAE;MAAArE,QAAA,gBAC3ChF,OAAA,CAACpB,GAAG;QAAC0K,IAAI,EAAE,CAAE;QAAAtE,QAAA,eACXhF,OAAA,CAACnC,IAAI;UAAAmH,QAAA,eACHhF,OAAA,CAACnB,SAAS;YACRqE,KAAK,EAAC,0BAAM;YACZiD,KAAK,EAAEhC,YAAa;YACpBoF,MAAM,eAAEvJ,OAAA,CAACJ,cAAc;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpF,OAAA,CAACpB,GAAG;QAAC0K,IAAI,EAAE,CAAE;QAAAtE,QAAA,eACXhF,OAAA,CAACnC,IAAI;UAAAmH,QAAA,eACHhF,OAAA,CAACnB,SAAS;YACRqE,KAAK,EAAC,0BAAM;YACZiD,KAAK,EAAE7C,aAAc;YACrBiG,MAAM,eAAEvJ,OAAA,CAACP,YAAY;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpF,OAAA,CAACpB,GAAG;QAAC0K,IAAI,EAAE,CAAE;QAAAtE,QAAA,eACXhF,OAAA,CAACnC,IAAI;UAAAmH,QAAA,eACHhF,OAAA,CAACnB,SAAS;YACRqE,KAAK,EAAC,0BAAM;YACZiD,KAAK,EAAEzB,mBAAoB;YAC3B6E,MAAM,eAAEvJ,OAAA,CAACZ,gBAAgB;cAACgK,KAAK,EAAE;gBAAE5G,KAAK,EAAE;cAAU;YAAE;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpF,OAAA,CAACpB,GAAG;QAAC0K,IAAI,EAAE,CAAE;QAAAtE,QAAA,eACXhF,OAAA,CAACnC,IAAI;UAAAmH,QAAA,eACHhF,OAAA,CAACnB,SAAS;YACRqE,KAAK,EAAC,oBAAK;YACXiD,KAAK,EAAE3C,UAAW;YAClBgG,MAAM,EAAC,QAAG;YACVD,MAAM,eAAEvJ,OAAA,CAACL,mBAAmB;cAACyJ,KAAK,EAAE;gBAAE5G,KAAK,EAAE;cAAU;YAAE;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENpF,OAAA,CAACnC,IAAI;MAAAmH,QAAA,eACHhF,OAAA,CAACf,IAAI;QACHwK,SAAS,EAAEzH,SAAU;QACrB0H,QAAQ,EAAEzH,YAAa;QACvB0H,KAAK,EAAE,CACL;UACE9E,GAAG,EAAE,SAAS;UACd+E,KAAK,eACH5J,OAAA;YAAAgF,QAAA,gBACEhF,OAAA,CAACJ,cAAc;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;UACDJ,QAAQ,eACNhF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAKiJ,SAAS,EAAC,SAAS;cAACG,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAArE,QAAA,eACnDhF,OAAA;gBAAKiJ,SAAS,EAAC,cAAc;gBAAAjE,QAAA,eAC3BhF,OAAA,CAACjC,MAAM;kBACLyH,IAAI,EAAC,SAAS;kBACde,IAAI,eAAEvG,OAAA,CAACb,YAAY;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBoB,OAAO,EAAEoB,kBAAmB;kBAAA5C,QAAA,EAC7B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpF,OAAA,CAAChC,KAAK;cACJ6L,OAAO,EAAElF,aAAc;cACvBmF,UAAU,EAAElJ,OAAQ;cACpBmJ,MAAM,EAAC,IAAI;cACX/I,OAAO,EAAEA,OAAQ;cACjBgJ,UAAU,EAAE;gBACVC,eAAe,EAAE,IAAI;gBACrBC,eAAe,EAAE,IAAI;gBACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;cAClC,CAAE;cACFC,UAAU,EAAE;gBACVC,iBAAiB,EAAGnD,MAAM,IAAK;kBAC7B,MAAMoD,cAAc,GAAGxB,iBAAiB,CAAC5B,MAAM,CAAC1G,EAAE,CAAC;kBACnD,oBACET,OAAA;oBAAKoJ,KAAK,EAAE;sBAAEoB,MAAM,EAAE;oBAAE,CAAE;oBAAAxF,QAAA,gBACxBhF,OAAA,CAAChB,OAAO;sBAACyL,WAAW,EAAC,MAAM;sBAAAzF,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAS,CAAC,eAC1CpF,OAAA,CAACjB,IAAI;sBACHwG,IAAI,EAAC,OAAO;sBACZuE,UAAU,EAAES,cAAe;sBAC3BG,UAAU,EAAGjG,OAAO,iBAClBzE,OAAA,CAACjB,IAAI,CAAC4L,IAAI;wBACRC,OAAO,EAAE,cACP5K,OAAA,CAACjC,MAAM;0BACLyH,IAAI,EAAC,MAAM;0BACXD,IAAI,EAAC,OAAO;0BACZiB,OAAO,EAAEA,CAAA,KAAM7F,QAAQ,CAAC,aAAaD,SAAS,YAAYyG,MAAM,CAAC1G,EAAE,aAAagE,OAAO,CAAChE,EAAE,EAAE,CAAE;0BAAAuE,QAAA,EAC/F;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,eACTpF,OAAA,CAACjC,MAAM;0BACLyH,IAAI,EAAC,MAAM;0BACXD,IAAI,EAAC,OAAO;0BACZiB,OAAO,EAAEA,CAAA,KAAM7F,QAAQ,CAAC,aAAaD,SAAS,YAAYyG,MAAM,CAAC1G,EAAE,aAAagE,OAAO,CAAChE,EAAE,EAAE,CAAE;0BAAAuE,QAAA,EAC/F;wBAED;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAQ,CAAC,CACT;wBAAAJ,QAAA,eAEFhF,OAAA,CAACjB,IAAI,CAAC4L,IAAI,CAACE,IAAI;0BACbC,MAAM,eAAE9K,OAAA,CAACZ,gBAAgB;4BAAA6F,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAE;0BAC7BlC,KAAK,eACHlD,OAAA,CAAC/B,KAAK;4BAAA+G,QAAA,GACHP,OAAO,CAACvB,KAAK,eACdlD,OAAA,CAAC9B,GAAG;8BAACsE,KAAK,EAAEO,mBAAmB,CAAC0B,OAAO,CAACrB,MAAM,CAAC,CAACZ,KAAM;8BAAAwC,QAAA,EACnDjC,mBAAmB,CAAC0B,OAAO,CAACrB,MAAM,CAAC,CAACX;4BAAI;8BAAAwC,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtC,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACD,CACR;0BACD2F,WAAW,EAAE,OAAOtG,OAAO,CAACR,SAAS,UAAUQ,OAAO,CAACb,SAAS;wBAAG;0BAAAqB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACpE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACO;oBACX;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EACDmF,cAAc,CAACnG,MAAM,KAAK,CAAC,iBAC1BpE,OAAA;sBAAKoJ,KAAK,EAAE;wBAAE4B,SAAS,EAAE,QAAQ;wBAAEC,OAAO,EAAE,MAAM;wBAAEzI,KAAK,EAAE;sBAAO,CAAE;sBAAAwC,QAAA,EAAC;oBAErE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAEV,CAAC;gBACD8F,aAAa,EAAEA,CAAA,KAAM;cACvB;YAAE;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAET,CAAC,EACD;UACEP,GAAG,EAAE,UAAU;UACf+E,KAAK,eACH5J,OAAA;YAAAgF,QAAA,gBACEhF,OAAA,CAACZ,gBAAgB;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,4BAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;UACDJ,QAAQ,eACNhF,OAAA;YAAAgF,QAAA,gBACEhF,OAAA;cAAKiJ,SAAS,EAAC,SAAS;cAACG,KAAK,EAAE;gBAAEC,YAAY,EAAE;cAAG,CAAE;cAAArE,QAAA,eACnDhF,OAAA;gBAAKiJ,SAAS,EAAC,cAAc;gBAAAjE,QAAA,eAC3BhF,OAAA,CAACjC,MAAM;kBACLyH,IAAI,EAAC,SAAS;kBACde,IAAI,eAAEvG,OAAA,CAACb,YAAY;oBAAA8F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBoB,OAAO,EAAEqC,mBAAoB;kBAAA7D,QAAA,EAC9B;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpF,OAAA,CAAChC,KAAK;cACJ6L,OAAO,EAAE3C,cAAe;cACxB4C,UAAU,EAAEhJ,QAAS;cACrBiJ,MAAM,EAAC,IAAI;cACX/I,OAAO,EAAEA,OAAQ;cACjBgJ,UAAU,EAAE;gBACVC,eAAe,EAAE,IAAI;gBACrBC,eAAe,EAAE,IAAI;gBACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;cAClC;YAAE;cAAAnF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAED4D,sBAAsB,CAAC,CAAC,CAAC5E,MAAM,GAAG,CAAC,iBAClCpE,OAAA;cAAKoJ,KAAK,EAAE;gBAAE+B,SAAS,EAAE;cAAG,CAAE;cAAAnG,QAAA,gBAC5BhF,OAAA,CAAChB,OAAO;gBAACyL,WAAW,EAAC,MAAM;gBAAAzF,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC1CpF,OAAA,CAACjB,IAAI;gBACH+K,UAAU,EAAEd,sBAAsB,CAAC,CAAE;gBACrC0B,UAAU,EAAGjG,OAAO,iBAClBzE,OAAA,CAACjB,IAAI,CAAC4L,IAAI;kBACRC,OAAO,EAAE,cACP5K,OAAA,CAACjC,MAAM;oBACLyH,IAAI,EAAC,MAAM;oBACXgB,OAAO,EAAEA,CAAA,KAAMe,iBAAiB,CAAC9C,OAAO,CAAE;oBAAAO,QAAA,EAC3C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTpF,OAAA,CAACjC,MAAM;oBACLyH,IAAI,EAAC,MAAM;oBACXgB,OAAO,EAAEA,CAAA,KAAMgB,iBAAiB,CAAC/C,OAAO,CAAE;oBAAAO,QAAA,EAC3C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTpF,OAAA,CAACjC,MAAM;oBACLyH,IAAI,EAAC,MAAM;oBACXgB,OAAO,EAAEA,CAAA,KAAMkB,oBAAoB,CAACjD,OAAO,CAAE;oBAAAO,QAAA,EAC9C;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,CACT;kBAAAJ,QAAA,eAEFhF,OAAA,CAACjB,IAAI,CAAC4L,IAAI,CAACE,IAAI;oBACbC,MAAM,eAAE9K,OAAA,CAACZ,gBAAgB;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAE;oBAC7BlC,KAAK,eACHlD,OAAA,CAAC/B,KAAK;sBAAA+G,QAAA,GACHP,OAAO,CAACvB,KAAK,eACdlD,OAAA,CAAC9B,GAAG;wBAACsE,KAAK,EAAEO,mBAAmB,CAAC0B,OAAO,CAACrB,MAAM,CAAC,CAACZ,KAAM;wBAAAwC,QAAA,EACnDjC,mBAAmB,CAAC0B,OAAO,CAACrB,MAAM,CAAC,CAACX;sBAAI;wBAAAwC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC,eACNpF,OAAA,CAAC9B,GAAG;wBAACsE,KAAK,EAAC,QAAQ;wBAAAwC,QAAA,EAAC;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CACR;oBACD2F,WAAW,EAAE,OAAOtG,OAAO,CAACR,SAAS,UAAUQ,OAAO,CAACb,SAAS;kBAAG;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO;cACX;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAET,CAAC;MACD;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPpF,OAAA,CAAC7B,KAAK;MACJ+E,KAAK,EAAE1B,aAAa,GAAG,MAAM,GAAG,MAAO;MACvC4J,IAAI,EAAElK,kBAAmB;MACzBmK,IAAI,EAAEjD,mBAAoB;MAC1BkD,QAAQ,EAAEA,CAAA,KAAM;QACdnK,qBAAqB,CAAC,KAAK,CAAC;QAC5Be,UAAU,CAAC2F,WAAW,CAAC,CAAC;MAC1B,CAAE;MACF0D,cAAc,EAAEvK,OAAQ;MACxBwK,KAAK,EAAE,GAAI;MAAAxG,QAAA,eAEXhF,OAAA,CAAC5B,IAAI;QACHqN,IAAI,EAAEvJ,UAAW;QACjBwJ,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UAAEvI,MAAM,EAAE;QAAW,CAAE;QAAA4B,QAAA,gBAEtChF,OAAA,CAACrB,GAAG;UAACwK,MAAM,EAAE,EAAG;UAAAnE,QAAA,gBACdhF,OAAA,CAACpB,GAAG;YAAC0K,IAAI,EAAE,EAAG;YAAAtE,QAAA,eACZhF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;cACRiB,IAAI,EAAC,OAAO;cACZhC,KAAK,EAAC,0BAAM;cACZiC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvN,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhDhF,OAAA,CAAC3B,KAAK;gBAAC0N,WAAW,EAAC;cAAS;gBAAA9G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNpF,OAAA,CAACpB,GAAG;YAAC0K,IAAI,EAAE,CAAE;YAAAtE,QAAA,eACXhF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;cACRiB,IAAI,EAAC,cAAc;cACnBhC,KAAK,EAAC,oBAAK;cACXiC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvN,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAyG,QAAA,eAE/ChF,OAAA,CAAC3B,KAAK;gBAACmH,IAAI,EAAC,QAAQ;gBAACuG,WAAW,EAAC;cAAG;gBAAA9G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;UACRiB,IAAI,EAAC,SAAS;UACdhC,KAAK,EAAC,0BAAM;UACZiC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvN,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyG,QAAA,eAEhDhF,OAAA,CAACG,QAAQ;YACP6L,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAmB;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZpF,OAAA,CAACrB,GAAG;UAACwK,MAAM,EAAE,EAAG;UAAAnE,QAAA,gBACdhF,OAAA,CAACpB,GAAG;YAAC0K,IAAI,EAAE,EAAG;YAAAtE,QAAA,eACZhF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;cACRiB,IAAI,EAAC,QAAQ;cACbhC,KAAK,EAAC,cAAI;cACViC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvN,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhDhF,OAAA,CAAC1B,MAAM;gBAAA0G,QAAA,EACJe,MAAM,CAACC,OAAO,CAAC1D,kBAAkB,CAAC,CAAC2D,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEqB,MAAM,CAAC,kBACpDlG,OAAA,CAACI,MAAM;kBAAW+F,KAAK,EAAEtB,GAAI;kBAAAG,QAAA,EAAEkB,MAAM,CAACzD;gBAAI,GAA7BoC,GAAG;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAmC,CACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNpF,OAAA,CAACpB,GAAG;YAAC0K,IAAI,EAAE,EAAG;YAAAtE,QAAA,eACZhF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;cACRiB,IAAI,EAAC,aAAa;cAClBhC,KAAK,EAAC,0BAAM;cAAA5E,QAAA,eAEZhF,OAAA,CAAC3B,KAAK;gBAACmH,IAAI,EAAC,QAAQ;gBAACuG,WAAW,EAAC,OAAO;gBAACvC,MAAM,EAAC;cAAG;gBAAAvE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;UACRiB,IAAI,EAAC,SAAS;UACdhC,KAAK,EAAC,0BAAM;UAAA5E,QAAA,eAEZhF,OAAA,CAACG,QAAQ;YACP6L,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAqB;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRpF,OAAA,CAAC7B,KAAK;MACJ+E,KAAK,EAAExB,cAAc,GAAG,MAAM,GAAG,MAAO;MACxC0J,IAAI,EAAEhK,mBAAoB;MAC1BiK,IAAI,EAAE1C,oBAAqB;MAC3B2C,QAAQ,EAAEA,CAAA,KAAM;QACdjK,sBAAsB,CAAC,KAAK,CAAC;QAC7Be,WAAW,CAACyF,WAAW,CAAC,CAAC;MAC3B,CAAE;MACF0D,cAAc,EAAEvK,OAAQ;MACxBwK,KAAK,EAAE,GAAI;MAAAxG,QAAA,eAEXhF,OAAA,CAAC5B,IAAI;QACHqN,IAAI,EAAErJ,WAAY;QAClBsJ,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UAAEvI,MAAM,EAAE;QAAW,CAAE;QAAA4B,QAAA,gBAEtChF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;UACRiB,IAAI,EAAC,UAAU;UACfhC,KAAK,EAAC,0BAAM;UACZqC,IAAI,EAAC,8DAAY;UAAAjH,QAAA,eAEjBhF,OAAA,CAAC1B,MAAM;YAACyN,WAAW,EAAC,kGAAkB;YAACG,UAAU;YAAAlH,QAAA,EAC9CpE,OAAO,CAACqF,GAAG,CAACkB,MAAM,iBACjBnH,OAAA,CAACI,MAAM;cAAiB+F,KAAK,EAAEgB,MAAM,CAAC1G,EAAG;cAAAuE,QAAA,EACtCmC,MAAM,CAACjE;YAAK,GADFiE,MAAM,CAAC1G,EAAE;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZpF,OAAA,CAACrB,GAAG;UAACwK,MAAM,EAAE,EAAG;UAAAnE,QAAA,gBACdhF,OAAA,CAACpB,GAAG;YAAC0K,IAAI,EAAE,EAAG;YAAAtE,QAAA,eACZhF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;cACRiB,IAAI,EAAC,OAAO;cACZhC,KAAK,EAAC,0BAAM;cACZiC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvN,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAyG,QAAA,eAEhDhF,OAAA,CAAC3B,KAAK;gBAAC0N,WAAW,EAAC;cAAS;gBAAA9G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNpF,OAAA,CAACpB,GAAG;YAAC0K,IAAI,EAAE,CAAE;YAAAtE,QAAA,eACXhF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;cACRiB,IAAI,EAAC,eAAe;cACpBhC,KAAK,EAAC,0BAAM;cACZqC,IAAI,EAAC,4CAAS;cAAAjH,QAAA,eAEdhF,OAAA,CAAC3B,KAAK;gBAACmH,IAAI,EAAC,QAAQ;gBAACuG,WAAW,EAAC;cAAG;gBAAA9G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENpF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;UACRiB,IAAI,EAAC,QAAQ;UACbhC,KAAK,EAAC,cAAI;UACViC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvN,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAyG,QAAA,eAEhDhF,OAAA,CAAC1B,MAAM;YAAA0G,QAAA,EACJe,MAAM,CAACC,OAAO,CAACjD,mBAAmB,CAAC,CAACkD,GAAG,CAAC,CAAC,CAACpB,GAAG,EAAEqB,MAAM,CAAC,kBACrDlG,OAAA,CAACI,MAAM;cAAW+F,KAAK,EAAEtB,GAAI;cAAAG,QAAA,EAAEkB,MAAM,CAACzD;YAAI,GAA7BoC,GAAG;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAmC,CACpD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZpF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;UACRiB,IAAI,EAAC,SAAS;UACdhC,KAAK,EAAC,0BAAM;UAAA5E,QAAA,eAEZhF,OAAA,CAACG,QAAQ;YACP6L,IAAI,EAAE,CAAE;YACRD,WAAW,EAAC;UAAoB;YAAA9G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRpF,OAAA,CAAC7B,KAAK;MACJ+E,KAAK,EAAC,4CAAS;MACfkI,IAAI,EAAE9J,kBAAmB;MACzB+J,IAAI,EAAEvC,mBAAoB;MAC1BwC,QAAQ,EAAEA,CAAA,KAAM;QACd/J,qBAAqB,CAAC,KAAK,CAAC;QAC5Bc,UAAU,CAACwF,WAAW,CAAC,CAAC;MAC1B,CAAE;MACF0D,cAAc,EAAEvK,OAAQ;MACxBwK,KAAK,EAAE,GAAI;MAAAxG,QAAA,eAEXhF,OAAA,CAAC5B,IAAI;QACHqN,IAAI,EAAEpJ,UAAW;QACjBqJ,MAAM,EAAC,UAAU;QAAA1G,QAAA,gBAEjBhF,OAAA;UAAKoJ,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAG,CAAE;UAAArE,QAAA,gBAC/BhF,OAAA,CAACE,IAAI;YAACmF,MAAM;YAAAL,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvBpF,OAAA,CAACE,IAAI;YAAA8E,QAAA,EAAEpD,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsB;UAAK;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAENpF,OAAA,CAAC5B,IAAI,CAACuM,IAAI;UACRiB,IAAI,EAAC,UAAU;UACfhC,KAAK,EAAC,0BAAM;UACZqC,IAAI,EAAC,oHAAqB;UAAAjH,QAAA,eAE1BhF,OAAA,CAAC1B,MAAM;YAACyN,WAAW,EAAC,gCAAO;YAACG,UAAU;YAAAlH,QAAA,EACnCpE,OAAO,CAACqF,GAAG,CAACkB,MAAM,iBACjBnH,OAAA,CAACI,MAAM;cAAiB+F,KAAK,EAAEgB,MAAM,CAAC1G,EAAG;cAAAuE,QAAA,EACtCmC,MAAM,CAACjE;YAAK,GADFiE,MAAM,CAAC1G,EAAE;cAAAwE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEd,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAz+BID,UAAU;EAAA,QACY7C,SAAS,EAClBC,WAAW,EAYPS,IAAI,CAAC+D,OAAO,EACX/D,IAAI,CAAC+D,OAAO,EACb/D,IAAI,CAAC+D,OAAO;AAAA;AAAAgK,EAAA,GAhB7B5L,UAAU;AA2+BhB,eAAeA,UAAU;AAAC,IAAA4L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}