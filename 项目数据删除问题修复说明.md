# 项目数据删除问题修复说明

## 问题描述

用户反映项目数据删除后仍然存在，经过分析发现了以下几个关键问题：

## 问题分析

### 1. 后端软删除查询过滤缺失

**问题**：在 `project_data_service.py` 中，获取项目数据时没有过滤软删除的记录。

**影响**：已删除的数据仍然会在查询结果中显示。

**修复位置**：
- `backend/app/services/project_data_service.py`
- 方法：`get_project_data()`, `get_project_model_data()`, `update_project_data()`, `delete_project_data()`, `get_project_statistics()`, `copy_project_data()`

**修复内容**：
```python
# 修复前
items = self.db.query(model_class).filter(
    model_class.project_id == project_id
).all()

# 修复后
query = self.db.query(model_class).filter(
    model_class.project_id == project_id
)

# 如果模型支持软删除，则过滤已删除的记录
if hasattr(model_class, 'is_deleted'):
    query = query.filter(model_class.is_deleted == False)

items = query.all()
```

### 2. 前端删除逻辑问题

**问题**：前端很多页面的删除操作只是模拟API调用，没有真正调用后端API。

**影响**：删除操作只在前端生效，刷新页面后数据重新出现。

**修复页面**：
- `frontend/src/pages/ProjectList.js` - 项目删除
- `frontend/src/pages/CharacterList.js` - 人物删除
- `frontend/src/pages/FactionList.js` - 势力删除
- `frontend/src/pages/PlotList.js` - 剧情删除
- `frontend/src/pages/Timeline.js` - 时间线事件删除
- `frontend/src/pages/VolumeList.js` - 卷宗和章节删除
- `frontend/src/pages/SpiritualTreasureSystems.js` - 灵宝体系删除
- `frontend/src/pages/PetSystems.js` - 宠物体系删除
- `frontend/src/pages/EquipmentSystems.js` - 装备体系删除
- `frontend/src/pages/DimensionStructure.js` - 维度结构删除
- `frontend/src/pages/MapStructure.js` - 地图结构删除
- `frontend/src/pages/SecretRealms.js` - 秘境分布删除

**修复内容**：
```javascript
// 修复前
const handleDelete = (id) => {
  setData(data.filter(item => item.id !== id));
  message.success('删除成功');
};

// 修复后
const handleDelete = async (id) => {
  try {
    // 调用后端API删除数据
    await axios.delete(`/api/project-data/projects/${projectId}/data/${modelName}/${id}`);

    // 删除成功后从列表中移除
    setData(data.filter(item => item.id !== id));
    message.success('删除成功');
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};
```

## 修复效果

### 后端修复效果
1. **软删除过滤**：所有查询操作都会自动过滤已删除的记录
2. **数据一致性**：确保软删除的数据不会在任何查询中出现
3. **统计准确性**：项目统计信息不包含已删除的数据

### 前端修复效果
1. **真实删除**：删除操作会真正调用后端API
2. **持久化删除**：删除后刷新页面数据不会重新出现
3. **错误处理**：删除失败时会显示错误信息

## ESLint错误修复

### 修复的导入问题
在修复删除功能的过程中，我们还解决了多个ESLint错误：

1. **axios导入缺失**：为所有需要调用删除API的页面添加了`import axios from 'axios'`
2. **useParams导入缺失**：为需要获取projectId的页面添加了`import { useParams } from 'react-router-dom'`
3. **projectId变量缺失**：为所有页面组件添加了`const { id: projectId } = useParams()`

### 修复的页面列表
以下页面都已修复导入和变量问题：
- `frontend/src/pages/ProjectList.js`
- `frontend/src/pages/CharacterList.js`
- `frontend/src/pages/FactionList.js`
- `frontend/src/pages/PlotList.js`
- `frontend/src/pages/Timeline.js`
- `frontend/src/pages/VolumeList.js`
- `frontend/src/pages/SpiritualTreasureSystems.js`
- `frontend/src/pages/PetSystems.js`
- `frontend/src/pages/EquipmentSystems.js`
- `frontend/src/pages/DimensionStructure.js`
- `frontend/src/pages/MapStructure.js`
- `frontend/src/pages/SecretRealms.js`

### 编译状态
✅ **编译成功**：项目现在可以成功编译，没有ESLint错误
⚠️ **仍有警告**：存在一些未使用变量和依赖项的警告，但不影响功能

## API调用规范

### 项目数据删除API
```
DELETE /api/project-data/projects/{project_id}/data/{model_name}/{item_id}
```

### 项目删除API
```
DELETE /api/projects/{project_id}
```

### 体系数据删除API
```
DELETE /api/{system-type}/{system_id}
```

## 数据模型映射

| 前端页面 | 模型名称 | API路径 |
|---------|---------|---------|
| 人物管理 | character | character |
| 势力管理 | faction | faction |
| 剧情管理 | plot | plot |
| 时间线 | timeline | timeline |
| 卷宗管理 | volume | volume |
| 章节管理 | chapter | chapter |
| 灵宝体系 | spiritual_treasure_system | spiritual_treasure_system |
| 宠物体系 | pet_system | pet_system |
| 装备体系 | equipment_system | equipment_system |
| 维度结构 | dimension_structure | dimension_structure |
| 地图结构 | map_structure | map_structure |
| 秘境分布 | secret_realm_distribution | secret_realm_distribution |

## 测试建议

1. **删除测试**：在各个管理页面测试删除功能
2. **刷新测试**：删除后刷新页面确认数据不再显示
3. **软删除测试**：检查数据库中软删除字段是否正确设置
4. **统计测试**：确认删除后统计数据正确更新

## 注意事项

1. **软删除vs硬删除**：系统使用软删除机制，数据不会从数据库中物理删除
2. **级联删除**：删除卷宗时会同时删除其下的章节
3. **权限检查**：预置项目不允许删除
4. **错误处理**：所有删除操作都包含错误处理和用户提示

## 相关文件

### 后端文件
- `backend/app/services/project_data_service.py`
- `backend/app/models/base.py`
- `backend/app/api/endpoints/project_data.py`

### 前端文件
- 所有管理页面的删除逻辑
- 项目数据相关的API调用

修复完成后，项目数据删除功能应该能够正常工作，删除的数据不会再重新出现。
