{"ast": null, "code": "import _objectSpread from\"D:/AI_project/\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect}from'react';import{Card,Table,Button,Space,Tag,Progress,Modal,Form,Input,Select,message,Popconfirm,Typography,Row,Col}from'antd';import{PlusOutlined,EditOutlined,DeleteOutlined,EyeOutlined,CopyOutlined,ExportOutlined,ImportOutlined}from'@ant-design/icons';import{useNavigate}from'react-router-dom';import axios from'axios';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const{Title}=Typography;const{Option}=Select;const{TextArea}=Input;const ProjectList=()=>{const navigate=useNavigate();const[projects,setProjects]=useState([]);const[loading,setLoading]=useState(false);const[modalVisible,setModalVisible]=useState(false);const[editingProject,setEditingProject]=useState(null);const[form]=Form.useForm();// 模拟数据\nconst mockProjects=[{id:1,name:'仙侠传说',title:'九天仙缘录',author:'作者A',type:'xianxia',status:'writing',wordCount:89000,chapterCount:45,characterCount:12,progress:65,createdAt:'2024-01-01',updatedAt:'2024-01-15',isPreset:false},{id:2,name:'星际征途',title:'银河帝国崛起',author:'作者B',type:'scifi',status:'planning',wordCount:12000,chapterCount:8,characterCount:6,progress:25,createdAt:'2024-01-10',updatedAt:'2024-01-14',isPreset:false},{id:3,name:'都市修仙',title:'现代仙侠录',author:'作者C',type:'modern',status:'completed',wordCount:156000,chapterCount:78,characterCount:20,progress:100,createdAt:'2023-12-01',updatedAt:'2024-01-13',isPreset:false},{id:4,name:'【预置】玄幻世界模板',title:'修仙世界设定模板',author:'系统',type:'fantasy',status:'completed',wordCount:50000,chapterCount:20,characterCount:15,progress:100,createdAt:'2024-01-01',updatedAt:'2024-01-01',isPreset:true},{id:5,name:'【预置】现代都市模板',title:'都市异能设定模板',author:'系统',type:'modern',status:'completed',wordCount:30000,chapterCount:15,characterCount:10,progress:100,createdAt:'2024-01-01',updatedAt:'2024-01-01',isPreset:true}];useEffect(()=>{loadProjects();},[]);const loadProjects=async()=>{setLoading(true);try{// 模拟API调用\nsetTimeout(()=>{setProjects(mockProjects);setLoading(false);},1000);}catch(error){message.error('加载项目列表失败');setLoading(false);}};const getStatusColor=status=>{const colors={planning:'blue',writing:'green',reviewing:'orange',completed:'purple',published:'gold'};return colors[status]||'default';};const getStatusText=status=>{const texts={planning:'规划中',writing:'写作中',reviewing:'审阅中',completed:'已完成',published:'已发布'};return texts[status]||status;};const getTypeText=type=>{const texts={fantasy:'奇幻',xianxia:'仙侠',wuxia:'武侠',scifi:'科幻',modern:'现代',historical:'历史',romance:'言情'};return texts[type]||type;};const handleCreate=()=>{setEditingProject(null);form.resetFields();setModalVisible(true);};const handleEdit=project=>{if(project.isPreset){message.warning('预置项目不允许编辑');return;}setEditingProject(project);form.setFieldsValue(project);setModalVisible(true);};const handleDelete=async id=>{try{const project=projects.find(p=>p.id===id);if(project&&project.isPreset){message.warning('预置项目不允许删除');return;}// 调用后端API删除项目\nawait axios.delete(\"/api/projects/\".concat(id));// 删除成功后从列表中移除\nsetProjects(projects.filter(p=>p.id!==id));message.success('项目删除成功');}catch(error){console.error('删除项目失败:',error);message.error('删除项目失败');}};const handleSubmit=async values=>{try{if(editingProject){// 更新项目\nconst updatedProjects=projects.map(p=>p.id===editingProject.id?_objectSpread(_objectSpread({},p),values):p);setProjects(updatedProjects);message.success('项目更新成功');}else{// 创建项目\nconst newProject=_objectSpread(_objectSpread({id:Date.now()},values),{},{wordCount:0,chapterCount:0,characterCount:0,progress:0,createdAt:new Date().toISOString().split('T')[0],updatedAt:new Date().toISOString().split('T')[0]});setProjects([...projects,newProject]);message.success('项目创建成功');}setModalVisible(false);}catch(error){message.error('操作失败');}};const handleDuplicate=async project=>{try{const newProject=_objectSpread(_objectSpread({},project),{},{id:Date.now(),name:\"\".concat(project.name,\" (\\u526F\\u672C)\"),status:'planning',progress:0,createdAt:new Date().toISOString().split('T')[0],updatedAt:new Date().toISOString().split('T')[0]});setProjects([...projects,newProject]);message.success('项目复制成功');}catch(error){message.error('复制项目失败');}};const columns=[{title:'项目名称',dataIndex:'name',key:'name',render:(text,record)=>/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"div\",{style:{fontWeight:'bold',display:'flex',alignItems:'center',gap:'8px'},children:[text,record.isPreset&&/*#__PURE__*/_jsx(Tag,{color:\"blue\",size:\"small\",children:\"\\u9884\\u7F6E\"})]}),record.title&&/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'12px',color:'#666'},children:record.title})]})},{title:'作者',dataIndex:'author',key:'author'},{title:'类型',dataIndex:'type',key:'type',render:type=>/*#__PURE__*/_jsx(Tag,{children:getTypeText(type)})},{title:'状态',dataIndex:'status',key:'status',render:status=>/*#__PURE__*/_jsx(Tag,{color:getStatusColor(status),children:getStatusText(status)})},{title:'进度',dataIndex:'progress',key:'progress',render:progress=>/*#__PURE__*/_jsx(Progress,{percent:progress,size:\"small\",status:progress===100?'success':'active'})},{title:'统计',key:'stats',render:(_,record)=>/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'12px'},children:[/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u5B57\\u6570: \",(record.wordCount/10000).toFixed(1),\"\\u4E07\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u7AE0\\u8282: \",record.chapterCount]}),/*#__PURE__*/_jsxs(\"div\",{children:[\"\\u4EBA\\u7269: \",record.characterCount]})]})},{title:'最后修改',dataIndex:'updatedAt',key:'updatedAt'},{title:'操作',key:'actions',render:(_,record)=>/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{type:\"link\",icon:/*#__PURE__*/_jsx(EyeOutlined,{}),onClick:()=>navigate(\"/projects/\".concat(record.id)),children:\"\\u67E5\\u770B\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",icon:/*#__PURE__*/_jsx(EditOutlined,{}),onClick:()=>handleEdit(record),disabled:record.isPreset,title:record.isPreset?'预置项目不允许编辑':'',children:\"\\u7F16\\u8F91\"}),/*#__PURE__*/_jsx(Button,{type:\"link\",icon:/*#__PURE__*/_jsx(CopyOutlined,{}),onClick:()=>handleDuplicate(record),children:\"\\u590D\\u5236\"}),!record.isPreset&&/*#__PURE__*/_jsx(Popconfirm,{title:\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u9879\\u76EE\\u5417\\uFF1F\",onConfirm:()=>handleDelete(record.id),okText:\"\\u786E\\u5B9A\",cancelText:\"\\u53D6\\u6D88\",children:/*#__PURE__*/_jsx(Button,{type:\"link\",danger:true,icon:/*#__PURE__*/_jsx(DeleteOutlined,{}),children:\"\\u5220\\u9664\"})})]})}];return/*#__PURE__*/_jsxs(\"div\",{className:\"fade-in\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"page-header\",children:/*#__PURE__*/_jsx(Title,{level:2,className:\"page-title\",children:\"\\u9879\\u76EE\\u7BA1\\u7406\"})}),/*#__PURE__*/_jsxs(Card,{children:[/*#__PURE__*/_jsxs(\"div\",{className:\"toolbar\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"toolbar-left\",children:/*#__PURE__*/_jsx(Button,{type:\"primary\",icon:/*#__PURE__*/_jsx(PlusOutlined,{}),onClick:handleCreate,children:\"\\u65B0\\u5EFA\\u9879\\u76EE\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"toolbar-right\",children:/*#__PURE__*/_jsxs(Space,{children:[/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ImportOutlined,{}),children:\"\\u5BFC\\u5165\\u9879\\u76EE\"}),/*#__PURE__*/_jsx(Button,{icon:/*#__PURE__*/_jsx(ExportOutlined,{}),children:\"\\u5BFC\\u51FA\\u9879\\u76EE\"})]})})]}),/*#__PURE__*/_jsx(Table,{columns:columns,dataSource:projects,rowKey:\"id\",loading:loading,pagination:{showSizeChanger:true,showQuickJumper:true,showTotal:total=>\"\\u5171 \".concat(total,\" \\u4E2A\\u9879\\u76EE\")}})]}),/*#__PURE__*/_jsx(Modal,{title:editingProject?'编辑项目':'新建项目',open:modalVisible,onCancel:()=>setModalVisible(false),onOk:()=>form.submit(),width:600,children:/*#__PURE__*/_jsxs(Form,{form:form,layout:\"vertical\",onFinish:handleSubmit,children:[/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"name\",label:\"\\u9879\\u76EE\\u540D\\u79F0\",rules:[{required:true,message:'请输入项目名称'}],children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"title\",label:\"\\u5C0F\\u8BF4\\u6807\\u9898\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u5C0F\\u8BF4\\u6807\\u9898\"})})})]}),/*#__PURE__*/_jsxs(Row,{gutter:16,children:[/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"author\",label:\"\\u4F5C\\u8005\",children:/*#__PURE__*/_jsx(Input,{placeholder:\"\\u8BF7\\u8F93\\u5165\\u4F5C\\u8005\\u540D\\u79F0\"})})}),/*#__PURE__*/_jsx(Col,{span:12,children:/*#__PURE__*/_jsx(Form.Item,{name:\"type\",label:\"\\u9879\\u76EE\\u7C7B\\u578B\",rules:[{required:true,message:'请选择项目类型'}],children:/*#__PURE__*/_jsxs(Select,{placeholder:\"\\u8BF7\\u9009\\u62E9\\u9879\\u76EE\\u7C7B\\u578B\",children:[/*#__PURE__*/_jsx(Option,{value:\"fantasy\",children:\"\\u5947\\u5E7B\"}),/*#__PURE__*/_jsx(Option,{value:\"xianxia\",children:\"\\u4ED9\\u4FA0\"}),/*#__PURE__*/_jsx(Option,{value:\"wuxia\",children:\"\\u6B66\\u4FA0\"}),/*#__PURE__*/_jsx(Option,{value:\"scifi\",children:\"\\u79D1\\u5E7B\"}),/*#__PURE__*/_jsx(Option,{value:\"modern\",children:\"\\u73B0\\u4EE3\"}),/*#__PURE__*/_jsx(Option,{value:\"historical\",children:\"\\u5386\\u53F2\"}),/*#__PURE__*/_jsx(Option,{value:\"romance\",children:\"\\u8A00\\u60C5\"})]})})})]}),/*#__PURE__*/_jsx(Form.Item,{name:\"summary\",label:\"\\u9879\\u76EE\\u7B80\\u4ECB\",children:/*#__PURE__*/_jsx(TextArea,{rows:4,placeholder:\"\\u8BF7\\u8F93\\u5165\\u9879\\u76EE\\u7B80\\u4ECB\"})})]})})]});};export default ProjectList;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Tag", "Progress", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "Typography", "Row", "Col", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "CopyOutlined", "ExportOutlined", "ImportOutlined", "useNavigate", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "Title", "Option", "TextArea", "ProjectList", "navigate", "projects", "setProjects", "loading", "setLoading", "modalVisible", "setModalVisible", "editingProject", "setEditingProject", "form", "useForm", "mockProjects", "id", "name", "title", "author", "type", "status", "wordCount", "chapterCount", "characterCount", "progress", "createdAt", "updatedAt", "isPreset", "loadProjects", "setTimeout", "error", "getStatusColor", "colors", "planning", "writing", "reviewing", "completed", "published", "getStatusText", "texts", "getTypeText", "fantasy", "xianxia", "wuxia", "scifi", "modern", "historical", "romance", "handleCreate", "resetFields", "handleEdit", "project", "warning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "find", "p", "delete", "concat", "filter", "success", "console", "handleSubmit", "values", "updatedProjects", "map", "_objectSpread", "newProject", "Date", "now", "toISOString", "split", "handleDuplicate", "columns", "dataIndex", "key", "render", "text", "record", "children", "style", "fontWeight", "display", "alignItems", "gap", "color", "size", "fontSize", "percent", "_", "toFixed", "icon", "onClick", "disabled", "onConfirm", "okText", "cancelText", "danger", "className", "level", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "width", "layout", "onFinish", "gutter", "span", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "value", "rows"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/ProjectList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Tag,\n  Progress,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  CopyOutlined,\n  ExportOutlined,\n  ImportOutlined\n} from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport axios from 'axios';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst ProjectList = () => {\n  const navigate = useNavigate();\n  const [projects, setProjects] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingProject, setEditingProject] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockProjects = [\n    {\n      id: 1,\n      name: '仙侠传说',\n      title: '九天仙缘录',\n      author: '作者A',\n      type: 'xianxia',\n      status: 'writing',\n      wordCount: 89000,\n      chapterCount: 45,\n      characterCount: 12,\n      progress: 65,\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-15',\n      isPreset: false\n    },\n    {\n      id: 2,\n      name: '星际征途',\n      title: '银河帝国崛起',\n      author: '作者B',\n      type: 'scifi',\n      status: 'planning',\n      wordCount: 12000,\n      chapterCount: 8,\n      characterCount: 6,\n      progress: 25,\n      createdAt: '2024-01-10',\n      updatedAt: '2024-01-14',\n      isPreset: false\n    },\n    {\n      id: 3,\n      name: '都市修仙',\n      title: '现代仙侠录',\n      author: '作者C',\n      type: 'modern',\n      status: 'completed',\n      wordCount: 156000,\n      chapterCount: 78,\n      characterCount: 20,\n      progress: 100,\n      createdAt: '2023-12-01',\n      updatedAt: '2024-01-13',\n      isPreset: false\n    },\n    {\n      id: 4,\n      name: '【预置】玄幻世界模板',\n      title: '修仙世界设定模板',\n      author: '系统',\n      type: 'fantasy',\n      status: 'completed',\n      wordCount: 50000,\n      chapterCount: 20,\n      characterCount: 15,\n      progress: 100,\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-01',\n      isPreset: true\n    },\n    {\n      id: 5,\n      name: '【预置】现代都市模板',\n      title: '都市异能设定模板',\n      author: '系统',\n      type: 'modern',\n      status: 'completed',\n      wordCount: 30000,\n      chapterCount: 15,\n      characterCount: 10,\n      progress: 100,\n      createdAt: '2024-01-01',\n      updatedAt: '2024-01-01',\n      isPreset: true\n    }\n  ];\n\n  useEffect(() => {\n    loadProjects();\n  }, []);\n\n  const loadProjects = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setProjects(mockProjects);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      message.error('加载项目列表失败');\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n\n  const getTypeText = (type) => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n\n  const handleCreate = () => {\n    setEditingProject(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (project) => {\n    if (project.isPreset) {\n      message.warning('预置项目不允许编辑');\n      return;\n    }\n    setEditingProject(project);\n    form.setFieldsValue(project);\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      const project = projects.find(p => p.id === id);\n      if (project && project.isPreset) {\n        message.warning('预置项目不允许删除');\n        return;\n      }\n\n      // 调用后端API删除项目\n      await axios.delete(`/api/projects/${id}`);\n\n      // 删除成功后从列表中移除\n      setProjects(projects.filter(p => p.id !== id));\n      message.success('项目删除成功');\n    } catch (error) {\n      console.error('删除项目失败:', error);\n      message.error('删除项目失败');\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    try {\n      if (editingProject) {\n        // 更新项目\n        const updatedProjects = projects.map(p =>\n          p.id === editingProject.id ? { ...p, ...values } : p\n        );\n        setProjects(updatedProjects);\n        message.success('项目更新成功');\n      } else {\n        // 创建项目\n        const newProject = {\n          id: Date.now(),\n          ...values,\n          wordCount: 0,\n          chapterCount: 0,\n          characterCount: 0,\n          progress: 0,\n          createdAt: new Date().toISOString().split('T')[0],\n          updatedAt: new Date().toISOString().split('T')[0]\n        };\n        setProjects([...projects, newProject]);\n        message.success('项目创建成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('操作失败');\n    }\n  };\n\n  const handleDuplicate = async (project) => {\n    try {\n      const newProject = {\n        ...project,\n        id: Date.now(),\n        name: `${project.name} (副本)`,\n        status: 'planning',\n        progress: 0,\n        createdAt: new Date().toISOString().split('T')[0],\n        updatedAt: new Date().toISOString().split('T')[0]\n      };\n      setProjects([...projects, newProject]);\n      message.success('项目复制成功');\n    } catch (error) {\n      message.error('复制项目失败');\n    }\n  };\n\n  const columns = [\n    {\n      title: '项目名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <div>\n          <div style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: '8px' }}>\n            {text}\n            {record.isPreset && (\n              <Tag color=\"blue\" size=\"small\">预置</Tag>\n            )}\n          </div>\n          {record.title && (\n            <div style={{ fontSize: '12px', color: '#666' }}>{record.title}</div>\n          )}\n        </div>\n      ),\n    },\n    {\n      title: '作者',\n      dataIndex: 'author',\n      key: 'author',\n    },\n    {\n      title: '类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type) => <Tag>{getTypeText(type)}</Tag>,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>\n      ),\n    },\n    {\n      title: '进度',\n      dataIndex: 'progress',\n      key: 'progress',\n      render: (progress) => (\n        <Progress\n          percent={progress}\n          size=\"small\"\n          status={progress === 100 ? 'success' : 'active'}\n        />\n      ),\n    },\n    {\n      title: '统计',\n      key: 'stats',\n      render: (_, record) => (\n        <div style={{ fontSize: '12px' }}>\n          <div>字数: {(record.wordCount / 10000).toFixed(1)}万</div>\n          <div>章节: {record.chapterCount}</div>\n          <div>人物: {record.characterCount}</div>\n        </div>\n      ),\n    },\n    {\n      title: '最后修改',\n      dataIndex: 'updatedAt',\n      key: 'updatedAt',\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Button\n            type=\"link\"\n            icon={<EyeOutlined />}\n            onClick={() => navigate(`/projects/${record.id}`)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n            disabled={record.isPreset}\n            title={record.isPreset ? '预置项目不允许编辑' : ''}\n          >\n            编辑\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<CopyOutlined />}\n            onClick={() => handleDuplicate(record)}\n          >\n            复制\n          </Button>\n          {!record.isPreset && (\n            <Popconfirm\n              title=\"确定要删除这个项目吗？\"\n              onConfirm={() => handleDelete(record.id)}\n              okText=\"确定\"\n              cancelText=\"取消\"\n            >\n              <Button type=\"link\" danger icon={<DeleteOutlined />}>\n                删除\n              </Button>\n            </Popconfirm>\n          )}\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">项目管理</Title>\n      </div>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreate}\n            >\n              新建项目\n            </Button>\n          </div>\n          <div className=\"toolbar-right\">\n            <Space>\n              <Button icon={<ImportOutlined />}>导入项目</Button>\n              <Button icon={<ExportOutlined />}>导出项目</Button>\n            </Space>\n          </div>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={projects}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个项目`,\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingProject ? '编辑项目' : '新建项目'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"项目名称\"\n                rules={[{ required: true, message: '请输入项目名称' }]}\n              >\n                <Input placeholder=\"请输入项目名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"title\"\n                label=\"小说标题\"\n              >\n                <Input placeholder=\"请输入小说标题\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"author\"\n                label=\"作者\"\n              >\n                <Input placeholder=\"请输入作者名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"type\"\n                label=\"项目类型\"\n                rules={[{ required: true, message: '请选择项目类型' }]}\n              >\n                <Select placeholder=\"请选择项目类型\">\n                  <Option value=\"fantasy\">奇幻</Option>\n                  <Option value=\"xianxia\">仙侠</Option>\n                  <Option value=\"wuxia\">武侠</Option>\n                  <Option value=\"scifi\">科幻</Option>\n                  <Option value=\"modern\">现代</Option>\n                  <Option value=\"historical\">历史</Option>\n                  <Option value=\"romance\">言情</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"summary\"\n            label=\"项目简介\"\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入项目简介\"\n            />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProjectList;\n"], "mappings": "gLAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,KAAK,CACLC,GAAG,CACHC,QAAQ,CACRC,KAAK,CACLC,IAAI,CACJC,KAAK,CACLC,MAAM,CACNC,OAAO,CACPC,UAAU,CACVC,UAAU,CACVC,GAAG,CACHC,GAAG,KACE,MAAM,CACb,OACEC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,YAAY,CACZC,cAAc,CACdC,cAAc,KACT,mBAAmB,CAC1B,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,MAAO,CAAAC,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAEC,KAAM,CAAC,CAAGhB,UAAU,CAC5B,KAAM,CAAEiB,MAAO,CAAC,CAAGpB,MAAM,CACzB,KAAM,CAAEqB,QAAS,CAAC,CAAGtB,KAAK,CAE1B,KAAM,CAAAuB,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,QAAQ,CAAGV,WAAW,CAAC,CAAC,CAC9B,KAAM,CAACW,QAAQ,CAAEC,WAAW,CAAC,CAAGpC,QAAQ,CAAC,EAAE,CAAC,CAC5C,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGtC,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACuC,YAAY,CAAEC,eAAe,CAAC,CAAGxC,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACyC,cAAc,CAAEC,iBAAiB,CAAC,CAAG1C,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAAC2C,IAAI,CAAC,CAAGlC,IAAI,CAACmC,OAAO,CAAC,CAAC,CAE7B;AACA,KAAM,CAAAC,YAAY,CAAG,CACnB,CACEC,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,SAAS,CAAE,KAAK,CAChBC,YAAY,CAAE,EAAE,CAChBC,cAAc,CAAE,EAAE,CAClBC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YAAY,CACvBC,QAAQ,CAAE,KACZ,CAAC,CACD,CACEZ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,OAAO,CACbC,MAAM,CAAE,UAAU,CAClBC,SAAS,CAAE,KAAK,CAChBC,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YAAY,CACvBC,QAAQ,CAAE,KACZ,CAAC,CACD,CACEZ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,MAAM,CACZC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,KAAK,CACbC,IAAI,CAAE,QAAQ,CACdC,MAAM,CAAE,WAAW,CACnBC,SAAS,CAAE,MAAM,CACjBC,YAAY,CAAE,EAAE,CAChBC,cAAc,CAAE,EAAE,CAClBC,QAAQ,CAAE,GAAG,CACbC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YAAY,CACvBC,QAAQ,CAAE,KACZ,CAAC,CACD,CACEZ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,UAAU,CACjBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,WAAW,CACnBC,SAAS,CAAE,KAAK,CAChBC,YAAY,CAAE,EAAE,CAChBC,cAAc,CAAE,EAAE,CAClBC,QAAQ,CAAE,GAAG,CACbC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YAAY,CACvBC,QAAQ,CAAE,IACZ,CAAC,CACD,CACEZ,EAAE,CAAE,CAAC,CACLC,IAAI,CAAE,YAAY,CAClBC,KAAK,CAAE,UAAU,CACjBC,MAAM,CAAE,IAAI,CACZC,IAAI,CAAE,QAAQ,CACdC,MAAM,CAAE,WAAW,CACnBC,SAAS,CAAE,KAAK,CAChBC,YAAY,CAAE,EAAE,CAChBC,cAAc,CAAE,EAAE,CAClBC,QAAQ,CAAE,GAAG,CACbC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YAAY,CACvBC,QAAQ,CAAE,IACZ,CAAC,CACF,CAEDzD,SAAS,CAAC,IAAM,CACd0D,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAA,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/BrB,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF;AACAsB,UAAU,CAAC,IAAM,CACfxB,WAAW,CAACS,YAAY,CAAC,CACzBP,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOuB,KAAK,CAAE,CACdjD,OAAO,CAACiD,KAAK,CAAC,UAAU,CAAC,CACzBvB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAwB,cAAc,CAAIX,MAAM,EAAK,CACjC,KAAM,CAAAY,MAAM,CAAG,CACbC,QAAQ,CAAE,MAAM,CAChBC,OAAO,CAAE,OAAO,CAChBC,SAAS,CAAE,QAAQ,CACnBC,SAAS,CAAE,QAAQ,CACnBC,SAAS,CAAE,MACb,CAAC,CACD,MAAO,CAAAL,MAAM,CAACZ,MAAM,CAAC,EAAI,SAAS,CACpC,CAAC,CAED,KAAM,CAAAkB,aAAa,CAAIlB,MAAM,EAAK,CAChC,KAAM,CAAAmB,KAAK,CAAG,CACZN,QAAQ,CAAE,KAAK,CACfC,OAAO,CAAE,KAAK,CACdC,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,KACb,CAAC,CACD,MAAO,CAAAE,KAAK,CAACnB,MAAM,CAAC,EAAIA,MAAM,CAChC,CAAC,CAED,KAAM,CAAAoB,WAAW,CAAIrB,IAAI,EAAK,CAC5B,KAAM,CAAAoB,KAAK,CAAG,CACZE,OAAO,CAAE,IAAI,CACbC,OAAO,CAAE,IAAI,CACbC,KAAK,CAAE,IAAI,CACXC,KAAK,CAAE,IAAI,CACXC,MAAM,CAAE,IAAI,CACZC,UAAU,CAAE,IAAI,CAChBC,OAAO,CAAE,IACX,CAAC,CACD,MAAO,CAAAR,KAAK,CAACpB,IAAI,CAAC,EAAIA,IAAI,CAC5B,CAAC,CAED,KAAM,CAAA6B,YAAY,CAAGA,CAAA,GAAM,CACzBrC,iBAAiB,CAAC,IAAI,CAAC,CACvBC,IAAI,CAACqC,WAAW,CAAC,CAAC,CAClBxC,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAAyC,UAAU,CAAIC,OAAO,EAAK,CAC9B,GAAIA,OAAO,CAACxB,QAAQ,CAAE,CACpB9C,OAAO,CAACuE,OAAO,CAAC,WAAW,CAAC,CAC5B,OACF,CACAzC,iBAAiB,CAACwC,OAAO,CAAC,CAC1BvC,IAAI,CAACyC,cAAc,CAACF,OAAO,CAAC,CAC5B1C,eAAe,CAAC,IAAI,CAAC,CACvB,CAAC,CAED,KAAM,CAAA6C,YAAY,CAAG,KAAO,CAAAvC,EAAE,EAAK,CACjC,GAAI,CACF,KAAM,CAAAoC,OAAO,CAAG/C,QAAQ,CAACmD,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACzC,EAAE,GAAKA,EAAE,CAAC,CAC/C,GAAIoC,OAAO,EAAIA,OAAO,CAACxB,QAAQ,CAAE,CAC/B9C,OAAO,CAACuE,OAAO,CAAC,WAAW,CAAC,CAC5B,OACF,CAEA;AACA,KAAM,CAAA1D,KAAK,CAAC+D,MAAM,kBAAAC,MAAA,CAAkB3C,EAAE,CAAE,CAAC,CAEzC;AACAV,WAAW,CAACD,QAAQ,CAACuD,MAAM,CAACH,CAAC,EAAIA,CAAC,CAACzC,EAAE,GAAKA,EAAE,CAAC,CAAC,CAC9ClC,OAAO,CAAC+E,OAAO,CAAC,QAAQ,CAAC,CAC3B,CAAE,MAAO9B,KAAK,CAAE,CACd+B,OAAO,CAAC/B,KAAK,CAAC,SAAS,CAAEA,KAAK,CAAC,CAC/BjD,OAAO,CAACiD,KAAK,CAAC,QAAQ,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAAgC,YAAY,CAAG,KAAO,CAAAC,MAAM,EAAK,CACrC,GAAI,CACF,GAAIrD,cAAc,CAAE,CAClB;AACA,KAAM,CAAAsD,eAAe,CAAG5D,QAAQ,CAAC6D,GAAG,CAACT,CAAC,EACpCA,CAAC,CAACzC,EAAE,GAAKL,cAAc,CAACK,EAAE,CAAAmD,aAAA,CAAAA,aAAA,IAAQV,CAAC,EAAKO,MAAM,EAAKP,CACrD,CAAC,CACDnD,WAAW,CAAC2D,eAAe,CAAC,CAC5BnF,OAAO,CAAC+E,OAAO,CAAC,QAAQ,CAAC,CAC3B,CAAC,IAAM,CACL;AACA,KAAM,CAAAO,UAAU,CAAAD,aAAA,CAAAA,aAAA,EACdnD,EAAE,CAAEqD,IAAI,CAACC,GAAG,CAAC,CAAC,EACXN,MAAM,MACT1C,SAAS,CAAE,CAAC,CACZC,YAAY,CAAE,CAAC,CACfC,cAAc,CAAE,CAAC,CACjBC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,GAAI,CAAA2C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACjD7C,SAAS,CAAE,GAAI,CAAA0C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAClD,CACDlE,WAAW,CAAC,CAAC,GAAGD,QAAQ,CAAE+D,UAAU,CAAC,CAAC,CACtCtF,OAAO,CAAC+E,OAAO,CAAC,QAAQ,CAAC,CAC3B,CACAnD,eAAe,CAAC,KAAK,CAAC,CACxB,CAAE,MAAOqB,KAAK,CAAE,CACdjD,OAAO,CAACiD,KAAK,CAAC,MAAM,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAA0C,eAAe,CAAG,KAAO,CAAArB,OAAO,EAAK,CACzC,GAAI,CACF,KAAM,CAAAgB,UAAU,CAAAD,aAAA,CAAAA,aAAA,IACXf,OAAO,MACVpC,EAAE,CAAEqD,IAAI,CAACC,GAAG,CAAC,CAAC,CACdrD,IAAI,IAAA0C,MAAA,CAAKP,OAAO,CAACnC,IAAI,mBAAO,CAC5BI,MAAM,CAAE,UAAU,CAClBI,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,GAAI,CAAA2C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACjD7C,SAAS,CAAE,GAAI,CAAA0C,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAClD,CACDlE,WAAW,CAAC,CAAC,GAAGD,QAAQ,CAAE+D,UAAU,CAAC,CAAC,CACtCtF,OAAO,CAAC+E,OAAO,CAAC,QAAQ,CAAC,CAC3B,CAAE,MAAO9B,KAAK,CAAE,CACdjD,OAAO,CAACiD,KAAK,CAAC,QAAQ,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAA2C,OAAO,CAAG,CACd,CACExD,KAAK,CAAE,MAAM,CACbyD,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,MAAM,CAAEA,CAACC,IAAI,CAAEC,MAAM,gBACnBhF,KAAA,QAAAiF,QAAA,eACEjF,KAAA,QAAKkF,KAAK,CAAE,CAAEC,UAAU,CAAE,MAAM,CAAEC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAM,CAAE,CAAAL,QAAA,EACnFF,IAAI,CACJC,MAAM,CAACnD,QAAQ,eACd/B,IAAA,CAACrB,GAAG,EAAC8G,KAAK,CAAC,MAAM,CAACC,IAAI,CAAC,OAAO,CAAAP,QAAA,CAAC,cAAE,CAAK,CACvC,EACE,CAAC,CACLD,MAAM,CAAC7D,KAAK,eACXrB,IAAA,QAAKoF,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAM,CAAEF,KAAK,CAAE,MAAO,CAAE,CAAAN,QAAA,CAAED,MAAM,CAAC7D,KAAK,CAAM,CACrE,EACE,CAET,CAAC,CACD,CACEA,KAAK,CAAE,IAAI,CACXyD,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QACP,CAAC,CACD,CACE1D,KAAK,CAAE,IAAI,CACXyD,SAAS,CAAE,MAAM,CACjBC,GAAG,CAAE,MAAM,CACXC,MAAM,CAAGzD,IAAI,eAAKvB,IAAA,CAACrB,GAAG,EAAAwG,QAAA,CAAEvC,WAAW,CAACrB,IAAI,CAAC,CAAM,CACjD,CAAC,CACD,CACEF,KAAK,CAAE,IAAI,CACXyD,SAAS,CAAE,QAAQ,CACnBC,GAAG,CAAE,QAAQ,CACbC,MAAM,CAAGxD,MAAM,eACbxB,IAAA,CAACrB,GAAG,EAAC8G,KAAK,CAAEtD,cAAc,CAACX,MAAM,CAAE,CAAA2D,QAAA,CAAEzC,aAAa,CAAClB,MAAM,CAAC,CAAM,CAEpE,CAAC,CACD,CACEH,KAAK,CAAE,IAAI,CACXyD,SAAS,CAAE,UAAU,CACrBC,GAAG,CAAE,UAAU,CACfC,MAAM,CAAGpD,QAAQ,eACf5B,IAAA,CAACpB,QAAQ,EACPgH,OAAO,CAAEhE,QAAS,CAClB8D,IAAI,CAAC,OAAO,CACZlE,MAAM,CAAEI,QAAQ,GAAK,GAAG,CAAG,SAAS,CAAG,QAAS,CACjD,CAEL,CAAC,CACD,CACEP,KAAK,CAAE,IAAI,CACX0D,GAAG,CAAE,OAAO,CACZC,MAAM,CAAEA,CAACa,CAAC,CAAEX,MAAM,gBAChBhF,KAAA,QAAKkF,KAAK,CAAE,CAAEO,QAAQ,CAAE,MAAO,CAAE,CAAAR,QAAA,eAC/BjF,KAAA,QAAAiF,QAAA,EAAK,gBAAI,CAAC,CAACD,MAAM,CAACzD,SAAS,CAAG,KAAK,EAAEqE,OAAO,CAAC,CAAC,CAAC,CAAC,QAAC,EAAK,CAAC,cACvD5F,KAAA,QAAAiF,QAAA,EAAK,gBAAI,CAACD,MAAM,CAACxD,YAAY,EAAM,CAAC,cACpCxB,KAAA,QAAAiF,QAAA,EAAK,gBAAI,CAACD,MAAM,CAACvD,cAAc,EAAM,CAAC,EACnC,CAET,CAAC,CACD,CACEN,KAAK,CAAE,MAAM,CACbyD,SAAS,CAAE,WAAW,CACtBC,GAAG,CAAE,WACP,CAAC,CACD,CACE1D,KAAK,CAAE,IAAI,CACX0D,GAAG,CAAE,SAAS,CACdC,MAAM,CAAEA,CAACa,CAAC,CAAEX,MAAM,gBAChBhF,KAAA,CAACxB,KAAK,EAAAyG,QAAA,eACJnF,IAAA,CAACvB,MAAM,EACL8C,IAAI,CAAC,MAAM,CACXwE,IAAI,cAAE/F,IAAA,CAACP,WAAW,GAAE,CAAE,CACtBuG,OAAO,CAAEA,CAAA,GAAMzF,QAAQ,cAAAuD,MAAA,CAAcoB,MAAM,CAAC/D,EAAE,CAAE,CAAE,CAAAgE,QAAA,CACnD,cAED,CAAQ,CAAC,cACTnF,IAAA,CAACvB,MAAM,EACL8C,IAAI,CAAC,MAAM,CACXwE,IAAI,cAAE/F,IAAA,CAACT,YAAY,GAAE,CAAE,CACvByG,OAAO,CAAEA,CAAA,GAAM1C,UAAU,CAAC4B,MAAM,CAAE,CAClCe,QAAQ,CAAEf,MAAM,CAACnD,QAAS,CAC1BV,KAAK,CAAE6D,MAAM,CAACnD,QAAQ,CAAG,WAAW,CAAG,EAAG,CAAAoD,QAAA,CAC3C,cAED,CAAQ,CAAC,cACTnF,IAAA,CAACvB,MAAM,EACL8C,IAAI,CAAC,MAAM,CACXwE,IAAI,cAAE/F,IAAA,CAACN,YAAY,GAAE,CAAE,CACvBsG,OAAO,CAAEA,CAAA,GAAMpB,eAAe,CAACM,MAAM,CAAE,CAAAC,QAAA,CACxC,cAED,CAAQ,CAAC,CACR,CAACD,MAAM,CAACnD,QAAQ,eACf/B,IAAA,CAACd,UAAU,EACTmC,KAAK,CAAC,oEAAa,CACnB6E,SAAS,CAAEA,CAAA,GAAMxC,YAAY,CAACwB,MAAM,CAAC/D,EAAE,CAAE,CACzCgF,MAAM,CAAC,cAAI,CACXC,UAAU,CAAC,cAAI,CAAAjB,QAAA,cAEfnF,IAAA,CAACvB,MAAM,EAAC8C,IAAI,CAAC,MAAM,CAAC8E,MAAM,MAACN,IAAI,cAAE/F,IAAA,CAACR,cAAc,GAAE,CAAE,CAAA2F,QAAA,CAAC,cAErD,CAAQ,CAAC,CACC,CACb,EACI,CAEX,CAAC,CACF,CAED,mBACEjF,KAAA,QAAKoG,SAAS,CAAC,SAAS,CAAAnB,QAAA,eACtBnF,IAAA,QAAKsG,SAAS,CAAC,aAAa,CAAAnB,QAAA,cAC1BnF,IAAA,CAACG,KAAK,EAACoG,KAAK,CAAE,CAAE,CAACD,SAAS,CAAC,YAAY,CAAAnB,QAAA,CAAC,0BAAI,CAAO,CAAC,CACjD,CAAC,cAENjF,KAAA,CAAC3B,IAAI,EAAA4G,QAAA,eACHjF,KAAA,QAAKoG,SAAS,CAAC,SAAS,CAAAnB,QAAA,eACtBnF,IAAA,QAAKsG,SAAS,CAAC,cAAc,CAAAnB,QAAA,cAC3BnF,IAAA,CAACvB,MAAM,EACL8C,IAAI,CAAC,SAAS,CACdwE,IAAI,cAAE/F,IAAA,CAACV,YAAY,GAAE,CAAE,CACvB0G,OAAO,CAAE5C,YAAa,CAAA+B,QAAA,CACvB,0BAED,CAAQ,CAAC,CACN,CAAC,cACNnF,IAAA,QAAKsG,SAAS,CAAC,eAAe,CAAAnB,QAAA,cAC5BjF,KAAA,CAACxB,KAAK,EAAAyG,QAAA,eACJnF,IAAA,CAACvB,MAAM,EAACsH,IAAI,cAAE/F,IAAA,CAACJ,cAAc,GAAE,CAAE,CAAAuF,QAAA,CAAC,0BAAI,CAAQ,CAAC,cAC/CnF,IAAA,CAACvB,MAAM,EAACsH,IAAI,cAAE/F,IAAA,CAACL,cAAc,GAAE,CAAE,CAAAwF,QAAA,CAAC,0BAAI,CAAQ,CAAC,EAC1C,CAAC,CACL,CAAC,EACH,CAAC,cAENnF,IAAA,CAACxB,KAAK,EACJqG,OAAO,CAAEA,OAAQ,CACjB2B,UAAU,CAAEhG,QAAS,CACrBiG,MAAM,CAAC,IAAI,CACX/F,OAAO,CAAEA,OAAQ,CACjBgG,UAAU,CAAE,CACVC,eAAe,CAAE,IAAI,CACrBC,eAAe,CAAE,IAAI,CACrBC,SAAS,CAAGC,KAAK,YAAAhD,MAAA,CAAUgD,KAAK,uBAClC,CAAE,CACH,CAAC,EACE,CAAC,cAEP9G,IAAA,CAACnB,KAAK,EACJwC,KAAK,CAAEP,cAAc,CAAG,MAAM,CAAG,MAAO,CACxCiG,IAAI,CAAEnG,YAAa,CACnBoG,QAAQ,CAAEA,CAAA,GAAMnG,eAAe,CAAC,KAAK,CAAE,CACvCoG,IAAI,CAAEA,CAAA,GAAMjG,IAAI,CAACkG,MAAM,CAAC,CAAE,CAC1BC,KAAK,CAAE,GAAI,CAAAhC,QAAA,cAEXjF,KAAA,CAACpB,IAAI,EACHkC,IAAI,CAAEA,IAAK,CACXoG,MAAM,CAAC,UAAU,CACjBC,QAAQ,CAAEnD,YAAa,CAAAiB,QAAA,eAEvBjF,KAAA,CAACd,GAAG,EAACkI,MAAM,CAAE,EAAG,CAAAnC,QAAA,eACdnF,IAAA,CAACX,GAAG,EAACkI,IAAI,CAAE,EAAG,CAAApC,QAAA,cACZnF,IAAA,CAAClB,IAAI,CAAC0I,IAAI,EACRpG,IAAI,CAAC,MAAM,CACXqG,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE1I,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAkG,QAAA,cAEhDnF,IAAA,CAACjB,KAAK,EAAC6I,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACN5H,IAAA,CAACX,GAAG,EAACkI,IAAI,CAAE,EAAG,CAAApC,QAAA,cACZnF,IAAA,CAAClB,IAAI,CAAC0I,IAAI,EACRpG,IAAI,CAAC,OAAO,CACZqG,KAAK,CAAC,0BAAM,CAAAtC,QAAA,cAEZnF,IAAA,CAACjB,KAAK,EAAC6I,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,EACH,CAAC,cAEN1H,KAAA,CAACd,GAAG,EAACkI,MAAM,CAAE,EAAG,CAAAnC,QAAA,eACdnF,IAAA,CAACX,GAAG,EAACkI,IAAI,CAAE,EAAG,CAAApC,QAAA,cACZnF,IAAA,CAAClB,IAAI,CAAC0I,IAAI,EACRpG,IAAI,CAAC,QAAQ,CACbqG,KAAK,CAAC,cAAI,CAAAtC,QAAA,cAEVnF,IAAA,CAACjB,KAAK,EAAC6I,WAAW,CAAC,4CAAS,CAAE,CAAC,CACtB,CAAC,CACT,CAAC,cACN5H,IAAA,CAACX,GAAG,EAACkI,IAAI,CAAE,EAAG,CAAApC,QAAA,cACZnF,IAAA,CAAClB,IAAI,CAAC0I,IAAI,EACRpG,IAAI,CAAC,MAAM,CACXqG,KAAK,CAAC,0BAAM,CACZC,KAAK,CAAE,CAAC,CAAEC,QAAQ,CAAE,IAAI,CAAE1I,OAAO,CAAE,SAAU,CAAC,CAAE,CAAAkG,QAAA,cAEhDjF,KAAA,CAAClB,MAAM,EAAC4I,WAAW,CAAC,4CAAS,CAAAzC,QAAA,eAC3BnF,IAAA,CAACI,MAAM,EAACyH,KAAK,CAAC,SAAS,CAAA1C,QAAA,CAAC,cAAE,CAAQ,CAAC,cACnCnF,IAAA,CAACI,MAAM,EAACyH,KAAK,CAAC,SAAS,CAAA1C,QAAA,CAAC,cAAE,CAAQ,CAAC,cACnCnF,IAAA,CAACI,MAAM,EAACyH,KAAK,CAAC,OAAO,CAAA1C,QAAA,CAAC,cAAE,CAAQ,CAAC,cACjCnF,IAAA,CAACI,MAAM,EAACyH,KAAK,CAAC,OAAO,CAAA1C,QAAA,CAAC,cAAE,CAAQ,CAAC,cACjCnF,IAAA,CAACI,MAAM,EAACyH,KAAK,CAAC,QAAQ,CAAA1C,QAAA,CAAC,cAAE,CAAQ,CAAC,cAClCnF,IAAA,CAACI,MAAM,EAACyH,KAAK,CAAC,YAAY,CAAA1C,QAAA,CAAC,cAAE,CAAQ,CAAC,cACtCnF,IAAA,CAACI,MAAM,EAACyH,KAAK,CAAC,SAAS,CAAA1C,QAAA,CAAC,cAAE,CAAQ,CAAC,EAC7B,CAAC,CACA,CAAC,CACT,CAAC,EACH,CAAC,cAENnF,IAAA,CAAClB,IAAI,CAAC0I,IAAI,EACRpG,IAAI,CAAC,SAAS,CACdqG,KAAK,CAAC,0BAAM,CAAAtC,QAAA,cAEZnF,IAAA,CAACK,QAAQ,EACPyH,IAAI,CAAE,CAAE,CACRF,WAAW,CAAC,4CAAS,CACtB,CAAC,CACO,CAAC,EACR,CAAC,CACF,CAAC,EACL,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}