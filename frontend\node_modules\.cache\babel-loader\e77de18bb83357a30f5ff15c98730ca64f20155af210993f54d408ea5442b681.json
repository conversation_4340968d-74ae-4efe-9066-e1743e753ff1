{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genBorderedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    lineType,\n    tableBorderColor,\n    tableHeaderBg,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    calc\n  } = token;\n  const tableBorder = \"\".concat(unit(lineWidth), \" \").concat(lineType, \" \").concat(tableBorderColor);\n  const getSizeBorderStyle = (size, paddingVertical, paddingHorizontal) => ({\n    [\"&\".concat(componentCls, \"-\").concat(size)]: {\n      [\"> \".concat(componentCls, \"-container\")]: {\n        [\"> \".concat(componentCls, \"-content, > \").concat(componentCls, \"-body\")]: {\n          [\"\\n            > table > tbody > tr > th,\\n            > table > tbody > tr > td\\n          \"]: {\n            [\"> \".concat(componentCls, \"-expanded-row-fixed\")]: {\n              margin: \"\".concat(unit(calc(paddingVertical).mul(-1).equal()), \"\\n              \").concat(unit(calc(calc(paddingHorizontal).add(lineWidth)).mul(-1).equal()))\n            }\n          }\n        }\n      }\n    }\n  });\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      [\"\".concat(componentCls).concat(componentCls, \"-bordered\")]: Object.assign(Object.assign(Object.assign({\n        // ============================ Title =============================\n        [\"> \".concat(componentCls, \"-title\")]: {\n          border: tableBorder,\n          borderBottom: 0\n        },\n        // ============================ Content ============================\n        [\"> \".concat(componentCls, \"-container\")]: {\n          borderInlineStart: tableBorder,\n          borderTop: tableBorder,\n          [\"\\n            > \".concat(componentCls, \"-content,\\n            > \").concat(componentCls, \"-header,\\n            > \").concat(componentCls, \"-body,\\n            > \").concat(componentCls, \"-summary\\n          \")]: {\n            '> table': {\n              // ============================= Cell =============================\n              [\"\\n                > thead > tr > th,\\n                > thead > tr > td,\\n                > tbody > tr > th,\\n                > tbody > tr > td,\\n                > tfoot > tr > th,\\n                > tfoot > tr > td\\n              \"]: {\n                borderInlineEnd: tableBorder\n              },\n              // ============================ Header ============================\n              '> thead': {\n                '> tr:not(:last-child) > th': {\n                  borderBottom: tableBorder\n                },\n                '> tr > th::before': {\n                  backgroundColor: 'transparent !important'\n                }\n              },\n              // Fixed right should provides additional border\n              [\"\\n                > thead > tr,\\n                > tbody > tr,\\n                > tfoot > tr\\n              \"]: {\n                [\"> \".concat(componentCls, \"-cell-fix-right-first::after\")]: {\n                  borderInlineEnd: tableBorder\n                }\n              },\n              // ========================== Expandable ==========================\n              [\"\\n                > tbody > tr > th,\\n                > tbody > tr > td\\n              \"]: {\n                [\"> \".concat(componentCls, \"-expanded-row-fixed\")]: {\n                  margin: \"\".concat(unit(calc(tablePaddingVertical).mul(-1).equal()), \" \").concat(unit(calc(calc(tablePaddingHorizontal).add(lineWidth)).mul(-1).equal())),\n                  '&::after': {\n                    position: 'absolute',\n                    top: 0,\n                    insetInlineEnd: lineWidth,\n                    bottom: 0,\n                    borderInlineEnd: tableBorder,\n                    content: '\"\"'\n                  }\n                }\n              }\n            }\n          }\n        },\n        // ============================ Scroll ============================\n        [\"&\".concat(componentCls, \"-scroll-horizontal\")]: {\n          [\"> \".concat(componentCls, \"-container > \").concat(componentCls, \"-body\")]: {\n            '> table > tbody': {\n              [\"\\n                > tr\".concat(componentCls, \"-expanded-row,\\n                > tr\").concat(componentCls, \"-placeholder\\n              \")]: {\n                '> th, > td': {\n                  borderInlineEnd: 0\n                }\n              }\n            }\n          }\n        }\n      }, getSizeBorderStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle)), getSizeBorderStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall)), {\n        // ============================ Footer ============================\n        [\"> \".concat(componentCls, \"-footer\")]: {\n          border: tableBorder,\n          borderTop: 0\n        }\n      }),\n      // ============================ Nested ============================\n      [\"\".concat(componentCls, \"-cell\")]: {\n        [\"\".concat(componentCls, \"-container:first-child\")]: {\n          // :first-child to avoid the case when bordered and title is set\n          borderTop: 0\n        },\n        // https://github.com/ant-design/ant-design/issues/35577\n        '&-scrollbar:not([rowspan])': {\n          boxShadow: \"0 \".concat(unit(lineWidth), \" 0 \").concat(unit(lineWidth), \" \").concat(tableHeaderBg)\n        }\n      },\n      [\"\".concat(componentCls, \"-bordered \").concat(componentCls, \"-cell-scrollbar\")]: {\n        borderInlineEnd: tableBorder\n      }\n    }\n  };\n};\nexport default genBorderedStyle;", "map": {"version": 3, "names": ["unit", "genBorderedStyle", "token", "componentCls", "lineWidth", "lineType", "tableBorderColor", "tableHeaderBg", "tablePaddingVertical", "tablePaddingHorizontal", "calc", "tableBorder", "concat", "getSizeBorderStyle", "size", "paddingVertical", "paddingHorizontal", "margin", "mul", "equal", "add", "Object", "assign", "border", "borderBottom", "borderInlineStart", "borderTop", "borderInlineEnd", "backgroundColor", "position", "top", "insetInlineEnd", "bottom", "content", "tablePaddingVerticalMiddle", "tablePaddingHorizontalMiddle", "tablePaddingVerticalSmall", "tablePaddingHorizontalSmall", "boxShadow"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/antd/es/table/style/bordered.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genBorderedStyle = token => {\n  const {\n    componentCls,\n    lineWidth,\n    lineType,\n    tableBorderColor,\n    tableHeaderBg,\n    tablePaddingVertical,\n    tablePaddingHorizontal,\n    calc\n  } = token;\n  const tableBorder = `${unit(lineWidth)} ${lineType} ${tableBorderColor}`;\n  const getSizeBorderStyle = (size, paddingVertical, paddingHorizontal) => ({\n    [`&${componentCls}-${size}`]: {\n      [`> ${componentCls}-container`]: {\n        [`> ${componentCls}-content, > ${componentCls}-body`]: {\n          [`\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          `]: {\n            [`> ${componentCls}-expanded-row-fixed`]: {\n              margin: `${unit(calc(paddingVertical).mul(-1).equal())}\n              ${unit(calc(calc(paddingHorizontal).add(lineWidth)).mul(-1).equal())}`\n            }\n          }\n        }\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}${componentCls}-bordered`]: Object.assign(Object.assign(Object.assign({\n        // ============================ Title =============================\n        [`> ${componentCls}-title`]: {\n          border: tableBorder,\n          borderBottom: 0\n        },\n        // ============================ Content ============================\n        [`> ${componentCls}-container`]: {\n          borderInlineStart: tableBorder,\n          borderTop: tableBorder,\n          [`\n            > ${componentCls}-content,\n            > ${componentCls}-header,\n            > ${componentCls}-body,\n            > ${componentCls}-summary\n          `]: {\n            '> table': {\n              // ============================= Cell =============================\n              [`\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              `]: {\n                borderInlineEnd: tableBorder\n              },\n              // ============================ Header ============================\n              '> thead': {\n                '> tr:not(:last-child) > th': {\n                  borderBottom: tableBorder\n                },\n                '> tr > th::before': {\n                  backgroundColor: 'transparent !important'\n                }\n              },\n              // Fixed right should provides additional border\n              [`\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              `]: {\n                [`> ${componentCls}-cell-fix-right-first::after`]: {\n                  borderInlineEnd: tableBorder\n                }\n              },\n              // ========================== Expandable ==========================\n              [`\n                > tbody > tr > th,\n                > tbody > tr > td\n              `]: {\n                [`> ${componentCls}-expanded-row-fixed`]: {\n                  margin: `${unit(calc(tablePaddingVertical).mul(-1).equal())} ${unit(calc(calc(tablePaddingHorizontal).add(lineWidth)).mul(-1).equal())}`,\n                  '&::after': {\n                    position: 'absolute',\n                    top: 0,\n                    insetInlineEnd: lineWidth,\n                    bottom: 0,\n                    borderInlineEnd: tableBorder,\n                    content: '\"\"'\n                  }\n                }\n              }\n            }\n          }\n        },\n        // ============================ Scroll ============================\n        [`&${componentCls}-scroll-horizontal`]: {\n          [`> ${componentCls}-container > ${componentCls}-body`]: {\n            '> table > tbody': {\n              [`\n                > tr${componentCls}-expanded-row,\n                > tr${componentCls}-placeholder\n              `]: {\n                '> th, > td': {\n                  borderInlineEnd: 0\n                }\n              }\n            }\n          }\n        }\n      }, getSizeBorderStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle)), getSizeBorderStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall)), {\n        // ============================ Footer ============================\n        [`> ${componentCls}-footer`]: {\n          border: tableBorder,\n          borderTop: 0\n        }\n      }),\n      // ============================ Nested ============================\n      [`${componentCls}-cell`]: {\n        [`${componentCls}-container:first-child`]: {\n          // :first-child to avoid the case when bordered and title is set\n          borderTop: 0\n        },\n        // https://github.com/ant-design/ant-design/issues/35577\n        '&-scrollbar:not([rowspan])': {\n          boxShadow: `0 ${unit(lineWidth)} 0 ${unit(lineWidth)} ${tableHeaderBg}`\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-cell-scrollbar`]: {\n        borderInlineEnd: tableBorder\n      }\n    }\n  };\n};\nexport default genBorderedStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAM;IACJC,YAAY;IACZC,SAAS;IACTC,QAAQ;IACRC,gBAAgB;IAChBC,aAAa;IACbC,oBAAoB;IACpBC,sBAAsB;IACtBC;EACF,CAAC,GAAGR,KAAK;EACT,MAAMS,WAAW,MAAAC,MAAA,CAAMZ,IAAI,CAACI,SAAS,CAAC,OAAAQ,MAAA,CAAIP,QAAQ,OAAAO,MAAA,CAAIN,gBAAgB,CAAE;EACxE,MAAMO,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,eAAe,EAAEC,iBAAiB,MAAM;IACxE,KAAAJ,MAAA,CAAKT,YAAY,OAAAS,MAAA,CAAIE,IAAI,IAAK;MAC5B,MAAAF,MAAA,CAAMT,YAAY,kBAAe;QAC/B,MAAAS,MAAA,CAAMT,YAAY,kBAAAS,MAAA,CAAeT,YAAY,aAAU;UACrD,iGAGI;YACF,MAAAS,MAAA,CAAMT,YAAY,2BAAwB;cACxCc,MAAM,KAAAL,MAAA,CAAKZ,IAAI,CAACU,IAAI,CAACK,eAAe,CAAC,CAACG,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,sBAAAP,MAAA,CACpDZ,IAAI,CAACU,IAAI,CAACA,IAAI,CAACM,iBAAiB,CAAC,CAACI,GAAG,CAAChB,SAAS,CAAC,CAAC,CAACc,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;YACtE;UACF;QACF;MACF;IACF;EACF,CAAC,CAAC;EACF,OAAO;IACL,IAAAP,MAAA,CAAIT,YAAY,gBAAa;MAC3B,IAAAS,MAAA,CAAIT,YAAY,EAAAS,MAAA,CAAGT,YAAY,iBAAckB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;QACrF;QACA,MAAAV,MAAA,CAAMT,YAAY,cAAW;UAC3BoB,MAAM,EAAEZ,WAAW;UACnBa,YAAY,EAAE;QAChB,CAAC;QACD;QACA,MAAAZ,MAAA,CAAMT,YAAY,kBAAe;UAC/BsB,iBAAiB,EAAEd,WAAW;UAC9Be,SAAS,EAAEf,WAAW;UACtB,oBAAAC,MAAA,CACMT,YAAY,+BAAAS,MAAA,CACZT,YAAY,8BAAAS,MAAA,CACZT,YAAY,4BAAAS,MAAA,CACZT,YAAY,4BACd;YACF,SAAS,EAAE;cACT;cACA,6OAOI;gBACFwB,eAAe,EAAEhB;cACnB,CAAC;cACD;cACA,SAAS,EAAE;gBACT,4BAA4B,EAAE;kBAC5Ba,YAAY,EAAEb;gBAChB,CAAC;gBACD,mBAAmB,EAAE;kBACnBiB,eAAe,EAAE;gBACnB;cACF,CAAC;cACD;cACA,kHAII;gBACF,MAAAhB,MAAA,CAAMT,YAAY,oCAAiC;kBACjDwB,eAAe,EAAEhB;gBACnB;cACF,CAAC;cACD;cACA,6FAGI;gBACF,MAAAC,MAAA,CAAMT,YAAY,2BAAwB;kBACxCc,MAAM,KAAAL,MAAA,CAAKZ,IAAI,CAACU,IAAI,CAACF,oBAAoB,CAAC,CAACU,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,OAAAP,MAAA,CAAIZ,IAAI,CAACU,IAAI,CAACA,IAAI,CAACD,sBAAsB,CAAC,CAACW,GAAG,CAAChB,SAAS,CAAC,CAAC,CAACc,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAE;kBACxI,UAAU,EAAE;oBACVU,QAAQ,EAAE,UAAU;oBACpBC,GAAG,EAAE,CAAC;oBACNC,cAAc,EAAE3B,SAAS;oBACzB4B,MAAM,EAAE,CAAC;oBACTL,eAAe,EAAEhB,WAAW;oBAC5BsB,OAAO,EAAE;kBACX;gBACF;cACF;YACF;UACF;QACF,CAAC;QACD;QACA,KAAArB,MAAA,CAAKT,YAAY,0BAAuB;UACtC,MAAAS,MAAA,CAAMT,YAAY,mBAAAS,MAAA,CAAgBT,YAAY,aAAU;YACtD,iBAAiB,EAAE;cACjB,0BAAAS,MAAA,CACQT,YAAY,0CAAAS,MAAA,CACZT,YAAY,oCAChB;gBACF,YAAY,EAAE;kBACZwB,eAAe,EAAE;gBACnB;cACF;YACF;UACF;QACF;MACF,CAAC,EAAEd,kBAAkB,CAAC,QAAQ,EAAEX,KAAK,CAACgC,0BAA0B,EAAEhC,KAAK,CAACiC,4BAA4B,CAAC,CAAC,EAAEtB,kBAAkB,CAAC,OAAO,EAAEX,KAAK,CAACkC,yBAAyB,EAAElC,KAAK,CAACmC,2BAA2B,CAAC,CAAC,EAAE;QACxM;QACA,MAAAzB,MAAA,CAAMT,YAAY,eAAY;UAC5BoB,MAAM,EAAEZ,WAAW;UACnBe,SAAS,EAAE;QACb;MACF,CAAC,CAAC;MACF;MACA,IAAAd,MAAA,CAAIT,YAAY,aAAU;QACxB,IAAAS,MAAA,CAAIT,YAAY,8BAA2B;UACzC;UACAuB,SAAS,EAAE;QACb,CAAC;QACD;QACA,4BAA4B,EAAE;UAC5BY,SAAS,OAAA1B,MAAA,CAAOZ,IAAI,CAACI,SAAS,CAAC,SAAAQ,MAAA,CAAMZ,IAAI,CAACI,SAAS,CAAC,OAAAQ,MAAA,CAAIL,aAAa;QACvE;MACF,CAAC;MACD,IAAAK,MAAA,CAAIT,YAAY,gBAAAS,MAAA,CAAaT,YAAY,uBAAoB;QAC3DwB,eAAe,EAAEhB;MACnB;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeV,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}