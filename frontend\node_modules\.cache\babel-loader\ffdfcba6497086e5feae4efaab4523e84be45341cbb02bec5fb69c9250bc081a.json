{"ast": null, "code": "\"use strict\";\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/esm/possibleConstructorReturn\";\nimport _isNativeReflectConstruct from \"@babel/runtime/helpers/esm/isNativeReflectConstruct\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/esm/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nfunction _callSuper(t, o, e) {\n  return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e));\n}\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { lazyStartIndex, lazyEndIndex, getPreClones } from \"./utils/innerSliderUtils\";\n\n// given specifications/props for a slide, fetch all the classes that need to be applied to the slide\nvar getSlideClasses = function getSlideClasses(spec) {\n  var slickActive, slickCenter, slickCloned;\n  var centerOffset, index;\n  if (spec.rtl) {\n    index = spec.slideCount - 1 - spec.index;\n  } else {\n    index = spec.index;\n  }\n  slickCloned = index < 0 || index >= spec.slideCount;\n  if (spec.centerMode) {\n    centerOffset = Math.floor(spec.slidesToShow / 2);\n    slickCenter = (index - spec.currentSlide) % spec.slideCount === 0;\n    if (index > spec.currentSlide - centerOffset - 1 && index <= spec.currentSlide + centerOffset) {\n      slickActive = true;\n    }\n  } else {\n    slickActive = spec.currentSlide <= index && index < spec.currentSlide + spec.slidesToShow;\n  }\n  var focusedSlide;\n  if (spec.targetSlide < 0) {\n    focusedSlide = spec.targetSlide + spec.slideCount;\n  } else if (spec.targetSlide >= spec.slideCount) {\n    focusedSlide = spec.targetSlide - spec.slideCount;\n  } else {\n    focusedSlide = spec.targetSlide;\n  }\n  var slickCurrent = index === focusedSlide;\n  return {\n    \"slick-slide\": true,\n    \"slick-active\": slickActive,\n    \"slick-center\": slickCenter,\n    \"slick-cloned\": slickCloned,\n    \"slick-current\": slickCurrent // dubious in case of RTL\n  };\n};\nvar getSlideStyle = function getSlideStyle(spec) {\n  var style = {};\n  if (spec.variableWidth === undefined || spec.variableWidth === false) {\n    style.width = spec.slideWidth;\n  }\n  if (spec.fade) {\n    style.position = \"relative\";\n    if (spec.vertical && spec.slideHeight) {\n      style.top = -spec.index * parseInt(spec.slideHeight);\n    } else {\n      style.left = -spec.index * parseInt(spec.slideWidth);\n    }\n    style.opacity = spec.currentSlide === spec.index ? 1 : 0;\n    style.zIndex = spec.currentSlide === spec.index ? 999 : 998;\n    if (spec.useCSS) {\n      style.transition = \"opacity \" + spec.speed + \"ms \" + spec.cssEase + \", \" + \"visibility \" + spec.speed + \"ms \" + spec.cssEase;\n    }\n  }\n  return style;\n};\nvar getKey = function getKey(child, fallbackKey) {\n  return child.key + \"-\" + fallbackKey;\n};\nvar renderSlides = function renderSlides(spec) {\n  var key;\n  var slides = [];\n  var preCloneSlides = [];\n  var postCloneSlides = [];\n  var childrenCount = React.Children.count(spec.children);\n  var startIndex = lazyStartIndex(spec);\n  var endIndex = lazyEndIndex(spec);\n  React.Children.forEach(spec.children, function (elem, index) {\n    var child;\n    var childOnClickOptions = {\n      message: \"children\",\n      index: index,\n      slidesToScroll: spec.slidesToScroll,\n      currentSlide: spec.currentSlide\n    };\n\n    // in case of lazyLoad, whether or not we want to fetch the slide\n    if (!spec.lazyLoad || spec.lazyLoad && spec.lazyLoadedList.indexOf(index) >= 0) {\n      child = elem;\n    } else {\n      child = /*#__PURE__*/React.createElement(\"div\", null);\n    }\n    var childStyle = getSlideStyle(_objectSpread(_objectSpread({}, spec), {}, {\n      index: index\n    }));\n    var slideClass = child.props.className || \"\";\n    var slideClasses = getSlideClasses(_objectSpread(_objectSpread({}, spec), {}, {\n      index: index\n    }));\n    // push a cloned element of the desired slide\n    slides.push(/*#__PURE__*/React.cloneElement(child, {\n      key: \"original\" + getKey(child, index),\n      \"data-index\": index,\n      className: classnames(slideClasses, slideClass),\n      tabIndex: \"-1\",\n      \"aria-hidden\": !slideClasses[\"slick-active\"],\n      style: _objectSpread(_objectSpread({\n        outline: \"none\"\n      }, child.props.style || {}), childStyle),\n      onClick: function onClick(e) {\n        child.props && child.props.onClick && child.props.onClick(e);\n        if (spec.focusOnSelect) {\n          spec.focusOnSelect(childOnClickOptions);\n        }\n      }\n    }));\n\n    // if slide needs to be precloned or postcloned\n    if (spec.infinite && childrenCount > 1 && spec.fade === false && !spec.unslick) {\n      var preCloneNo = childrenCount - index;\n      if (preCloneNo <= getPreClones(spec)) {\n        key = -preCloneNo;\n        if (key >= startIndex) {\n          child = elem;\n        }\n        slideClasses = getSlideClasses(_objectSpread(_objectSpread({}, spec), {}, {\n          index: key\n        }));\n        preCloneSlides.push(/*#__PURE__*/React.cloneElement(child, {\n          key: \"precloned\" + getKey(child, key),\n          \"data-index\": key,\n          tabIndex: \"-1\",\n          className: classnames(slideClasses, slideClass),\n          \"aria-hidden\": !slideClasses[\"slick-active\"],\n          style: _objectSpread(_objectSpread({}, child.props.style || {}), childStyle),\n          onClick: function onClick(e) {\n            child.props && child.props.onClick && child.props.onClick(e);\n            if (spec.focusOnSelect) {\n              spec.focusOnSelect(childOnClickOptions);\n            }\n          }\n        }));\n      }\n      key = childrenCount + index;\n      if (key < endIndex) {\n        child = elem;\n      }\n      slideClasses = getSlideClasses(_objectSpread(_objectSpread({}, spec), {}, {\n        index: key\n      }));\n      postCloneSlides.push(/*#__PURE__*/React.cloneElement(child, {\n        key: \"postcloned\" + getKey(child, key),\n        \"data-index\": key,\n        tabIndex: \"-1\",\n        className: classnames(slideClasses, slideClass),\n        \"aria-hidden\": !slideClasses[\"slick-active\"],\n        style: _objectSpread(_objectSpread({}, child.props.style || {}), childStyle),\n        onClick: function onClick(e) {\n          child.props && child.props.onClick && child.props.onClick(e);\n          if (spec.focusOnSelect) {\n            spec.focusOnSelect(childOnClickOptions);\n          }\n        }\n      }));\n    }\n  });\n  if (spec.rtl) {\n    return preCloneSlides.concat(slides, postCloneSlides).reverse();\n  } else {\n    return preCloneSlides.concat(slides, postCloneSlides);\n  }\n};\nexport var Track = /*#__PURE__*/function (_React$PureComponent) {\n  function Track() {\n    var _this;\n    _classCallCheck(this, Track);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Track, [].concat(args));\n    _defineProperty(_this, \"node\", null);\n    _defineProperty(_this, \"handleRef\", function (ref) {\n      _this.node = ref;\n    });\n    return _this;\n  }\n  _inherits(Track, _React$PureComponent);\n  return _createClass(Track, [{\n    key: \"render\",\n    value: function render() {\n      var slides = renderSlides(this.props);\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        onMouseOver = _this$props.onMouseOver,\n        onMouseLeave = _this$props.onMouseLeave;\n      var mouseEvents = {\n        onMouseEnter: onMouseEnter,\n        onMouseOver: onMouseOver,\n        onMouseLeave: onMouseLeave\n      };\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        ref: this.handleRef,\n        className: \"slick-track\",\n        style: this.props.trackStyle\n      }, mouseEvents), slides);\n    }\n  }]);\n}(React.PureComponent);", "map": {"version": 3, "names": ["_extends", "_classCallCheck", "_createClass", "_possibleConstructorReturn", "_isNativeReflectConstruct", "_getPrototypeOf", "_inherits", "_defineProperty", "_objectSpread", "_callSuper", "t", "o", "e", "Reflect", "construct", "constructor", "apply", "React", "classnames", "lazyStartIndex", "lazyEndIndex", "getPreClones", "getSlideClasses", "spec", "slickActive", "slickCenter", "slickCloned", "centerOffset", "index", "rtl", "slideCount", "centerMode", "Math", "floor", "slidesToShow", "currentSlide", "focusedSlide", "targetSlide", "<PERSON><PERSON><PERSON><PERSON>", "getSlideStyle", "style", "variableWidth", "undefined", "width", "slideWidth", "fade", "position", "vertical", "slideHeight", "top", "parseInt", "left", "opacity", "zIndex", "useCSS", "transition", "speed", "cssEase", "<PERSON><PERSON><PERSON>", "child", "fallback<PERSON><PERSON>", "key", "renderSlides", "slides", "preCloneSlides", "postCloneSlides", "childrenCount", "Children", "count", "children", "startIndex", "endIndex", "for<PERSON>ach", "elem", "childOnClickOptions", "message", "slidesToScroll", "lazyLoad", "lazyLoadedList", "indexOf", "createElement", "childStyle", "slideClass", "props", "className", "slideClasses", "push", "cloneElement", "tabIndex", "outline", "onClick", "focusOnSelect", "infinite", "unslick", "preCloneNo", "concat", "reverse", "Track", "_React$PureComponent", "_this", "_len", "arguments", "length", "args", "Array", "_key", "ref", "node", "value", "render", "_this$props", "onMouseEnter", "onMouseOver", "onMouseLeave", "mouseEvents", "handleRef", "trackStyle", "PureComponent"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/@ant-design/react-slick/es/track.js"], "sourcesContent": ["\"use strict\";\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/esm/possibleConstructorReturn\";\nimport _isNativeReflectConstruct from \"@babel/runtime/helpers/esm/isNativeReflectConstruct\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/esm/getPrototypeOf\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nimport React from \"react\";\nimport classnames from \"classnames\";\nimport { lazyStartIndex, lazyEndIndex, getPreClones } from \"./utils/innerSliderUtils\";\n\n// given specifications/props for a slide, fetch all the classes that need to be applied to the slide\nvar getSlideClasses = function getSlideClasses(spec) {\n  var slickActive, slickCenter, slickCloned;\n  var centerOffset, index;\n  if (spec.rtl) {\n    index = spec.slideCount - 1 - spec.index;\n  } else {\n    index = spec.index;\n  }\n  slickCloned = index < 0 || index >= spec.slideCount;\n  if (spec.centerMode) {\n    centerOffset = Math.floor(spec.slidesToShow / 2);\n    slickCenter = (index - spec.currentSlide) % spec.slideCount === 0;\n    if (index > spec.currentSlide - centerOffset - 1 && index <= spec.currentSlide + centerOffset) {\n      slickActive = true;\n    }\n  } else {\n    slickActive = spec.currentSlide <= index && index < spec.currentSlide + spec.slidesToShow;\n  }\n  var focusedSlide;\n  if (spec.targetSlide < 0) {\n    focusedSlide = spec.targetSlide + spec.slideCount;\n  } else if (spec.targetSlide >= spec.slideCount) {\n    focusedSlide = spec.targetSlide - spec.slideCount;\n  } else {\n    focusedSlide = spec.targetSlide;\n  }\n  var slickCurrent = index === focusedSlide;\n  return {\n    \"slick-slide\": true,\n    \"slick-active\": slickActive,\n    \"slick-center\": slickCenter,\n    \"slick-cloned\": slickCloned,\n    \"slick-current\": slickCurrent // dubious in case of RTL\n  };\n};\nvar getSlideStyle = function getSlideStyle(spec) {\n  var style = {};\n  if (spec.variableWidth === undefined || spec.variableWidth === false) {\n    style.width = spec.slideWidth;\n  }\n  if (spec.fade) {\n    style.position = \"relative\";\n    if (spec.vertical && spec.slideHeight) {\n      style.top = -spec.index * parseInt(spec.slideHeight);\n    } else {\n      style.left = -spec.index * parseInt(spec.slideWidth);\n    }\n    style.opacity = spec.currentSlide === spec.index ? 1 : 0;\n    style.zIndex = spec.currentSlide === spec.index ? 999 : 998;\n    if (spec.useCSS) {\n      style.transition = \"opacity \" + spec.speed + \"ms \" + spec.cssEase + \", \" + \"visibility \" + spec.speed + \"ms \" + spec.cssEase;\n    }\n  }\n  return style;\n};\nvar getKey = function getKey(child, fallbackKey) {\n  return child.key + \"-\" + fallbackKey;\n};\nvar renderSlides = function renderSlides(spec) {\n  var key;\n  var slides = [];\n  var preCloneSlides = [];\n  var postCloneSlides = [];\n  var childrenCount = React.Children.count(spec.children);\n  var startIndex = lazyStartIndex(spec);\n  var endIndex = lazyEndIndex(spec);\n  React.Children.forEach(spec.children, function (elem, index) {\n    var child;\n    var childOnClickOptions = {\n      message: \"children\",\n      index: index,\n      slidesToScroll: spec.slidesToScroll,\n      currentSlide: spec.currentSlide\n    };\n\n    // in case of lazyLoad, whether or not we want to fetch the slide\n    if (!spec.lazyLoad || spec.lazyLoad && spec.lazyLoadedList.indexOf(index) >= 0) {\n      child = elem;\n    } else {\n      child = /*#__PURE__*/React.createElement(\"div\", null);\n    }\n    var childStyle = getSlideStyle(_objectSpread(_objectSpread({}, spec), {}, {\n      index: index\n    }));\n    var slideClass = child.props.className || \"\";\n    var slideClasses = getSlideClasses(_objectSpread(_objectSpread({}, spec), {}, {\n      index: index\n    }));\n    // push a cloned element of the desired slide\n    slides.push( /*#__PURE__*/React.cloneElement(child, {\n      key: \"original\" + getKey(child, index),\n      \"data-index\": index,\n      className: classnames(slideClasses, slideClass),\n      tabIndex: \"-1\",\n      \"aria-hidden\": !slideClasses[\"slick-active\"],\n      style: _objectSpread(_objectSpread({\n        outline: \"none\"\n      }, child.props.style || {}), childStyle),\n      onClick: function onClick(e) {\n        child.props && child.props.onClick && child.props.onClick(e);\n        if (spec.focusOnSelect) {\n          spec.focusOnSelect(childOnClickOptions);\n        }\n      }\n    }));\n\n    // if slide needs to be precloned or postcloned\n    if (spec.infinite && childrenCount > 1 && spec.fade === false && !spec.unslick) {\n      var preCloneNo = childrenCount - index;\n      if (preCloneNo <= getPreClones(spec)) {\n        key = -preCloneNo;\n        if (key >= startIndex) {\n          child = elem;\n        }\n        slideClasses = getSlideClasses(_objectSpread(_objectSpread({}, spec), {}, {\n          index: key\n        }));\n        preCloneSlides.push( /*#__PURE__*/React.cloneElement(child, {\n          key: \"precloned\" + getKey(child, key),\n          \"data-index\": key,\n          tabIndex: \"-1\",\n          className: classnames(slideClasses, slideClass),\n          \"aria-hidden\": !slideClasses[\"slick-active\"],\n          style: _objectSpread(_objectSpread({}, child.props.style || {}), childStyle),\n          onClick: function onClick(e) {\n            child.props && child.props.onClick && child.props.onClick(e);\n            if (spec.focusOnSelect) {\n              spec.focusOnSelect(childOnClickOptions);\n            }\n          }\n        }));\n      }\n      key = childrenCount + index;\n      if (key < endIndex) {\n        child = elem;\n      }\n      slideClasses = getSlideClasses(_objectSpread(_objectSpread({}, spec), {}, {\n        index: key\n      }));\n      postCloneSlides.push( /*#__PURE__*/React.cloneElement(child, {\n        key: \"postcloned\" + getKey(child, key),\n        \"data-index\": key,\n        tabIndex: \"-1\",\n        className: classnames(slideClasses, slideClass),\n        \"aria-hidden\": !slideClasses[\"slick-active\"],\n        style: _objectSpread(_objectSpread({}, child.props.style || {}), childStyle),\n        onClick: function onClick(e) {\n          child.props && child.props.onClick && child.props.onClick(e);\n          if (spec.focusOnSelect) {\n            spec.focusOnSelect(childOnClickOptions);\n          }\n        }\n      }));\n    }\n  });\n  if (spec.rtl) {\n    return preCloneSlides.concat(slides, postCloneSlides).reverse();\n  } else {\n    return preCloneSlides.concat(slides, postCloneSlides);\n  }\n};\nexport var Track = /*#__PURE__*/function (_React$PureComponent) {\n  function Track() {\n    var _this;\n    _classCallCheck(this, Track);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _callSuper(this, Track, [].concat(args));\n    _defineProperty(_this, \"node\", null);\n    _defineProperty(_this, \"handleRef\", function (ref) {\n      _this.node = ref;\n    });\n    return _this;\n  }\n  _inherits(Track, _React$PureComponent);\n  return _createClass(Track, [{\n    key: \"render\",\n    value: function render() {\n      var slides = renderSlides(this.props);\n      var _this$props = this.props,\n        onMouseEnter = _this$props.onMouseEnter,\n        onMouseOver = _this$props.onMouseOver,\n        onMouseLeave = _this$props.onMouseLeave;\n      var mouseEvents = {\n        onMouseEnter: onMouseEnter,\n        onMouseOver: onMouseOver,\n        onMouseLeave: onMouseLeave\n      };\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        ref: this.handleRef,\n        className: \"slick-track\",\n        style: this.props.trackStyle\n      }, mouseEvents), slides);\n    }\n  }]);\n}(React.PureComponent);"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,0BAA0B,MAAM,sDAAsD;AAC7F,OAAOC,yBAAyB,MAAM,qDAAqD;AAC3F,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,SAASC,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,GAAGN,eAAe,CAACM,CAAC,CAAC,EAAER,0BAA0B,CAACO,CAAC,EAAEN,yBAAyB,CAAC,CAAC,GAAGS,OAAO,CAACC,SAAS,CAACH,CAAC,EAAEC,CAAC,IAAI,EAAE,EAAEP,eAAe,CAACK,CAAC,CAAC,CAACK,WAAW,CAAC,GAAGJ,CAAC,CAACK,KAAK,CAACN,CAAC,EAAEE,CAAC,CAAC,CAAC;AAAE;AAC1M,OAAOK,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,cAAc,EAAEC,YAAY,EAAEC,YAAY,QAAQ,0BAA0B;;AAErF;AACA,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAE;EACnD,IAAIC,WAAW,EAAEC,WAAW,EAAEC,WAAW;EACzC,IAAIC,YAAY,EAAEC,KAAK;EACvB,IAAIL,IAAI,CAACM,GAAG,EAAE;IACZD,KAAK,GAAGL,IAAI,CAACO,UAAU,GAAG,CAAC,GAAGP,IAAI,CAACK,KAAK;EAC1C,CAAC,MAAM;IACLA,KAAK,GAAGL,IAAI,CAACK,KAAK;EACpB;EACAF,WAAW,GAAGE,KAAK,GAAG,CAAC,IAAIA,KAAK,IAAIL,IAAI,CAACO,UAAU;EACnD,IAAIP,IAAI,CAACQ,UAAU,EAAE;IACnBJ,YAAY,GAAGK,IAAI,CAACC,KAAK,CAACV,IAAI,CAACW,YAAY,GAAG,CAAC,CAAC;IAChDT,WAAW,GAAG,CAACG,KAAK,GAAGL,IAAI,CAACY,YAAY,IAAIZ,IAAI,CAACO,UAAU,KAAK,CAAC;IACjE,IAAIF,KAAK,GAAGL,IAAI,CAACY,YAAY,GAAGR,YAAY,GAAG,CAAC,IAAIC,KAAK,IAAIL,IAAI,CAACY,YAAY,GAAGR,YAAY,EAAE;MAC7FH,WAAW,GAAG,IAAI;IACpB;EACF,CAAC,MAAM;IACLA,WAAW,GAAGD,IAAI,CAACY,YAAY,IAAIP,KAAK,IAAIA,KAAK,GAAGL,IAAI,CAACY,YAAY,GAAGZ,IAAI,CAACW,YAAY;EAC3F;EACA,IAAIE,YAAY;EAChB,IAAIb,IAAI,CAACc,WAAW,GAAG,CAAC,EAAE;IACxBD,YAAY,GAAGb,IAAI,CAACc,WAAW,GAAGd,IAAI,CAACO,UAAU;EACnD,CAAC,MAAM,IAAIP,IAAI,CAACc,WAAW,IAAId,IAAI,CAACO,UAAU,EAAE;IAC9CM,YAAY,GAAGb,IAAI,CAACc,WAAW,GAAGd,IAAI,CAACO,UAAU;EACnD,CAAC,MAAM;IACLM,YAAY,GAAGb,IAAI,CAACc,WAAW;EACjC;EACA,IAAIC,YAAY,GAAGV,KAAK,KAAKQ,YAAY;EACzC,OAAO;IACL,aAAa,EAAE,IAAI;IACnB,cAAc,EAAEZ,WAAW;IAC3B,cAAc,EAAEC,WAAW;IAC3B,cAAc,EAAEC,WAAW;IAC3B,eAAe,EAAEY,YAAY,CAAC;EAChC,CAAC;AACH,CAAC;AACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAAChB,IAAI,EAAE;EAC/C,IAAIiB,KAAK,GAAG,CAAC,CAAC;EACd,IAAIjB,IAAI,CAACkB,aAAa,KAAKC,SAAS,IAAInB,IAAI,CAACkB,aAAa,KAAK,KAAK,EAAE;IACpED,KAAK,CAACG,KAAK,GAAGpB,IAAI,CAACqB,UAAU;EAC/B;EACA,IAAIrB,IAAI,CAACsB,IAAI,EAAE;IACbL,KAAK,CAACM,QAAQ,GAAG,UAAU;IAC3B,IAAIvB,IAAI,CAACwB,QAAQ,IAAIxB,IAAI,CAACyB,WAAW,EAAE;MACrCR,KAAK,CAACS,GAAG,GAAG,CAAC1B,IAAI,CAACK,KAAK,GAAGsB,QAAQ,CAAC3B,IAAI,CAACyB,WAAW,CAAC;IACtD,CAAC,MAAM;MACLR,KAAK,CAACW,IAAI,GAAG,CAAC5B,IAAI,CAACK,KAAK,GAAGsB,QAAQ,CAAC3B,IAAI,CAACqB,UAAU,CAAC;IACtD;IACAJ,KAAK,CAACY,OAAO,GAAG7B,IAAI,CAACY,YAAY,KAAKZ,IAAI,CAACK,KAAK,GAAG,CAAC,GAAG,CAAC;IACxDY,KAAK,CAACa,MAAM,GAAG9B,IAAI,CAACY,YAAY,KAAKZ,IAAI,CAACK,KAAK,GAAG,GAAG,GAAG,GAAG;IAC3D,IAAIL,IAAI,CAAC+B,MAAM,EAAE;MACfd,KAAK,CAACe,UAAU,GAAG,UAAU,GAAGhC,IAAI,CAACiC,KAAK,GAAG,KAAK,GAAGjC,IAAI,CAACkC,OAAO,GAAG,IAAI,GAAG,aAAa,GAAGlC,IAAI,CAACiC,KAAK,GAAG,KAAK,GAAGjC,IAAI,CAACkC,OAAO;IAC9H;EACF;EACA,OAAOjB,KAAK;AACd,CAAC;AACD,IAAIkB,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAC/C,OAAOD,KAAK,CAACE,GAAG,GAAG,GAAG,GAAGD,WAAW;AACtC,CAAC;AACD,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACvC,IAAI,EAAE;EAC7C,IAAIsC,GAAG;EACP,IAAIE,MAAM,GAAG,EAAE;EACf,IAAIC,cAAc,GAAG,EAAE;EACvB,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIC,aAAa,GAAGjD,KAAK,CAACkD,QAAQ,CAACC,KAAK,CAAC7C,IAAI,CAAC8C,QAAQ,CAAC;EACvD,IAAIC,UAAU,GAAGnD,cAAc,CAACI,IAAI,CAAC;EACrC,IAAIgD,QAAQ,GAAGnD,YAAY,CAACG,IAAI,CAAC;EACjCN,KAAK,CAACkD,QAAQ,CAACK,OAAO,CAACjD,IAAI,CAAC8C,QAAQ,EAAE,UAAUI,IAAI,EAAE7C,KAAK,EAAE;IAC3D,IAAI+B,KAAK;IACT,IAAIe,mBAAmB,GAAG;MACxBC,OAAO,EAAE,UAAU;MACnB/C,KAAK,EAAEA,KAAK;MACZgD,cAAc,EAAErD,IAAI,CAACqD,cAAc;MACnCzC,YAAY,EAAEZ,IAAI,CAACY;IACrB,CAAC;;IAED;IACA,IAAI,CAACZ,IAAI,CAACsD,QAAQ,IAAItD,IAAI,CAACsD,QAAQ,IAAItD,IAAI,CAACuD,cAAc,CAACC,OAAO,CAACnD,KAAK,CAAC,IAAI,CAAC,EAAE;MAC9E+B,KAAK,GAAGc,IAAI;IACd,CAAC,MAAM;MACLd,KAAK,GAAG,aAAa1C,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC;IACvD;IACA,IAAIC,UAAU,GAAG1C,aAAa,CAAC/B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MACxEK,KAAK,EAAEA;IACT,CAAC,CAAC,CAAC;IACH,IAAIsD,UAAU,GAAGvB,KAAK,CAACwB,KAAK,CAACC,SAAS,IAAI,EAAE;IAC5C,IAAIC,YAAY,GAAG/D,eAAe,CAACd,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5EK,KAAK,EAAEA;IACT,CAAC,CAAC,CAAC;IACH;IACAmC,MAAM,CAACuB,IAAI,CAAE,aAAarE,KAAK,CAACsE,YAAY,CAAC5B,KAAK,EAAE;MAClDE,GAAG,EAAE,UAAU,GAAGH,MAAM,CAACC,KAAK,EAAE/B,KAAK,CAAC;MACtC,YAAY,EAAEA,KAAK;MACnBwD,SAAS,EAAElE,UAAU,CAACmE,YAAY,EAAEH,UAAU,CAAC;MAC/CM,QAAQ,EAAE,IAAI;MACd,aAAa,EAAE,CAACH,YAAY,CAAC,cAAc,CAAC;MAC5C7C,KAAK,EAAEhC,aAAa,CAACA,aAAa,CAAC;QACjCiF,OAAO,EAAE;MACX,CAAC,EAAE9B,KAAK,CAACwB,KAAK,CAAC3C,KAAK,IAAI,CAAC,CAAC,CAAC,EAAEyC,UAAU,CAAC;MACxCS,OAAO,EAAE,SAASA,OAAOA,CAAC9E,CAAC,EAAE;QAC3B+C,KAAK,CAACwB,KAAK,IAAIxB,KAAK,CAACwB,KAAK,CAACO,OAAO,IAAI/B,KAAK,CAACwB,KAAK,CAACO,OAAO,CAAC9E,CAAC,CAAC;QAC5D,IAAIW,IAAI,CAACoE,aAAa,EAAE;UACtBpE,IAAI,CAACoE,aAAa,CAACjB,mBAAmB,CAAC;QACzC;MACF;IACF,CAAC,CAAC,CAAC;;IAEH;IACA,IAAInD,IAAI,CAACqE,QAAQ,IAAI1B,aAAa,GAAG,CAAC,IAAI3C,IAAI,CAACsB,IAAI,KAAK,KAAK,IAAI,CAACtB,IAAI,CAACsE,OAAO,EAAE;MAC9E,IAAIC,UAAU,GAAG5B,aAAa,GAAGtC,KAAK;MACtC,IAAIkE,UAAU,IAAIzE,YAAY,CAACE,IAAI,CAAC,EAAE;QACpCsC,GAAG,GAAG,CAACiC,UAAU;QACjB,IAAIjC,GAAG,IAAIS,UAAU,EAAE;UACrBX,KAAK,GAAGc,IAAI;QACd;QACAY,YAAY,GAAG/D,eAAe,CAACd,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UACxEK,KAAK,EAAEiC;QACT,CAAC,CAAC,CAAC;QACHG,cAAc,CAACsB,IAAI,CAAE,aAAarE,KAAK,CAACsE,YAAY,CAAC5B,KAAK,EAAE;UAC1DE,GAAG,EAAE,WAAW,GAAGH,MAAM,CAACC,KAAK,EAAEE,GAAG,CAAC;UACrC,YAAY,EAAEA,GAAG;UACjB2B,QAAQ,EAAE,IAAI;UACdJ,SAAS,EAAElE,UAAU,CAACmE,YAAY,EAAEH,UAAU,CAAC;UAC/C,aAAa,EAAE,CAACG,YAAY,CAAC,cAAc,CAAC;UAC5C7C,KAAK,EAAEhC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmD,KAAK,CAACwB,KAAK,CAAC3C,KAAK,IAAI,CAAC,CAAC,CAAC,EAAEyC,UAAU,CAAC;UAC5ES,OAAO,EAAE,SAASA,OAAOA,CAAC9E,CAAC,EAAE;YAC3B+C,KAAK,CAACwB,KAAK,IAAIxB,KAAK,CAACwB,KAAK,CAACO,OAAO,IAAI/B,KAAK,CAACwB,KAAK,CAACO,OAAO,CAAC9E,CAAC,CAAC;YAC5D,IAAIW,IAAI,CAACoE,aAAa,EAAE;cACtBpE,IAAI,CAACoE,aAAa,CAACjB,mBAAmB,CAAC;YACzC;UACF;QACF,CAAC,CAAC,CAAC;MACL;MACAb,GAAG,GAAGK,aAAa,GAAGtC,KAAK;MAC3B,IAAIiC,GAAG,GAAGU,QAAQ,EAAE;QAClBZ,KAAK,GAAGc,IAAI;MACd;MACAY,YAAY,GAAG/D,eAAe,CAACd,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;QACxEK,KAAK,EAAEiC;MACT,CAAC,CAAC,CAAC;MACHI,eAAe,CAACqB,IAAI,CAAE,aAAarE,KAAK,CAACsE,YAAY,CAAC5B,KAAK,EAAE;QAC3DE,GAAG,EAAE,YAAY,GAAGH,MAAM,CAACC,KAAK,EAAEE,GAAG,CAAC;QACtC,YAAY,EAAEA,GAAG;QACjB2B,QAAQ,EAAE,IAAI;QACdJ,SAAS,EAAElE,UAAU,CAACmE,YAAY,EAAEH,UAAU,CAAC;QAC/C,aAAa,EAAE,CAACG,YAAY,CAAC,cAAc,CAAC;QAC5C7C,KAAK,EAAEhC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmD,KAAK,CAACwB,KAAK,CAAC3C,KAAK,IAAI,CAAC,CAAC,CAAC,EAAEyC,UAAU,CAAC;QAC5ES,OAAO,EAAE,SAASA,OAAOA,CAAC9E,CAAC,EAAE;UAC3B+C,KAAK,CAACwB,KAAK,IAAIxB,KAAK,CAACwB,KAAK,CAACO,OAAO,IAAI/B,KAAK,CAACwB,KAAK,CAACO,OAAO,CAAC9E,CAAC,CAAC;UAC5D,IAAIW,IAAI,CAACoE,aAAa,EAAE;YACtBpE,IAAI,CAACoE,aAAa,CAACjB,mBAAmB,CAAC;UACzC;QACF;MACF,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC;EACF,IAAInD,IAAI,CAACM,GAAG,EAAE;IACZ,OAAOmC,cAAc,CAAC+B,MAAM,CAAChC,MAAM,EAAEE,eAAe,CAAC,CAAC+B,OAAO,CAAC,CAAC;EACjE,CAAC,MAAM;IACL,OAAOhC,cAAc,CAAC+B,MAAM,CAAChC,MAAM,EAAEE,eAAe,CAAC;EACvD;AACF,CAAC;AACD,OAAO,IAAIgC,KAAK,GAAG,aAAa,UAAUC,oBAAoB,EAAE;EAC9D,SAASD,KAAKA,CAAA,EAAG;IACf,IAAIE,KAAK;IACTlG,eAAe,CAAC,IAAI,EAAEgG,KAAK,CAAC;IAC5B,KAAK,IAAIG,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAG1F,UAAU,CAAC,IAAI,EAAEwF,KAAK,EAAE,EAAE,CAACF,MAAM,CAACQ,IAAI,CAAC,CAAC;IAChDhG,eAAe,CAAC4F,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC;IACpC5F,eAAe,CAAC4F,KAAK,EAAE,WAAW,EAAE,UAAUO,GAAG,EAAE;MACjDP,KAAK,CAACQ,IAAI,GAAGD,GAAG;IAClB,CAAC,CAAC;IACF,OAAOP,KAAK;EACd;EACA7F,SAAS,CAAC2F,KAAK,EAAEC,oBAAoB,CAAC;EACtC,OAAOhG,YAAY,CAAC+F,KAAK,EAAE,CAAC;IAC1BpC,GAAG,EAAE,QAAQ;IACb+C,KAAK,EAAE,SAASC,MAAMA,CAAA,EAAG;MACvB,IAAI9C,MAAM,GAAGD,YAAY,CAAC,IAAI,CAACqB,KAAK,CAAC;MACrC,IAAI2B,WAAW,GAAG,IAAI,CAAC3B,KAAK;QAC1B4B,YAAY,GAAGD,WAAW,CAACC,YAAY;QACvCC,WAAW,GAAGF,WAAW,CAACE,WAAW;QACrCC,YAAY,GAAGH,WAAW,CAACG,YAAY;MACzC,IAAIC,WAAW,GAAG;QAChBH,YAAY,EAAEA,YAAY;QAC1BC,WAAW,EAAEA,WAAW;QACxBC,YAAY,EAAEA;MAChB,CAAC;MACD,OAAO,aAAahG,KAAK,CAAC+D,aAAa,CAAC,KAAK,EAAEhF,QAAQ,CAAC;QACtD0G,GAAG,EAAE,IAAI,CAACS,SAAS;QACnB/B,SAAS,EAAE,aAAa;QACxB5C,KAAK,EAAE,IAAI,CAAC2C,KAAK,CAACiC;MACpB,CAAC,EAAEF,WAAW,CAAC,EAAEnD,MAAM,CAAC;IAC1B;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC9C,KAAK,CAACoG,aAAa,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}