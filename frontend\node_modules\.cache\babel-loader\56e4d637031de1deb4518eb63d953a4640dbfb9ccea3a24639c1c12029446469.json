{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport getScrollBarSize from \"rc-util/es/getScrollBarSize\";\nimport * as React from 'react';\nimport TableContext from \"./context/TableContext\";\nimport { useLayoutState } from \"./hooks/useFrame\";\nimport raf from \"rc-util/es/raf\";\nimport { getOffset } from \"./utils/offsetUtil\";\nimport { getDOM } from \"rc-util/es/Dom/findDOMNode\";\nvar StickyScrollBar = function StickyScrollBar(_ref, ref) {\n  var _scrollBodyRef$curren, _scrollBodyRef$curren2;\n  var scrollBodyRef = _ref.scrollBodyRef,\n    onScroll = _ref.onScroll,\n    offsetScroll = _ref.offsetScroll,\n    container = _ref.container,\n    direction = _ref.direction;\n  var prefixCls = useContext(TableContext, 'prefixCls');\n  var bodyScrollWidth = ((_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 ? void 0 : _scrollBodyRef$curren.scrollWidth) || 0;\n  var bodyWidth = ((_scrollBodyRef$curren2 = scrollBodyRef.current) === null || _scrollBodyRef$curren2 === void 0 ? void 0 : _scrollBodyRef$curren2.clientWidth) || 0;\n  var scrollBarWidth = bodyScrollWidth && bodyWidth * (bodyWidth / bodyScrollWidth);\n  var scrollBarRef = React.useRef();\n  var _useLayoutState = useLayoutState({\n      scrollLeft: 0,\n      isHiddenScrollBar: true\n    }),\n    _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n    scrollState = _useLayoutState2[0],\n    setScrollState = _useLayoutState2[1];\n  var refState = React.useRef({\n    delta: 0,\n    x: 0\n  });\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isActive = _React$useState2[0],\n    setActive = _React$useState2[1];\n  var rafRef = React.useRef(null);\n  React.useEffect(function () {\n    return function () {\n      raf.cancel(rafRef.current);\n    };\n  }, []);\n  var onMouseUp = function onMouseUp() {\n    setActive(false);\n  };\n  var onMouseDown = function onMouseDown(event) {\n    event.persist();\n    refState.current.delta = event.pageX - scrollState.scrollLeft;\n    refState.current.x = 0;\n    setActive(true);\n    event.preventDefault();\n  };\n  var onMouseMove = function onMouseMove(event) {\n    var _window;\n    // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons\n    var _ref2 = event || ((_window = window) === null || _window === void 0 ? void 0 : _window.event),\n      buttons = _ref2.buttons;\n    if (!isActive || buttons === 0) {\n      // If out body mouse up, we can set isActive false when mouse move\n      if (isActive) {\n        setActive(false);\n      }\n      return;\n    }\n    var left = refState.current.x + event.pageX - refState.current.x - refState.current.delta;\n    var isRTL = direction === 'rtl';\n    // Limit scroll range\n    left = Math.max(isRTL ? scrollBarWidth - bodyWidth : 0, Math.min(isRTL ? 0 : bodyWidth - scrollBarWidth, left));\n    // Calculate the scroll position and update\n    var shouldScroll = !isRTL || Math.abs(left) + Math.abs(scrollBarWidth) < bodyWidth;\n    if (shouldScroll) {\n      onScroll({\n        scrollLeft: left / bodyWidth * (bodyScrollWidth + 2)\n      });\n      refState.current.x = event.pageX;\n    }\n  };\n  var checkScrollBarVisible = function checkScrollBarVisible() {\n    raf.cancel(rafRef.current);\n    rafRef.current = raf(function () {\n      if (!scrollBodyRef.current) {\n        return;\n      }\n      var tableOffsetTop = getOffset(scrollBodyRef.current).top;\n      var tableBottomOffset = tableOffsetTop + scrollBodyRef.current.offsetHeight;\n      var currentClientOffset = container === window ? document.documentElement.scrollTop + window.innerHeight : getOffset(container).top + container.clientHeight;\n      if (tableBottomOffset - getScrollBarSize() <= currentClientOffset || tableOffsetTop >= currentClientOffset - offsetScroll) {\n        setScrollState(function (state) {\n          return _objectSpread(_objectSpread({}, state), {}, {\n            isHiddenScrollBar: true\n          });\n        });\n      } else {\n        setScrollState(function (state) {\n          return _objectSpread(_objectSpread({}, state), {}, {\n            isHiddenScrollBar: false\n          });\n        });\n      }\n    });\n  };\n  var setScrollLeft = function setScrollLeft(left) {\n    setScrollState(function (state) {\n      return _objectSpread(_objectSpread({}, state), {}, {\n        scrollLeft: left / bodyScrollWidth * bodyWidth || 0\n      });\n    });\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      setScrollLeft: setScrollLeft,\n      checkScrollBarVisible: checkScrollBarVisible\n    };\n  });\n  React.useEffect(function () {\n    var onMouseUpListener = addEventListener(document.body, 'mouseup', onMouseUp, false);\n    var onMouseMoveListener = addEventListener(document.body, 'mousemove', onMouseMove, false);\n    checkScrollBarVisible();\n    return function () {\n      onMouseUpListener.remove();\n      onMouseMoveListener.remove();\n    };\n  }, [scrollBarWidth, isActive]);\n\n  // Loop for scroll event check\n  React.useEffect(function () {\n    if (!scrollBodyRef.current) return;\n    var scrollParents = [];\n    var parent = getDOM(scrollBodyRef.current);\n    while (parent) {\n      scrollParents.push(parent);\n      parent = parent.parentElement;\n    }\n    scrollParents.forEach(function (p) {\n      return p.addEventListener('scroll', checkScrollBarVisible, false);\n    });\n    window.addEventListener('resize', checkScrollBarVisible, false);\n    window.addEventListener('scroll', checkScrollBarVisible, false);\n    container.addEventListener('scroll', checkScrollBarVisible, false);\n    return function () {\n      scrollParents.forEach(function (p) {\n        return p.removeEventListener('scroll', checkScrollBarVisible);\n      });\n      window.removeEventListener('resize', checkScrollBarVisible);\n      window.removeEventListener('scroll', checkScrollBarVisible);\n      container.removeEventListener('scroll', checkScrollBarVisible);\n    };\n  }, [container]);\n  React.useEffect(function () {\n    if (!scrollState.isHiddenScrollBar) {\n      setScrollState(function (state) {\n        var bodyNode = scrollBodyRef.current;\n        if (!bodyNode) {\n          return state;\n        }\n        return _objectSpread(_objectSpread({}, state), {}, {\n          scrollLeft: bodyNode.scrollLeft / bodyNode.scrollWidth * bodyNode.clientWidth\n        });\n      });\n    }\n  }, [scrollState.isHiddenScrollBar]);\n  if (bodyScrollWidth <= bodyWidth || !scrollBarWidth || scrollState.isHiddenScrollBar) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: getScrollBarSize(),\n      width: bodyWidth,\n      bottom: offsetScroll\n    },\n    className: \"\".concat(prefixCls, \"-sticky-scroll\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onMouseDown,\n    ref: scrollBarRef,\n    className: classNames(\"\".concat(prefixCls, \"-sticky-scroll-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-sticky-scroll-bar-active\"), isActive)),\n    style: {\n      width: \"\".concat(scrollBarWidth, \"px\"),\n      transform: \"translate3d(\".concat(scrollState.scrollLeft, \"px, 0, 0)\")\n    }\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(StickyScrollBar);", "map": {"version": 3, "names": ["_defineProperty", "_objectSpread", "_slicedToArray", "useContext", "classNames", "addEventListener", "getScrollBarSize", "React", "TableContext", "useLayoutState", "raf", "getOffset", "getDOM", "StickyScrollBar", "_ref", "ref", "_scrollBodyRef$curren", "_scrollBodyRef$curren2", "scrollBodyRef", "onScroll", "offsetScroll", "container", "direction", "prefixCls", "bodyScrollWidth", "current", "scrollWidth", "bodyWidth", "clientWidth", "scrollBarWidth", "scrollBarRef", "useRef", "_useLayoutState", "scrollLeft", "isHiddenScrollBar", "_useLayoutState2", "scrollState", "setScrollState", "refState", "delta", "x", "_React$useState", "useState", "_React$useState2", "isActive", "setActive", "rafRef", "useEffect", "cancel", "onMouseUp", "onMouseDown", "event", "persist", "pageX", "preventDefault", "onMouseMove", "_window", "_ref2", "window", "buttons", "left", "isRTL", "Math", "max", "min", "shouldScroll", "abs", "checkScrollBarVisible", "tableOffsetTop", "top", "tableBottomOffset", "offsetHeight", "currentClientOffset", "document", "documentElement", "scrollTop", "innerHeight", "clientHeight", "state", "setScrollLeft", "useImperativeHandle", "onMouseUpListener", "body", "onMouseMoveListener", "remove", "scrollParents", "parent", "push", "parentElement", "for<PERSON>ach", "p", "removeEventListener", "bodyNode", "createElement", "style", "height", "width", "bottom", "className", "concat", "transform", "forwardRef"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/rc-table/es/stickyScrollBar.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport { useContext } from '@rc-component/context';\nimport classNames from 'classnames';\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport getScrollBarSize from \"rc-util/es/getScrollBarSize\";\nimport * as React from 'react';\nimport TableContext from \"./context/TableContext\";\nimport { useLayoutState } from \"./hooks/useFrame\";\nimport raf from \"rc-util/es/raf\";\nimport { getOffset } from \"./utils/offsetUtil\";\nimport { getDOM } from \"rc-util/es/Dom/findDOMNode\";\nvar StickyScrollBar = function StickyScrollBar(_ref, ref) {\n  var _scrollBodyRef$curren, _scrollBodyRef$curren2;\n  var scrollBodyRef = _ref.scrollBodyRef,\n    onScroll = _ref.onScroll,\n    offsetScroll = _ref.offsetScroll,\n    container = _ref.container,\n    direction = _ref.direction;\n  var prefixCls = useContext(TableContext, 'prefixCls');\n  var bodyScrollWidth = ((_scrollBodyRef$curren = scrollBodyRef.current) === null || _scrollBodyRef$curren === void 0 ? void 0 : _scrollBodyRef$curren.scrollWidth) || 0;\n  var bodyWidth = ((_scrollBodyRef$curren2 = scrollBodyRef.current) === null || _scrollBodyRef$curren2 === void 0 ? void 0 : _scrollBodyRef$curren2.clientWidth) || 0;\n  var scrollBarWidth = bodyScrollWidth && bodyWidth * (bodyWidth / bodyScrollWidth);\n  var scrollBarRef = React.useRef();\n  var _useLayoutState = useLayoutState({\n      scrollLeft: 0,\n      isHiddenScrollBar: true\n    }),\n    _useLayoutState2 = _slicedToArray(_useLayoutState, 2),\n    scrollState = _useLayoutState2[0],\n    setScrollState = _useLayoutState2[1];\n  var refState = React.useRef({\n    delta: 0,\n    x: 0\n  });\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    isActive = _React$useState2[0],\n    setActive = _React$useState2[1];\n  var rafRef = React.useRef(null);\n  React.useEffect(function () {\n    return function () {\n      raf.cancel(rafRef.current);\n    };\n  }, []);\n  var onMouseUp = function onMouseUp() {\n    setActive(false);\n  };\n  var onMouseDown = function onMouseDown(event) {\n    event.persist();\n    refState.current.delta = event.pageX - scrollState.scrollLeft;\n    refState.current.x = 0;\n    setActive(true);\n    event.preventDefault();\n  };\n  var onMouseMove = function onMouseMove(event) {\n    var _window;\n    // https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/buttons\n    var _ref2 = event || ((_window = window) === null || _window === void 0 ? void 0 : _window.event),\n      buttons = _ref2.buttons;\n    if (!isActive || buttons === 0) {\n      // If out body mouse up, we can set isActive false when mouse move\n      if (isActive) {\n        setActive(false);\n      }\n      return;\n    }\n    var left = refState.current.x + event.pageX - refState.current.x - refState.current.delta;\n    var isRTL = direction === 'rtl';\n    // Limit scroll range\n    left = Math.max(isRTL ? scrollBarWidth - bodyWidth : 0, Math.min(isRTL ? 0 : bodyWidth - scrollBarWidth, left));\n    // Calculate the scroll position and update\n    var shouldScroll = !isRTL || Math.abs(left) + Math.abs(scrollBarWidth) < bodyWidth;\n    if (shouldScroll) {\n      onScroll({\n        scrollLeft: left / bodyWidth * (bodyScrollWidth + 2)\n      });\n      refState.current.x = event.pageX;\n    }\n  };\n  var checkScrollBarVisible = function checkScrollBarVisible() {\n    raf.cancel(rafRef.current);\n    rafRef.current = raf(function () {\n      if (!scrollBodyRef.current) {\n        return;\n      }\n      var tableOffsetTop = getOffset(scrollBodyRef.current).top;\n      var tableBottomOffset = tableOffsetTop + scrollBodyRef.current.offsetHeight;\n      var currentClientOffset = container === window ? document.documentElement.scrollTop + window.innerHeight : getOffset(container).top + container.clientHeight;\n      if (tableBottomOffset - getScrollBarSize() <= currentClientOffset || tableOffsetTop >= currentClientOffset - offsetScroll) {\n        setScrollState(function (state) {\n          return _objectSpread(_objectSpread({}, state), {}, {\n            isHiddenScrollBar: true\n          });\n        });\n      } else {\n        setScrollState(function (state) {\n          return _objectSpread(_objectSpread({}, state), {}, {\n            isHiddenScrollBar: false\n          });\n        });\n      }\n    });\n  };\n  var setScrollLeft = function setScrollLeft(left) {\n    setScrollState(function (state) {\n      return _objectSpread(_objectSpread({}, state), {}, {\n        scrollLeft: left / bodyScrollWidth * bodyWidth || 0\n      });\n    });\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      setScrollLeft: setScrollLeft,\n      checkScrollBarVisible: checkScrollBarVisible\n    };\n  });\n  React.useEffect(function () {\n    var onMouseUpListener = addEventListener(document.body, 'mouseup', onMouseUp, false);\n    var onMouseMoveListener = addEventListener(document.body, 'mousemove', onMouseMove, false);\n    checkScrollBarVisible();\n    return function () {\n      onMouseUpListener.remove();\n      onMouseMoveListener.remove();\n    };\n  }, [scrollBarWidth, isActive]);\n\n  // Loop for scroll event check\n  React.useEffect(function () {\n    if (!scrollBodyRef.current) return;\n    var scrollParents = [];\n    var parent = getDOM(scrollBodyRef.current);\n    while (parent) {\n      scrollParents.push(parent);\n      parent = parent.parentElement;\n    }\n    scrollParents.forEach(function (p) {\n      return p.addEventListener('scroll', checkScrollBarVisible, false);\n    });\n    window.addEventListener('resize', checkScrollBarVisible, false);\n    window.addEventListener('scroll', checkScrollBarVisible, false);\n    container.addEventListener('scroll', checkScrollBarVisible, false);\n    return function () {\n      scrollParents.forEach(function (p) {\n        return p.removeEventListener('scroll', checkScrollBarVisible);\n      });\n      window.removeEventListener('resize', checkScrollBarVisible);\n      window.removeEventListener('scroll', checkScrollBarVisible);\n      container.removeEventListener('scroll', checkScrollBarVisible);\n    };\n  }, [container]);\n  React.useEffect(function () {\n    if (!scrollState.isHiddenScrollBar) {\n      setScrollState(function (state) {\n        var bodyNode = scrollBodyRef.current;\n        if (!bodyNode) {\n          return state;\n        }\n        return _objectSpread(_objectSpread({}, state), {}, {\n          scrollLeft: bodyNode.scrollLeft / bodyNode.scrollWidth * bodyNode.clientWidth\n        });\n      });\n    }\n  }, [scrollState.isHiddenScrollBar]);\n  if (bodyScrollWidth <= bodyWidth || !scrollBarWidth || scrollState.isHiddenScrollBar) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: getScrollBarSize(),\n      width: bodyWidth,\n      bottom: offsetScroll\n    },\n    className: \"\".concat(prefixCls, \"-sticky-scroll\")\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onMouseDown: onMouseDown,\n    ref: scrollBarRef,\n    className: classNames(\"\".concat(prefixCls, \"-sticky-scroll-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-sticky-scroll-bar-active\"), isActive)),\n    style: {\n      width: \"\".concat(scrollBarWidth, \"px\"),\n      transform: \"translate3d(\".concat(scrollState.scrollLeft, \"px, 0, 0)\")\n    }\n  }));\n};\nexport default /*#__PURE__*/React.forwardRef(StickyScrollBar);"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,gBAAgB,MAAM,6BAA6B;AAC1D,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,YAAY,MAAM,wBAAwB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,GAAG,MAAM,gBAAgB;AAChC,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,SAASC,MAAM,QAAQ,4BAA4B;AACnD,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,IAAI,EAAEC,GAAG,EAAE;EACxD,IAAIC,qBAAqB,EAAEC,sBAAsB;EACjD,IAAIC,aAAa,GAAGJ,IAAI,CAACI,aAAa;IACpCC,QAAQ,GAAGL,IAAI,CAACK,QAAQ;IACxBC,YAAY,GAAGN,IAAI,CAACM,YAAY;IAChCC,SAAS,GAAGP,IAAI,CAACO,SAAS;IAC1BC,SAAS,GAAGR,IAAI,CAACQ,SAAS;EAC5B,IAAIC,SAAS,GAAGpB,UAAU,CAACK,YAAY,EAAE,WAAW,CAAC;EACrD,IAAIgB,eAAe,GAAG,CAAC,CAACR,qBAAqB,GAAGE,aAAa,CAACO,OAAO,MAAM,IAAI,IAAIT,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACU,WAAW,KAAK,CAAC;EACtK,IAAIC,SAAS,GAAG,CAAC,CAACV,sBAAsB,GAAGC,aAAa,CAACO,OAAO,MAAM,IAAI,IAAIR,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACW,WAAW,KAAK,CAAC;EACnK,IAAIC,cAAc,GAAGL,eAAe,IAAIG,SAAS,IAAIA,SAAS,GAAGH,eAAe,CAAC;EACjF,IAAIM,YAAY,GAAGvB,KAAK,CAACwB,MAAM,CAAC,CAAC;EACjC,IAAIC,eAAe,GAAGvB,cAAc,CAAC;MACjCwB,UAAU,EAAE,CAAC;MACbC,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACFC,gBAAgB,GAAGjC,cAAc,CAAC8B,eAAe,EAAE,CAAC,CAAC;IACrDI,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACjCE,cAAc,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACtC,IAAIG,QAAQ,GAAG/B,KAAK,CAACwB,MAAM,CAAC;IAC1BQ,KAAK,EAAE,CAAC;IACRC,CAAC,EAAE;EACL,CAAC,CAAC;EACF,IAAIC,eAAe,GAAGlC,KAAK,CAACmC,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGzC,cAAc,CAACuC,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,SAAS,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACjC,IAAIG,MAAM,GAAGvC,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EAC/BxB,KAAK,CAACwC,SAAS,CAAC,YAAY;IAC1B,OAAO,YAAY;MACjBrC,GAAG,CAACsC,MAAM,CAACF,MAAM,CAACrB,OAAO,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAIwB,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnCJ,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC;EACD,IAAIK,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;IAC5CA,KAAK,CAACC,OAAO,CAAC,CAAC;IACfd,QAAQ,CAACb,OAAO,CAACc,KAAK,GAAGY,KAAK,CAACE,KAAK,GAAGjB,WAAW,CAACH,UAAU;IAC7DK,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAG,CAAC;IACtBK,SAAS,CAAC,IAAI,CAAC;IACfM,KAAK,CAACG,cAAc,CAAC,CAAC;EACxB,CAAC;EACD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACJ,KAAK,EAAE;IAC5C,IAAIK,OAAO;IACX;IACA,IAAIC,KAAK,GAAGN,KAAK,KAAK,CAACK,OAAO,GAAGE,MAAM,MAAM,IAAI,IAAIF,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACL,KAAK,CAAC;MAC/FQ,OAAO,GAAGF,KAAK,CAACE,OAAO;IACzB,IAAI,CAACf,QAAQ,IAAIe,OAAO,KAAK,CAAC,EAAE;MAC9B;MACA,IAAIf,QAAQ,EAAE;QACZC,SAAS,CAAC,KAAK,CAAC;MAClB;MACA;IACF;IACA,IAAIe,IAAI,GAAGtB,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAGW,KAAK,CAACE,KAAK,GAAGf,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAGF,QAAQ,CAACb,OAAO,CAACc,KAAK;IACzF,IAAIsB,KAAK,GAAGvC,SAAS,KAAK,KAAK;IAC/B;IACAsC,IAAI,GAAGE,IAAI,CAACC,GAAG,CAACF,KAAK,GAAGhC,cAAc,GAAGF,SAAS,GAAG,CAAC,EAAEmC,IAAI,CAACE,GAAG,CAACH,KAAK,GAAG,CAAC,GAAGlC,SAAS,GAAGE,cAAc,EAAE+B,IAAI,CAAC,CAAC;IAC/G;IACA,IAAIK,YAAY,GAAG,CAACJ,KAAK,IAAIC,IAAI,CAACI,GAAG,CAACN,IAAI,CAAC,GAAGE,IAAI,CAACI,GAAG,CAACrC,cAAc,CAAC,GAAGF,SAAS;IAClF,IAAIsC,YAAY,EAAE;MAChB9C,QAAQ,CAAC;QACPc,UAAU,EAAE2B,IAAI,GAAGjC,SAAS,IAAIH,eAAe,GAAG,CAAC;MACrD,CAAC,CAAC;MACFc,QAAQ,CAACb,OAAO,CAACe,CAAC,GAAGW,KAAK,CAACE,KAAK;IAClC;EACF,CAAC;EACD,IAAIc,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IAC3DzD,GAAG,CAACsC,MAAM,CAACF,MAAM,CAACrB,OAAO,CAAC;IAC1BqB,MAAM,CAACrB,OAAO,GAAGf,GAAG,CAAC,YAAY;MAC/B,IAAI,CAACQ,aAAa,CAACO,OAAO,EAAE;QAC1B;MACF;MACA,IAAI2C,cAAc,GAAGzD,SAAS,CAACO,aAAa,CAACO,OAAO,CAAC,CAAC4C,GAAG;MACzD,IAAIC,iBAAiB,GAAGF,cAAc,GAAGlD,aAAa,CAACO,OAAO,CAAC8C,YAAY;MAC3E,IAAIC,mBAAmB,GAAGnD,SAAS,KAAKqC,MAAM,GAAGe,QAAQ,CAACC,eAAe,CAACC,SAAS,GAAGjB,MAAM,CAACkB,WAAW,GAAGjE,SAAS,CAACU,SAAS,CAAC,CAACgD,GAAG,GAAGhD,SAAS,CAACwD,YAAY;MAC5J,IAAIP,iBAAiB,GAAGhE,gBAAgB,CAAC,CAAC,IAAIkE,mBAAmB,IAAIJ,cAAc,IAAII,mBAAmB,GAAGpD,YAAY,EAAE;QACzHiB,cAAc,CAAC,UAAUyC,KAAK,EAAE;UAC9B,OAAO7E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6E,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjD5C,iBAAiB,EAAE;UACrB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM;QACLG,cAAc,CAAC,UAAUyC,KAAK,EAAE;UAC9B,OAAO7E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6E,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;YACjD5C,iBAAiB,EAAE;UACrB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC;EACD,IAAI6C,aAAa,GAAG,SAASA,aAAaA,CAACnB,IAAI,EAAE;IAC/CvB,cAAc,CAAC,UAAUyC,KAAK,EAAE;MAC9B,OAAO7E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6E,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QACjD7C,UAAU,EAAE2B,IAAI,GAAGpC,eAAe,GAAGG,SAAS,IAAI;MACpD,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDpB,KAAK,CAACyE,mBAAmB,CAACjE,GAAG,EAAE,YAAY;IACzC,OAAO;MACLgE,aAAa,EAAEA,aAAa;MAC5BZ,qBAAqB,EAAEA;IACzB,CAAC;EACH,CAAC,CAAC;EACF5D,KAAK,CAACwC,SAAS,CAAC,YAAY;IAC1B,IAAIkC,iBAAiB,GAAG5E,gBAAgB,CAACoE,QAAQ,CAACS,IAAI,EAAE,SAAS,EAAEjC,SAAS,EAAE,KAAK,CAAC;IACpF,IAAIkC,mBAAmB,GAAG9E,gBAAgB,CAACoE,QAAQ,CAACS,IAAI,EAAE,WAAW,EAAE3B,WAAW,EAAE,KAAK,CAAC;IAC1FY,qBAAqB,CAAC,CAAC;IACvB,OAAO,YAAY;MACjBc,iBAAiB,CAACG,MAAM,CAAC,CAAC;MAC1BD,mBAAmB,CAACC,MAAM,CAAC,CAAC;IAC9B,CAAC;EACH,CAAC,EAAE,CAACvD,cAAc,EAAEe,QAAQ,CAAC,CAAC;;EAE9B;EACArC,KAAK,CAACwC,SAAS,CAAC,YAAY;IAC1B,IAAI,CAAC7B,aAAa,CAACO,OAAO,EAAE;IAC5B,IAAI4D,aAAa,GAAG,EAAE;IACtB,IAAIC,MAAM,GAAG1E,MAAM,CAACM,aAAa,CAACO,OAAO,CAAC;IAC1C,OAAO6D,MAAM,EAAE;MACbD,aAAa,CAACE,IAAI,CAACD,MAAM,CAAC;MAC1BA,MAAM,GAAGA,MAAM,CAACE,aAAa;IAC/B;IACAH,aAAa,CAACI,OAAO,CAAC,UAAUC,CAAC,EAAE;MACjC,OAAOA,CAAC,CAACrF,gBAAgB,CAAC,QAAQ,EAAE8D,qBAAqB,EAAE,KAAK,CAAC;IACnE,CAAC,CAAC;IACFT,MAAM,CAACrD,gBAAgB,CAAC,QAAQ,EAAE8D,qBAAqB,EAAE,KAAK,CAAC;IAC/DT,MAAM,CAACrD,gBAAgB,CAAC,QAAQ,EAAE8D,qBAAqB,EAAE,KAAK,CAAC;IAC/D9C,SAAS,CAAChB,gBAAgB,CAAC,QAAQ,EAAE8D,qBAAqB,EAAE,KAAK,CAAC;IAClE,OAAO,YAAY;MACjBkB,aAAa,CAACI,OAAO,CAAC,UAAUC,CAAC,EAAE;QACjC,OAAOA,CAAC,CAACC,mBAAmB,CAAC,QAAQ,EAAExB,qBAAqB,CAAC;MAC/D,CAAC,CAAC;MACFT,MAAM,CAACiC,mBAAmB,CAAC,QAAQ,EAAExB,qBAAqB,CAAC;MAC3DT,MAAM,CAACiC,mBAAmB,CAAC,QAAQ,EAAExB,qBAAqB,CAAC;MAC3D9C,SAAS,CAACsE,mBAAmB,CAAC,QAAQ,EAAExB,qBAAqB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAAC9C,SAAS,CAAC,CAAC;EACfd,KAAK,CAACwC,SAAS,CAAC,YAAY;IAC1B,IAAI,CAACX,WAAW,CAACF,iBAAiB,EAAE;MAClCG,cAAc,CAAC,UAAUyC,KAAK,EAAE;QAC9B,IAAIc,QAAQ,GAAG1E,aAAa,CAACO,OAAO;QACpC,IAAI,CAACmE,QAAQ,EAAE;UACb,OAAOd,KAAK;QACd;QACA,OAAO7E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE6E,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UACjD7C,UAAU,EAAE2D,QAAQ,CAAC3D,UAAU,GAAG2D,QAAQ,CAAClE,WAAW,GAAGkE,QAAQ,CAAChE;QACpE,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACQ,WAAW,CAACF,iBAAiB,CAAC,CAAC;EACnC,IAAIV,eAAe,IAAIG,SAAS,IAAI,CAACE,cAAc,IAAIO,WAAW,CAACF,iBAAiB,EAAE;IACpF,OAAO,IAAI;EACb;EACA,OAAO,aAAa3B,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;IAC7CC,KAAK,EAAE;MACLC,MAAM,EAAEzF,gBAAgB,CAAC,CAAC;MAC1B0F,KAAK,EAAErE,SAAS;MAChBsE,MAAM,EAAE7E;IACV,CAAC;IACD8E,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC5E,SAAS,EAAE,gBAAgB;EAClD,CAAC,EAAE,aAAahB,KAAK,CAACsF,aAAa,CAAC,KAAK,EAAE;IACzC3C,WAAW,EAAEA,WAAW;IACxBnC,GAAG,EAAEe,YAAY;IACjBoE,SAAS,EAAE9F,UAAU,CAAC,EAAE,CAAC+F,MAAM,CAAC5E,SAAS,EAAE,oBAAoB,CAAC,EAAEvB,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACmG,MAAM,CAAC5E,SAAS,EAAE,2BAA2B,CAAC,EAAEqB,QAAQ,CAAC,CAAC;IACnJkD,KAAK,EAAE;MACLE,KAAK,EAAE,EAAE,CAACG,MAAM,CAACtE,cAAc,EAAE,IAAI,CAAC;MACtCuE,SAAS,EAAE,cAAc,CAACD,MAAM,CAAC/D,WAAW,CAACH,UAAU,EAAE,WAAW;IACtE;EACF,CAAC,CAAC,CAAC;AACL,CAAC;AACD,eAAe,aAAa1B,KAAK,CAAC8F,UAAU,CAACxF,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}