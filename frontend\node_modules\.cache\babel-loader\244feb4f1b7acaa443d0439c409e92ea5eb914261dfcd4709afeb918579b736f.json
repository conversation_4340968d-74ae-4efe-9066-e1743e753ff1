{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\FactionList.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, message, Popconfirm, Tooltip, Row, Col, Statistic, Descriptions, InputNumber, Rate } from 'antd';\nimport { PlusOutlined, TeamOutlined, EditOutlined, DeleteOutlined, EyeOutlined, RobotOutlined, CrownOutlined, SafetyOutlined, ToolOutlined, HomeOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst FactionList = () => {\n  _s();\n  var _viewingFaction$membe;\n  const {\n    id: projectId\n  } = useParams();\n  const [factions, setFactions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingFaction, setEditingFaction] = useState(null);\n  const [viewingFaction, setViewingFaction] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟势力数据\n  const mockFactions = [{\n    id: 1,\n    name: '青云宗',\n    type: 'sect',\n    level: 'major',\n    territory: '青云山脉',\n    leader: '云长老',\n    memberCount: 3000,\n    strength: 4,\n    influence: 5,\n    description: '修仙界的正道大宗，以剑法闻名天下',\n    history: '创立于三千年前，历经数代传承',\n    specialties: ['剑法', '炼丹', '阵法'],\n    allies: ['天剑门', '玄天宗'],\n    enemies: ['血煞门', '魔道联盟'],\n    resources: ['灵石矿', '药园', '剑冢'],\n    status: 'active',\n    createdAt: '2024-01-15'\n  }, {\n    id: 2,\n    name: '血煞门',\n    type: 'sect',\n    level: 'major',\n    territory: '血煞谷',\n    leader: '血煞魔君',\n    memberCount: 1500,\n    strength: 4,\n    influence: 3,\n    description: '魔道势力，以血煞功法著称',\n    history: '魔道崛起的代表势力',\n    specialties: ['血煞功', '魔道秘术', '炼魂术'],\n    allies: ['魔道联盟'],\n    enemies: ['青云宗', '正道联盟'],\n    resources: ['血池', '魔石矿'],\n    status: 'active',\n    createdAt: '2024-01-16'\n  }, {\n    id: 3,\n    name: '商盟',\n    type: 'organization',\n    level: 'medium',\n    territory: '各大城市',\n    leader: '商会会长',\n    memberCount: 5000,\n    strength: 2,\n    influence: 4,\n    description: '修仙界最大的商业组织',\n    history: '由各大商家联合组成',\n    specialties: ['贸易', '情报', '物流'],\n    allies: ['中立势力'],\n    enemies: [],\n    resources: ['商路', '仓库', '金库'],\n    status: 'active',\n    createdAt: '2024-01-17'\n  }];\n  useEffect(() => {\n    setFactions(mockFactions);\n  }, []);\n\n  // 势力类型配置\n  const typeConfig = {\n    sect: {\n      color: 'blue',\n      text: '宗门',\n      icon: /*#__PURE__*/_jsxDEV(HomeOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 46\n      }, this)\n    },\n    family: {\n      color: 'green',\n      text: '家族',\n      icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 49\n      }, this)\n    },\n    organization: {\n      color: 'orange',\n      text: '组织',\n      icon: /*#__PURE__*/_jsxDEV(SafetyOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 56\n      }, this)\n    },\n    empire: {\n      color: 'purple',\n      text: '帝国',\n      icon: /*#__PURE__*/_jsxDEV(CrownOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 50\n      }, this)\n    },\n    alliance: {\n      color: 'gold',\n      text: '联盟',\n      icon: /*#__PURE__*/_jsxDEV(ToolOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 50\n      }, this)\n    }\n  };\n\n  // 势力等级配置\n  const levelConfig = {\n    minor: {\n      color: 'default',\n      text: '小型'\n    },\n    medium: {\n      color: 'blue',\n      text: '中型'\n    },\n    major: {\n      color: 'orange',\n      text: '大型'\n    },\n    super: {\n      color: 'red',\n      text: '超级'\n    }\n  };\n\n  // 表格列配置\n  const columns = [{\n    title: '势力名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [typeConfig[record.type].icon, /*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '类型',\n    dataIndex: 'type',\n    key: 'type',\n    render: type => /*#__PURE__*/_jsxDEV(Tag, {\n      color: typeConfig[type].color,\n      children: typeConfig[type].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 9\n    }, this),\n    filters: [{\n      text: '宗门',\n      value: 'sect'\n    }, {\n      text: '家族',\n      value: 'family'\n    }, {\n      text: '组织',\n      value: 'organization'\n    }, {\n      text: '帝国',\n      value: 'empire'\n    }, {\n      text: '联盟',\n      value: 'alliance'\n    }],\n    onFilter: (value, record) => record.type === value\n  }, {\n    title: '等级',\n    dataIndex: 'level',\n    key: 'level',\n    render: level => /*#__PURE__*/_jsxDEV(Tag, {\n      color: levelConfig[level].color,\n      children: levelConfig[level].text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '领袖',\n    dataIndex: 'leader',\n    key: 'leader'\n  }, {\n    title: '成员数量',\n    dataIndex: 'memberCount',\n    key: 'memberCount',\n    render: count => count.toLocaleString(),\n    sorter: (a, b) => a.memberCount - b.memberCount\n  }, {\n    title: '实力',\n    dataIndex: 'strength',\n    key: 'strength',\n    render: strength => /*#__PURE__*/_jsxDEV(Rate, {\n      disabled: true,\n      value: strength,\n      style: {\n        fontSize: 14\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.strength - b.strength\n  }, {\n    title: '影响力',\n    dataIndex: 'influence',\n    key: 'influence',\n    render: influence => /*#__PURE__*/_jsxDEV(Rate, {\n      disabled: true,\n      value: influence,\n      style: {\n        fontSize: 14\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.influence - b.influence\n  }, {\n    title: '操作',\n    key: 'actions',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleView(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 217,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"AI\\u751F\\u6210\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(RobotOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleAIGenerate(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u52BF\\u529B\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this)\n  }];\n\n  // 处理新建/编辑势力\n  const handleCreateOrEdit = () => {\n    setEditingFaction(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = faction => {\n    setEditingFaction(faction);\n    form.setFieldsValue({\n      ...faction,\n      specialties: faction.specialties.join('\\n'),\n      allies: faction.allies.join('\\n'),\n      enemies: faction.enemies.join('\\n'),\n      resources: faction.resources.join('\\n')\n    });\n    setModalVisible(true);\n  };\n  const handleView = faction => {\n    setViewingFaction(faction);\n    setDetailModalVisible(true);\n  };\n  const handleAIGenerate = faction => {\n    message.info(`AI生成势力详情：${faction.name}`);\n  };\n  const handleDelete = async id => {\n    try {\n      // 调用后端API删除数据\n      await axios.delete(`/api/project-data/projects/${projectId}/data/faction/${id}`);\n\n      // 删除成功后从列表中移除\n      setFactions(factions.filter(f => f.id !== id));\n      message.success('势力删除成功');\n    } catch (error) {\n      console.error('删除势力失败:', error);\n      message.error('删除势力失败');\n    }\n  };\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 处理数组字段\n      const processedValues = {\n        ...values,\n        specialties: values.specialties ? values.specialties.split('\\n').filter(item => item.trim()) : [],\n        allies: values.allies ? values.allies.split('\\n').filter(item => item.trim()) : [],\n        enemies: values.enemies ? values.enemies.split('\\n').filter(item => item.trim()) : [],\n        resources: values.resources ? values.resources.split('\\n').filter(item => item.trim()) : []\n      };\n      if (editingFaction) {\n        // 编辑势力\n        setFactions(factions.map(f => f.id === editingFaction.id ? {\n          ...f,\n          ...processedValues\n        } : f));\n        message.success('势力更新成功');\n      } else {\n        // 新建势力\n        const newFaction = {\n          id: Date.now(),\n          ...processedValues,\n          status: 'active',\n          createdAt: new Date().toISOString().split('T')[0]\n        };\n        setFactions([...factions, newFaction]);\n        message.success('势力创建成功');\n      }\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalFactions = factions.length;\n  const sects = factions.filter(f => f.type === 'sect').length;\n  const organizations = factions.filter(f => f.type === 'organization').length;\n  const families = factions.filter(f => f.type === 'family').length;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: \"\\u52BF\\u529B\\u7BA1\\u7406\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 343,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u52BF\\u529B\\u6570\",\n            value: totalFactions,\n            prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 349,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5B97\\u95E8\",\n            value: sects,\n            prefix: /*#__PURE__*/_jsxDEV(HomeOutlined, {\n              style: {\n                color: '#1890ff'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7EC4\\u7EC7\",\n            value: organizations,\n            prefix: /*#__PURE__*/_jsxDEV(SafetyOutlined, {\n              style: {\n                color: '#fa8c16'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5BB6\\u65CF\",\n            value: families,\n            prefix: /*#__PURE__*/_jsxDEV(TeamOutlined, {\n              style: {\n                color: '#52c41a'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"toolbar-left\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 21\n            }, this),\n            onClick: handleCreateOrEdit,\n            children: \"\\u6DFB\\u52A0\\u52BF\\u529B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: factions,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个势力`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingFaction ? '编辑势力' : '新建势力',\n      open: modalVisible,\n      onOk: handleModalOk,\n      onCancel: () => {\n        setModalVisible(false);\n        form.resetFields();\n      },\n      confirmLoading: loading,\n      width: 800,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: {\n          type: 'sect',\n          level: 'medium',\n          strength: 3,\n          influence: 3,\n          status: 'active'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u52BF\\u529B\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入势力名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u52BF\\u529B\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 443,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 437,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u52BF\\u529B\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择势力类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"sect\",\n                  children: \"\\u5B97\\u95E8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"family\",\n                  children: \"\\u5BB6\\u65CF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"organization\",\n                  children: \"\\u7EC4\\u7EC7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"empire\",\n                  children: \"\\u5E1D\\u56FD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"alliance\",\n                  children: \"\\u8054\\u76DF\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 436,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"level\",\n              label: \"\\u52BF\\u529B\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请选择势力等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"minor\",\n                  children: \"\\u5C0F\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 471,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"medium\",\n                  children: \"\\u4E2D\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 472,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"major\",\n                  children: \"\\u5927\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 473,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"super\",\n                  children: \"\\u8D85\\u7EA7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 474,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"leader\",\n              label: \"\\u9886\\u8896\",\n              rules: [{\n                required: true,\n                message: '请输入领袖名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u9886\\u8896\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 484,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"memberCount\",\n              label: \"\\u6210\\u5458\\u6570\\u91CF\",\n              rules: [{\n                required: true,\n                message: '请输入成员数量'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 487,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"territory\",\n              label: \"\\u52BF\\u529B\\u8303\\u56F4\",\n              rules: [{\n                required: true,\n                message: '请输入势力范围'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u5982\\uFF1A\\u9752\\u4E91\\u5C71\\u8109\\u3001\\u8840\\u715E\\u8C37\\u7B49\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"strength\",\n              label: \"\\u5B9E\\u529B\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请选择实力等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Rate, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 514,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"influence\",\n              label: \"\\u5F71\\u54CD\\u529B\",\n              rules: [{\n                required: true,\n                message: '请选择影响力'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Rate, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 523,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 518,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u52BF\\u529B\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入势力描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u52BF\\u529B\\u7684\\u57FA\\u672C\\u60C5\\u51B5\\u3001\\u7279\\u8272\\u7B49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 528,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"history\",\n          label: \"\\u52BF\\u529B\\u5386\\u53F2\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 2,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u52BF\\u529B\\u7684\\u53D1\\u5C55\\u5386\\u53F2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"specialties\",\n              label: \"\\u7279\\u8272\\u80FD\\u529B\",\n              tooltip: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u80FD\\u529B\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u5982\\uFF1A\\u5251\\u6CD5\\n\\u70BC\\u4E39\\n\\u9635\\u6CD5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 551,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"resources\",\n              label: \"\\u52BF\\u529B\\u8D44\\u6E90\",\n              tooltip: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u8D44\\u6E90\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 3,\n                placeholder: \"\\u5982\\uFF1A\\u7075\\u77F3\\u77FF\\n\\u836F\\u56ED\\n\\u5251\\u51A2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 563,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"allies\",\n              label: \"\\u76DF\\u53CB\\u52BF\\u529B\",\n              tooltip: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u76DF\\u53CB\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 2,\n                placeholder: \"\\u5982\\uFF1A\\u5929\\u5251\\u95E8\\n\\u7384\\u5929\\u5B97\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 583,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"enemies\",\n              label: \"\\u654C\\u5BF9\\u52BF\\u529B\",\n              tooltip: \"\\u6BCF\\u884C\\u4E00\\u4E2A\\u654C\\u4EBA\",\n              children: /*#__PURE__*/_jsxDEV(TextArea, {\n                rows: 2,\n                placeholder: \"\\u5982\\uFF1A\\u8840\\u715E\\u95E8\\n\\u9B54\\u9053\\u8054\\u76DF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 595,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u52BF\\u529B\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setDetailModalVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 611,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: viewingFaction && /*#__PURE__*/_jsxDEV(Descriptions, {\n        bordered: true,\n        column: 2,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u52BF\\u529B\\u540D\\u79F0\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [typeConfig[viewingFaction.type].icon, /*#__PURE__*/_jsxDEV(Text, {\n              strong: true,\n              children: viewingFaction.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 622,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: typeConfig[viewingFaction.type].color,\n              children: typeConfig[viewingFaction.type].text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 623,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Tag, {\n              color: levelConfig[viewingFaction.level].color,\n              children: levelConfig[viewingFaction.level].text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 626,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9886\\u8896\",\n          children: viewingFaction.leader\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u6210\\u5458\\u6570\\u91CF\",\n          children: (_viewingFaction$membe = viewingFaction.memberCount) === null || _viewingFaction$membe === void 0 ? void 0 : _viewingFaction$membe.toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u52BF\\u529B\\u8303\\u56F4\",\n          children: viewingFaction.territory\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: viewingFaction.createdAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5B9E\\u529B\\u7B49\\u7EA7\",\n          children: /*#__PURE__*/_jsxDEV(Rate, {\n            disabled: true,\n            value: viewingFaction.strength,\n            style: {\n              fontSize: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5F71\\u54CD\\u529B\",\n          children: /*#__PURE__*/_jsxDEV(Rate, {\n            disabled: true,\n            value: viewingFaction.influence,\n            style: {\n              fontSize: 16\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 641,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u52BF\\u529B\\u63CF\\u8FF0\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: viewingFaction.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 13\n        }, this), viewingFaction.history && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u52BF\\u529B\\u5386\\u53F2\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(Paragraph, {\n            children: viewingFaction.history\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 15\n        }, this), viewingFaction.specialties && viewingFaction.specialties.length > 0 && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u7279\\u8272\\u80FD\\u529B\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            wrap: true,\n            children: viewingFaction.specialties.map((specialty, index) => /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"blue\",\n              children: specialty\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 656,\n          columnNumber: 15\n        }, this), viewingFaction.resources && viewingFaction.resources.length > 0 && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u52BF\\u529B\\u8D44\\u6E90\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            wrap: true,\n            children: viewingFaction.resources.map((resource, index) => /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"green\",\n              children: resource\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 667,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 15\n        }, this), viewingFaction.allies && viewingFaction.allies.length > 0 && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u76DF\\u53CB\\u52BF\\u529B\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            wrap: true,\n            children: viewingFaction.allies.map((ally, index) => /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"cyan\",\n              children: ally\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 15\n        }, this), viewingFaction.enemies && viewingFaction.enemies.length > 0 && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u654C\\u5BF9\\u52BF\\u529B\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            wrap: true,\n            children: viewingFaction.enemies.map((enemy, index) => /*#__PURE__*/_jsxDEV(Tag, {\n              color: \"red\",\n              children: enemy\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 606,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 342,\n    columnNumber: 5\n  }, this);\n};\n_s(FactionList, \"Vj3yROoyCIw9D5wkBxAIh+vVctc=\", false, function () {\n  return [useParams, Form.useForm];\n});\n_c = FactionList;\nexport default FactionList;\nvar _c;\n$RefreshReg$(_c, \"FactionList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "axios", "Card", "Typography", "<PERSON><PERSON>", "Table", "Space", "Tag", "Modal", "Form", "Input", "Select", "message", "Popconfirm", "<PERSON><PERSON><PERSON>", "Row", "Col", "Statistic", "Descriptions", "InputNumber", "Rate", "PlusOutlined", "TeamOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "RobotOutlined", "CrownOutlined", "SafetyOutlined", "ToolOutlined", "HomeOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "TextArea", "Option", "FactionList", "_s", "_viewingFaction$membe", "id", "projectId", "factions", "setFactions", "loading", "setLoading", "modalVisible", "setModalVisible", "detailModalVisible", "setDetailModalVisible", "editingFaction", "setEditingFaction", "viewingFaction", "setViewingFaction", "form", "useForm", "mockFactions", "name", "type", "level", "territory", "leader", "memberCount", "strength", "influence", "description", "history", "specialties", "allies", "enemies", "resources", "status", "createdAt", "typeConfig", "sect", "color", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "family", "organization", "empire", "alliance", "levelConfig", "minor", "medium", "major", "super", "columns", "title", "dataIndex", "key", "render", "record", "children", "strong", "filters", "value", "onFilter", "count", "toLocaleString", "sorter", "a", "b", "disabled", "style", "fontSize", "_", "onClick", "handleView", "handleEdit", "handleAIGenerate", "onConfirm", "handleDelete", "okText", "cancelText", "danger", "handleCreateOrEdit", "resetFields", "faction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "info", "delete", "filter", "f", "success", "error", "console", "handleModalOk", "values", "validateFields", "processedValues", "split", "item", "trim", "map", "newFaction", "Date", "now", "toISOString", "totalFactions", "length", "sects", "organizations", "families", "className", "gutter", "marginBottom", "span", "prefix", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onOk", "onCancel", "confirmLoading", "width", "layout", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "min", "rows", "tooltip", "footer", "bordered", "column", "wrap", "specialty", "index", "resource", "ally", "enemy", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/FactionList.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  Card,\n  Typography,\n  Button,\n  Table,\n  Space,\n  Tag,\n  Modal,\n  Form,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Tooltip,\n  Row,\n  Col,\n  Statistic,\n  Descriptions,\n  InputNumber,\n  Rate\n} from 'antd';\nimport {\n  PlusOutlined,\n  TeamOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  RobotOutlined,\n  CrownOutlined,\n  SafetyOutlined,\n  ToolOutlined,\n  HomeOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst FactionList = () => {\n  const { id: projectId } = useParams();\n  const [factions, setFactions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingFaction, setEditingFaction] = useState(null);\n  const [viewingFaction, setViewingFaction] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟势力数据\n  const mockFactions = [\n    {\n      id: 1,\n      name: '青云宗',\n      type: 'sect',\n      level: 'major',\n      territory: '青云山脉',\n      leader: '云长老',\n      memberCount: 3000,\n      strength: 4,\n      influence: 5,\n      description: '修仙界的正道大宗，以剑法闻名天下',\n      history: '创立于三千年前，历经数代传承',\n      specialties: ['剑法', '炼丹', '阵法'],\n      allies: ['天剑门', '玄天宗'],\n      enemies: ['血煞门', '魔道联盟'],\n      resources: ['灵石矿', '药园', '剑冢'],\n      status: 'active',\n      createdAt: '2024-01-15'\n    },\n    {\n      id: 2,\n      name: '血煞门',\n      type: 'sect',\n      level: 'major',\n      territory: '血煞谷',\n      leader: '血煞魔君',\n      memberCount: 1500,\n      strength: 4,\n      influence: 3,\n      description: '魔道势力，以血煞功法著称',\n      history: '魔道崛起的代表势力',\n      specialties: ['血煞功', '魔道秘术', '炼魂术'],\n      allies: ['魔道联盟'],\n      enemies: ['青云宗', '正道联盟'],\n      resources: ['血池', '魔石矿'],\n      status: 'active',\n      createdAt: '2024-01-16'\n    },\n    {\n      id: 3,\n      name: '商盟',\n      type: 'organization',\n      level: 'medium',\n      territory: '各大城市',\n      leader: '商会会长',\n      memberCount: 5000,\n      strength: 2,\n      influence: 4,\n      description: '修仙界最大的商业组织',\n      history: '由各大商家联合组成',\n      specialties: ['贸易', '情报', '物流'],\n      allies: ['中立势力'],\n      enemies: [],\n      resources: ['商路', '仓库', '金库'],\n      status: 'active',\n      createdAt: '2024-01-17'\n    }\n  ];\n\n  useEffect(() => {\n    setFactions(mockFactions);\n  }, []);\n\n  // 势力类型配置\n  const typeConfig = {\n    sect: { color: 'blue', text: '宗门', icon: <HomeOutlined /> },\n    family: { color: 'green', text: '家族', icon: <TeamOutlined /> },\n    organization: { color: 'orange', text: '组织', icon: <SafetyOutlined /> },\n    empire: { color: 'purple', text: '帝国', icon: <CrownOutlined /> },\n    alliance: { color: 'gold', text: '联盟', icon: <ToolOutlined /> }\n  };\n\n  // 势力等级配置\n  const levelConfig = {\n    minor: { color: 'default', text: '小型' },\n    medium: { color: 'blue', text: '中型' },\n    major: { color: 'orange', text: '大型' },\n    super: { color: 'red', text: '超级' }\n  };\n\n  // 表格列配置\n  const columns = [\n    {\n      title: '势力名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          {typeConfig[record.type].icon}\n          <Text strong>{text}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '类型',\n      dataIndex: 'type',\n      key: 'type',\n      render: (type) => (\n        <Tag color={typeConfig[type].color}>\n          {typeConfig[type].text}\n        </Tag>\n      ),\n      filters: [\n        { text: '宗门', value: 'sect' },\n        { text: '家族', value: 'family' },\n        { text: '组织', value: 'organization' },\n        { text: '帝国', value: 'empire' },\n        { text: '联盟', value: 'alliance' }\n      ],\n      onFilter: (value, record) => record.type === value\n    },\n    {\n      title: '等级',\n      dataIndex: 'level',\n      key: 'level',\n      render: (level) => (\n        <Tag color={levelConfig[level].color}>\n          {levelConfig[level].text}\n        </Tag>\n      )\n    },\n    {\n      title: '领袖',\n      dataIndex: 'leader',\n      key: 'leader'\n    },\n    {\n      title: '成员数量',\n      dataIndex: 'memberCount',\n      key: 'memberCount',\n      render: (count) => count.toLocaleString(),\n      sorter: (a, b) => a.memberCount - b.memberCount\n    },\n    {\n      title: '实力',\n      dataIndex: 'strength',\n      key: 'strength',\n      render: (strength) => (\n        <Rate disabled value={strength} style={{ fontSize: 14 }} />\n      ),\n      sorter: (a, b) => a.strength - b.strength\n    },\n    {\n      title: '影响力',\n      dataIndex: 'influence',\n      key: 'influence',\n      render: (influence) => (\n        <Rate disabled value={influence} style={{ fontSize: 14 }} />\n      ),\n      sorter: (a, b) => a.influence - b.influence\n    },\n    {\n      title: '操作',\n      key: 'actions',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"text\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"AI生成\">\n            <Button\n              type=\"text\"\n              icon={<RobotOutlined />}\n              onClick={() => handleAIGenerate(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个势力吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  // 处理新建/编辑势力\n  const handleCreateOrEdit = () => {\n    setEditingFaction(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (faction) => {\n    setEditingFaction(faction);\n    form.setFieldsValue({\n      ...faction,\n      specialties: faction.specialties.join('\\n'),\n      allies: faction.allies.join('\\n'),\n      enemies: faction.enemies.join('\\n'),\n      resources: faction.resources.join('\\n')\n    });\n    setModalVisible(true);\n  };\n\n  const handleView = (faction) => {\n    setViewingFaction(faction);\n    setDetailModalVisible(true);\n  };\n\n  const handleAIGenerate = (faction) => {\n    message.info(`AI生成势力详情：${faction.name}`);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 调用后端API删除数据\n      await axios.delete(`/api/project-data/projects/${projectId}/data/faction/${id}`);\n\n      // 删除成功后从列表中移除\n      setFactions(factions.filter(f => f.id !== id));\n      message.success('势力删除成功');\n    } catch (error) {\n      console.error('删除势力失败:', error);\n      message.error('删除势力失败');\n    }\n  };\n\n  const handleModalOk = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 处理数组字段\n      const processedValues = {\n        ...values,\n        specialties: values.specialties ? values.specialties.split('\\n').filter(item => item.trim()) : [],\n        allies: values.allies ? values.allies.split('\\n').filter(item => item.trim()) : [],\n        enemies: values.enemies ? values.enemies.split('\\n').filter(item => item.trim()) : [],\n        resources: values.resources ? values.resources.split('\\n').filter(item => item.trim()) : []\n      };\n\n      if (editingFaction) {\n        // 编辑势力\n        setFactions(factions.map(f =>\n          f.id === editingFaction.id\n            ? { ...f, ...processedValues }\n            : f\n        ));\n        message.success('势力更新成功');\n      } else {\n        // 新建势力\n        const newFaction = {\n          id: Date.now(),\n          ...processedValues,\n          status: 'active',\n          createdAt: new Date().toISOString().split('T')[0]\n        };\n        setFactions([...factions, newFaction]);\n        message.success('势力创建成功');\n      }\n\n      setModalVisible(false);\n      form.resetFields();\n    } catch (error) {\n      console.error('表单验证失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 统计数据\n  const totalFactions = factions.length;\n  const sects = factions.filter(f => f.type === 'sect').length;\n  const organizations = factions.filter(f => f.type === 'organization').length;\n  const families = factions.filter(f => f.type === 'family').length;\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">势力管理</Title>\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"总势力数\"\n              value={totalFactions}\n              prefix={<TeamOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"宗门\"\n              value={sects}\n              prefix={<HomeOutlined style={{ color: '#1890ff' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"组织\"\n              value={organizations}\n              prefix={<SafetyOutlined style={{ color: '#fa8c16' }} />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card>\n            <Statistic\n              title=\"家族\"\n              value={families}\n              prefix={<TeamOutlined style={{ color: '#52c41a' }} />}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <div className=\"toolbar\">\n          <div className=\"toolbar-left\">\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleCreateOrEdit}\n            >\n              添加势力\n            </Button>\n          </div>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={factions}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个势力`\n          }}\n        />\n      </Card>\n\n      {/* 新建/编辑势力模态框 */}\n      <Modal\n        title={editingFaction ? '编辑势力' : '新建势力'}\n        open={modalVisible}\n        onOk={handleModalOk}\n        onCancel={() => {\n          setModalVisible(false);\n          form.resetFields();\n        }}\n        confirmLoading={loading}\n        width={800}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={{\n            type: 'sect',\n            level: 'medium',\n            strength: 3,\n            influence: 3,\n            status: 'active'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"势力名称\"\n                rules={[{ required: true, message: '请输入势力名称' }]}\n              >\n                <Input placeholder=\"请输入势力名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"type\"\n                label=\"势力类型\"\n                rules={[{ required: true, message: '请选择势力类型' }]}\n              >\n                <Select>\n                  <Option value=\"sect\">宗门</Option>\n                  <Option value=\"family\">家族</Option>\n                  <Option value=\"organization\">组织</Option>\n                  <Option value=\"empire\">帝国</Option>\n                  <Option value=\"alliance\">联盟</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"level\"\n                label=\"势力等级\"\n                rules={[{ required: true, message: '请选择势力等级' }]}\n              >\n                <Select>\n                  <Option value=\"minor\">小型</Option>\n                  <Option value=\"medium\">中型</Option>\n                  <Option value=\"major\">大型</Option>\n                  <Option value=\"super\">超级</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"leader\"\n                label=\"领袖\"\n                rules={[{ required: true, message: '请输入领袖名称' }]}\n              >\n                <Input placeholder=\"请输入领袖名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"memberCount\"\n                label=\"成员数量\"\n                rules={[{ required: true, message: '请输入成员数量' }]}\n              >\n                <InputNumber min={1} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"territory\"\n                label=\"势力范围\"\n                rules={[{ required: true, message: '请输入势力范围' }]}\n              >\n                <Input placeholder=\"如：青云山脉、血煞谷等\" />\n              </Form.Item>\n            </Col>\n            <Col span={6}>\n              <Form.Item\n                name=\"strength\"\n                label=\"实力等级\"\n                rules={[{ required: true, message: '请选择实力等级' }]}\n              >\n                <Rate />\n              </Form.Item>\n            </Col>\n            <Col span={6}>\n              <Form.Item\n                name=\"influence\"\n                label=\"影响力\"\n                rules={[{ required: true, message: '请选择影响力' }]}\n              >\n                <Rate />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"势力描述\"\n            rules={[{ required: true, message: '请输入势力描述' }]}\n          >\n            <TextArea\n              rows={3}\n              placeholder=\"请描述势力的基本情况、特色等\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"history\"\n            label=\"势力历史\"\n          >\n            <TextArea\n              rows={2}\n              placeholder=\"请描述势力的发展历史\"\n            />\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"specialties\"\n                label=\"特色能力\"\n                tooltip=\"每行一个能力\"\n              >\n                <TextArea\n                  rows={3}\n                  placeholder=\"如：剑法&#10;炼丹&#10;阵法\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"resources\"\n                label=\"势力资源\"\n                tooltip=\"每行一个资源\"\n              >\n                <TextArea\n                  rows={3}\n                  placeholder=\"如：灵石矿&#10;药园&#10;剑冢\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"allies\"\n                label=\"盟友势力\"\n                tooltip=\"每行一个盟友\"\n              >\n                <TextArea\n                  rows={2}\n                  placeholder=\"如：天剑门&#10;玄天宗\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"enemies\"\n                label=\"敌对势力\"\n                tooltip=\"每行一个敌人\"\n              >\n                <TextArea\n                  rows={2}\n                  placeholder=\"如：血煞门&#10;魔道联盟\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Modal>\n\n      {/* 势力详情查看模态框 */}\n      <Modal\n        title=\"势力详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={[\n          <Button key=\"close\" onClick={() => setDetailModalVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {viewingFaction && (\n          <Descriptions bordered column={2}>\n            <Descriptions.Item label=\"势力名称\" span={2}>\n              <Space>\n                {typeConfig[viewingFaction.type].icon}\n                <Text strong>{viewingFaction.name}</Text>\n                <Tag color={typeConfig[viewingFaction.type].color}>\n                  {typeConfig[viewingFaction.type].text}\n                </Tag>\n                <Tag color={levelConfig[viewingFaction.level].color}>\n                  {levelConfig[viewingFaction.level].text}\n                </Tag>\n              </Space>\n            </Descriptions.Item>\n\n            <Descriptions.Item label=\"领袖\">{viewingFaction.leader}</Descriptions.Item>\n            <Descriptions.Item label=\"成员数量\">{viewingFaction.memberCount?.toLocaleString()}</Descriptions.Item>\n\n            <Descriptions.Item label=\"势力范围\">{viewingFaction.territory}</Descriptions.Item>\n            <Descriptions.Item label=\"创建时间\">{viewingFaction.createdAt}</Descriptions.Item>\n\n            <Descriptions.Item label=\"实力等级\">\n              <Rate disabled value={viewingFaction.strength} style={{ fontSize: 16 }} />\n            </Descriptions.Item>\n            <Descriptions.Item label=\"影响力\">\n              <Rate disabled value={viewingFaction.influence} style={{ fontSize: 16 }} />\n            </Descriptions.Item>\n\n            <Descriptions.Item label=\"势力描述\" span={2}>\n              <Paragraph>{viewingFaction.description}</Paragraph>\n            </Descriptions.Item>\n\n            {viewingFaction.history && (\n              <Descriptions.Item label=\"势力历史\" span={2}>\n                <Paragraph>{viewingFaction.history}</Paragraph>\n              </Descriptions.Item>\n            )}\n\n            {viewingFaction.specialties && viewingFaction.specialties.length > 0 && (\n              <Descriptions.Item label=\"特色能力\" span={2}>\n                <Space wrap>\n                  {viewingFaction.specialties.map((specialty, index) => (\n                    <Tag key={index} color=\"blue\">{specialty}</Tag>\n                  ))}\n                </Space>\n              </Descriptions.Item>\n            )}\n\n            {viewingFaction.resources && viewingFaction.resources.length > 0 && (\n              <Descriptions.Item label=\"势力资源\" span={2}>\n                <Space wrap>\n                  {viewingFaction.resources.map((resource, index) => (\n                    <Tag key={index} color=\"green\">{resource}</Tag>\n                  ))}\n                </Space>\n              </Descriptions.Item>\n            )}\n\n            {viewingFaction.allies && viewingFaction.allies.length > 0 && (\n              <Descriptions.Item label=\"盟友势力\" span={2}>\n                <Space wrap>\n                  {viewingFaction.allies.map((ally, index) => (\n                    <Tag key={index} color=\"cyan\">{ally}</Tag>\n                  ))}\n                </Space>\n              </Descriptions.Item>\n            )}\n\n            {viewingFaction.enemies && viewingFaction.enemies.length > 0 && (\n              <Descriptions.Item label=\"敌对势力\" span={2}>\n                <Space wrap>\n                  {viewingFaction.enemies.map((enemy, index) => (\n                    <Tag key={index} color=\"red\">{enemy}</Tag>\n                  ))}\n                </Space>\n              </Descriptions.Item>\n            )}\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default FactionList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,YAAY,EACZC,WAAW,EACXC,IAAI,QACC,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,YAAY,EACZC,YAAY,QACP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGhC,UAAU;AAC7C,MAAM;EAAEiC;AAAS,CAAC,GAAG1B,KAAK;AAC1B,MAAM;EAAE2B;AAAO,CAAC,GAAG1B,MAAM;AAEzB,MAAM2B,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACxB,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAG1C,SAAS,CAAC,CAAC;EACrC,MAAM,CAAC2C,QAAQ,EAAEC,WAAW,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACmD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACqD,cAAc,EAAEC,iBAAiB,CAAC,GAAGtD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuD,cAAc,EAAEC,iBAAiB,CAAC,GAAGxD,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACyD,IAAI,CAAC,GAAG9C,IAAI,CAAC+C,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,YAAY,GAAG,CACnB;IACEhB,EAAE,EAAE,CAAC;IACLiB,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,KAAK;IACbC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,kBAAkB;IAC/BC,OAAO,EAAE,gBAAgB;IACzBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/BC,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;IACtBC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;IACxBC,SAAS,EAAE,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAC9BC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE;EACb,CAAC,EACD;IACEhC,EAAE,EAAE,CAAC;IACLiB,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,KAAK;IAChBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,cAAc;IAC3BC,OAAO,EAAE,WAAW;IACpBC,WAAW,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;IACnCC,MAAM,EAAE,CAAC,MAAM,CAAC;IAChBC,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;IACxBC,SAAS,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;IACxBC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE;EACb,CAAC,EACD;IACEhC,EAAE,EAAE,CAAC;IACLiB,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,QAAQ;IACfC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,MAAM;IACdC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,CAAC;IACXC,SAAS,EAAE,CAAC;IACZC,WAAW,EAAE,YAAY;IACzBC,OAAO,EAAE,WAAW;IACpBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/BC,MAAM,EAAE,CAAC,MAAM,CAAC;IAChBC,OAAO,EAAE,EAAE;IACXC,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC7BC,MAAM,EAAE,QAAQ;IAChBC,SAAS,EAAE;EACb,CAAC,CACF;EAED1E,SAAS,CAAC,MAAM;IACd6C,WAAW,CAACa,YAAY,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMiB,UAAU,GAAG;IACjBC,IAAI,EAAE;MAAEC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE9C,OAAA,CAACF,YAAY;QAAAiD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC3DC,MAAM,EAAE;MAAEP,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE9C,OAAA,CAACV,YAAY;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAC9DE,YAAY,EAAE;MAAER,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE9C,OAAA,CAACJ,cAAc;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IACvEG,MAAM,EAAE;MAAET,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE9C,OAAA,CAACL,aAAa;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE,CAAC;IAChEI,QAAQ,EAAE;MAAEV,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,IAAI;MAAEC,IAAI,eAAE9C,OAAA,CAACH,YAAY;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;EAChE,CAAC;;EAED;EACA,MAAMK,WAAW,GAAG;IAClBC,KAAK,EAAE;MAAEZ,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAC;IACvCY,MAAM,EAAE;MAAEb,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE;IAAK,CAAC;IACrCa,KAAK,EAAE;MAAEd,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK,CAAC;IACtCc,KAAK,EAAE;MAAEf,KAAK,EAAE,KAAK;MAAEC,IAAI,EAAE;IAAK;EACpC,CAAC;;EAED;EACA,MAAMe,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACnB,IAAI,EAAEoB,MAAM,kBACnBjE,OAAA,CAAC1B,KAAK;MAAA4F,QAAA,GACHxB,UAAU,CAACuB,MAAM,CAACtC,IAAI,CAAC,CAACmB,IAAI,eAC7B9C,OAAA,CAACE,IAAI;QAACiE,MAAM;QAAAD,QAAA,EAAErB;MAAI;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAEX,CAAC,EACD;IACEW,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAGrC,IAAI,iBACX3B,OAAA,CAACzB,GAAG;MAACqE,KAAK,EAAEF,UAAU,CAACf,IAAI,CAAC,CAACiB,KAAM;MAAAsB,QAAA,EAChCxB,UAAU,CAACf,IAAI,CAAC,CAACkB;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACN;IACDkB,OAAO,EAAE,CACP;MAAEvB,IAAI,EAAE,IAAI;MAAEwB,KAAK,EAAE;IAAO,CAAC,EAC7B;MAAExB,IAAI,EAAE,IAAI;MAAEwB,KAAK,EAAE;IAAS,CAAC,EAC/B;MAAExB,IAAI,EAAE,IAAI;MAAEwB,KAAK,EAAE;IAAe,CAAC,EACrC;MAAExB,IAAI,EAAE,IAAI;MAAEwB,KAAK,EAAE;IAAS,CAAC,EAC/B;MAAExB,IAAI,EAAE,IAAI;MAAEwB,KAAK,EAAE;IAAW,CAAC,CAClC;IACDC,QAAQ,EAAEA,CAACD,KAAK,EAAEJ,MAAM,KAAKA,MAAM,CAACtC,IAAI,KAAK0C;EAC/C,CAAC,EACD;IACER,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAGpC,KAAK,iBACZ5B,OAAA,CAACzB,GAAG;MAACqE,KAAK,EAAEW,WAAW,CAAC3B,KAAK,CAAC,CAACgB,KAAM;MAAAsB,QAAA,EAClCX,WAAW,CAAC3B,KAAK,CAAC,CAACiB;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB;EAET,CAAC,EACD;IACEW,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE;EACP,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGO,KAAK,IAAKA,KAAK,CAACC,cAAc,CAAC,CAAC;IACzCC,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3C,WAAW,GAAG4C,CAAC,CAAC5C;EACtC,CAAC,EACD;IACE8B,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGhC,QAAQ,iBACfhC,OAAA,CAACZ,IAAI;MAACwF,QAAQ;MAACP,KAAK,EAAErC,QAAS;MAAC6C,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC3D;IACDuB,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC1C,QAAQ,GAAG2C,CAAC,CAAC3C;EACnC,CAAC,EACD;IACE6B,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAG/B,SAAS,iBAChBjC,OAAA,CAACZ,IAAI;MAACwF,QAAQ;MAACP,KAAK,EAAEpC,SAAU;MAAC4C,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAA/B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC5D;IACDuB,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACzC,SAAS,GAAG0C,CAAC,CAAC1C;EACpC,CAAC,EACD;IACE4B,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,SAAS;IACdC,MAAM,EAAEA,CAACe,CAAC,EAAEd,MAAM,kBAChBjE,OAAA,CAAC1B,KAAK;MAAA4F,QAAA,gBACJlE,OAAA,CAAClB,OAAO;QAAC+E,KAAK,EAAC,0BAAM;QAAAK,QAAA,eACnBlE,OAAA,CAAC5B,MAAM;UACLuD,IAAI,EAAC,MAAM;UACXmB,IAAI,eAAE9C,OAAA,CAACP,WAAW;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtB8B,OAAO,EAAEA,CAAA,KAAMC,UAAU,CAAChB,MAAM;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVlD,OAAA,CAAClB,OAAO;QAAC+E,KAAK,EAAC,cAAI;QAAAK,QAAA,eACjBlE,OAAA,CAAC5B,MAAM;UACLuD,IAAI,EAAC,MAAM;UACXmB,IAAI,eAAE9C,OAAA,CAACT,YAAY;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB8B,OAAO,EAAEA,CAAA,KAAME,UAAU,CAACjB,MAAM;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVlD,OAAA,CAAClB,OAAO;QAAC+E,KAAK,EAAC,gBAAM;QAAAK,QAAA,eACnBlE,OAAA,CAAC5B,MAAM;UACLuD,IAAI,EAAC,MAAM;UACXmB,IAAI,eAAE9C,OAAA,CAACN,aAAa;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACxB8B,OAAO,EAAEA,CAAA,KAAMG,gBAAgB,CAAClB,MAAM;QAAE;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVlD,OAAA,CAACnB,UAAU;QACTgF,KAAK,EAAC,8DAAY;QAClBuB,SAAS,EAAEA,CAAA,KAAMC,YAAY,CAACpB,MAAM,CAACxD,EAAE,CAAE;QACzC6E,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAArB,QAAA,eAEflE,OAAA,CAAClB,OAAO;UAAC+E,KAAK,EAAC,cAAI;UAAAK,QAAA,eACjBlE,OAAA,CAAC5B,MAAM;YACLuD,IAAI,EAAC,MAAM;YACX6D,MAAM;YACN1C,IAAI,eAAE9C,OAAA,CAACR,cAAc;cAAAuD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;;EAED;EACA,MAAMuC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrE,iBAAiB,CAAC,IAAI,CAAC;IACvBG,IAAI,CAACmE,WAAW,CAAC,CAAC;IAClB1E,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMkE,UAAU,GAAIS,OAAO,IAAK;IAC9BvE,iBAAiB,CAACuE,OAAO,CAAC;IAC1BpE,IAAI,CAACqE,cAAc,CAAC;MAClB,GAAGD,OAAO;MACVvD,WAAW,EAAEuD,OAAO,CAACvD,WAAW,CAACyD,IAAI,CAAC,IAAI,CAAC;MAC3CxD,MAAM,EAAEsD,OAAO,CAACtD,MAAM,CAACwD,IAAI,CAAC,IAAI,CAAC;MACjCvD,OAAO,EAAEqD,OAAO,CAACrD,OAAO,CAACuD,IAAI,CAAC,IAAI,CAAC;MACnCtD,SAAS,EAAEoD,OAAO,CAACpD,SAAS,CAACsD,IAAI,CAAC,IAAI;IACxC,CAAC,CAAC;IACF7E,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMiE,UAAU,GAAIU,OAAO,IAAK;IAC9BrE,iBAAiB,CAACqE,OAAO,CAAC;IAC1BzE,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMiE,gBAAgB,GAAIQ,OAAO,IAAK;IACpC/G,OAAO,CAACkH,IAAI,CAAC,YAAYH,OAAO,CAACjE,IAAI,EAAE,CAAC;EAC1C,CAAC;EAED,MAAM2D,YAAY,GAAG,MAAO5E,EAAE,IAAK;IACjC,IAAI;MACF;MACA,MAAMxC,KAAK,CAAC8H,MAAM,CAAC,8BAA8BrF,SAAS,iBAAiBD,EAAE,EAAE,CAAC;;MAEhF;MACAG,WAAW,CAACD,QAAQ,CAACqF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACxF,EAAE,KAAKA,EAAE,CAAC,CAAC;MAC9C7B,OAAO,CAACsH,OAAO,CAAC,QAAQ,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BvH,OAAO,CAACuH,KAAK,CAAC,QAAQ,CAAC;IACzB;EACF,CAAC;EAED,MAAME,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM/E,IAAI,CAACgF,cAAc,CAAC,CAAC;MAC1CzF,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM0F,eAAe,GAAG;QACtB,GAAGF,MAAM;QACTlE,WAAW,EAAEkE,MAAM,CAAClE,WAAW,GAAGkE,MAAM,CAAClE,WAAW,CAACqE,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QACjGtE,MAAM,EAAEiE,MAAM,CAACjE,MAAM,GAAGiE,MAAM,CAACjE,MAAM,CAACoE,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QAClFrE,OAAO,EAAEgE,MAAM,CAAChE,OAAO,GAAGgE,MAAM,CAAChE,OAAO,CAACmE,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG,EAAE;QACrFpE,SAAS,EAAE+D,MAAM,CAAC/D,SAAS,GAAG+D,MAAM,CAAC/D,SAAS,CAACkE,KAAK,CAAC,IAAI,CAAC,CAACT,MAAM,CAACU,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,GAAG;MAC3F,CAAC;MAED,IAAIxF,cAAc,EAAE;QAClB;QACAP,WAAW,CAACD,QAAQ,CAACiG,GAAG,CAACX,CAAC,IACxBA,CAAC,CAACxF,EAAE,KAAKU,cAAc,CAACV,EAAE,GACtB;UAAE,GAAGwF,CAAC;UAAE,GAAGO;QAAgB,CAAC,GAC5BP,CACN,CAAC,CAAC;QACFrH,OAAO,CAACsH,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,MAAM;QACL;QACA,MAAMW,UAAU,GAAG;UACjBpG,EAAE,EAAEqG,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGP,eAAe;UAClBhE,MAAM,EAAE,QAAQ;UAChBC,SAAS,EAAE,IAAIqE,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACP,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAClD,CAAC;QACD7F,WAAW,CAAC,CAAC,GAAGD,QAAQ,EAAEkG,UAAU,CAAC,CAAC;QACtCjI,OAAO,CAACsH,OAAO,CAAC,QAAQ,CAAC;MAC3B;MAEAlF,eAAe,CAAC,KAAK,CAAC;MACtBO,IAAI,CAACmE,WAAW,CAAC,CAAC;IACpB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC,CAAC,SAAS;MACRrF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmG,aAAa,GAAGtG,QAAQ,CAACuG,MAAM;EACrC,MAAMC,KAAK,GAAGxG,QAAQ,CAACqF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtE,IAAI,KAAK,MAAM,CAAC,CAACuF,MAAM;EAC5D,MAAME,aAAa,GAAGzG,QAAQ,CAACqF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtE,IAAI,KAAK,cAAc,CAAC,CAACuF,MAAM;EAC5E,MAAMG,QAAQ,GAAG1G,QAAQ,CAACqF,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACtE,IAAI,KAAK,QAAQ,CAAC,CAACuF,MAAM;EAEjE,oBACElH,OAAA;IAAKsH,SAAS,EAAC,SAAS;IAAApD,QAAA,gBACtBlE,OAAA;MAAKsH,SAAS,EAAC,aAAa;MAAApD,QAAA,eAC1BlE,OAAA,CAACC,KAAK;QAAC2B,KAAK,EAAE,CAAE;QAAC0F,SAAS,EAAC,YAAY;QAAApD,QAAA,EAAC;MAAI;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC,eAGNlD,OAAA,CAACjB,GAAG;MAACwI,MAAM,EAAE,EAAG;MAAC1C,KAAK,EAAE;QAAE2C,YAAY,EAAE;MAAG,CAAE;MAAAtD,QAAA,gBAC3ClE,OAAA,CAAChB,GAAG;QAACyI,IAAI,EAAE,CAAE;QAAAvD,QAAA,eACXlE,OAAA,CAAC9B,IAAI;UAAAgG,QAAA,eACHlE,OAAA,CAACf,SAAS;YACR4E,KAAK,EAAC,0BAAM;YACZQ,KAAK,EAAE4C,aAAc;YACrBS,MAAM,eAAE1H,OAAA,CAACV,YAAY;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAAChB,GAAG;QAACyI,IAAI,EAAE,CAAE;QAAAvD,QAAA,eACXlE,OAAA,CAAC9B,IAAI;UAAAgG,QAAA,eACHlE,OAAA,CAACf,SAAS;YACR4E,KAAK,EAAC,cAAI;YACVQ,KAAK,EAAE8C,KAAM;YACbO,MAAM,eAAE1H,OAAA,CAACF,YAAY;cAAC+E,KAAK,EAAE;gBAAEjC,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAAChB,GAAG;QAACyI,IAAI,EAAE,CAAE;QAAAvD,QAAA,eACXlE,OAAA,CAAC9B,IAAI;UAAAgG,QAAA,eACHlE,OAAA,CAACf,SAAS;YACR4E,KAAK,EAAC,cAAI;YACVQ,KAAK,EAAE+C,aAAc;YACrBM,MAAM,eAAE1H,OAAA,CAACJ,cAAc;cAACiF,KAAK,EAAE;gBAAEjC,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlD,OAAA,CAAChB,GAAG;QAACyI,IAAI,EAAE,CAAE;QAAAvD,QAAA,eACXlE,OAAA,CAAC9B,IAAI;UAAAgG,QAAA,eACHlE,OAAA,CAACf,SAAS;YACR4E,KAAK,EAAC,cAAI;YACVQ,KAAK,EAAEgD,QAAS;YAChBK,MAAM,eAAE1H,OAAA,CAACV,YAAY;cAACuF,KAAK,EAAE;gBAAEjC,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENlD,OAAA,CAAC9B,IAAI;MAAAgG,QAAA,gBACHlE,OAAA;QAAKsH,SAAS,EAAC,SAAS;QAAApD,QAAA,eACtBlE,OAAA;UAAKsH,SAAS,EAAC,cAAc;UAAApD,QAAA,eAC3BlE,OAAA,CAAC5B,MAAM;YACLuD,IAAI,EAAC,SAAS;YACdmB,IAAI,eAAE9C,OAAA,CAACX,YAAY;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB8B,OAAO,EAAES,kBAAmB;YAAAvB,QAAA,EAC7B;UAED;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlD,OAAA,CAAC3B,KAAK;QACJuF,OAAO,EAAEA,OAAQ;QACjB+D,UAAU,EAAEhH,QAAS;QACrBiH,MAAM,EAAC,IAAI;QACX/G,OAAO,EAAEA,OAAQ;QACjBgH,UAAU,EAAE;UACVC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAlF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPlD,OAAA,CAACxB,KAAK;MACJqF,KAAK,EAAE1C,cAAc,GAAG,MAAM,GAAG,MAAO;MACxC+G,IAAI,EAAEnH,YAAa;MACnBoH,IAAI,EAAE9B,aAAc;MACpB+B,QAAQ,EAAEA,CAAA,KAAM;QACdpH,eAAe,CAAC,KAAK,CAAC;QACtBO,IAAI,CAACmE,WAAW,CAAC,CAAC;MACpB,CAAE;MACF2C,cAAc,EAAExH,OAAQ;MACxByH,KAAK,EAAE,GAAI;MAAApE,QAAA,eAEXlE,OAAA,CAACvB,IAAI;QACH8C,IAAI,EAAEA,IAAK;QACXgH,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE;UACb7G,IAAI,EAAE,MAAM;UACZC,KAAK,EAAE,QAAQ;UACfI,QAAQ,EAAE,CAAC;UACXC,SAAS,EAAE,CAAC;UACZO,MAAM,EAAE;QACV,CAAE;QAAA0B,QAAA,gBAEFlE,OAAA,CAACjB,GAAG;UAACwI,MAAM,EAAE,EAAG;UAAArD,QAAA,gBACdlE,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,MAAM;cACXgH,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsF,QAAA,eAEhDlE,OAAA,CAACtB,KAAK;gBAACmK,WAAW,EAAC;cAAS;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,MAAM;cACXgH,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsF,QAAA,eAEhDlE,OAAA,CAACrB,MAAM;gBAAAuF,QAAA,gBACLlE,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,MAAM;kBAAAH,QAAA,EAAC;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClD,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,QAAQ;kBAAAH,QAAA,EAAC;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClClD,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,cAAc;kBAAAH,QAAA,EAAC;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxClD,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,QAAQ;kBAAAH,QAAA,EAAC;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClClD,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,UAAU;kBAAAH,QAAA,EAAC;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAACjB,GAAG;UAACwI,MAAM,EAAE,EAAG;UAAArD,QAAA,gBACdlE,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACXlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,OAAO;cACZgH,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsF,QAAA,eAEhDlE,OAAA,CAACrB,MAAM;gBAAAuF,QAAA,gBACLlE,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,OAAO;kBAAAH,QAAA,EAAC;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjClD,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,QAAQ;kBAAAH,QAAA,EAAC;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClClD,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,OAAO;kBAAAH,QAAA,EAAC;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjClD,OAAA,CAACK,MAAM;kBAACgE,KAAK,EAAC,OAAO;kBAAAH,QAAA,EAAC;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACXlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,QAAQ;cACbgH,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsF,QAAA,eAEhDlE,OAAA,CAACtB,KAAK;gBAACmK,WAAW,EAAC;cAAS;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACXlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,aAAa;cAClBgH,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsF,QAAA,eAEhDlE,OAAA,CAACb,WAAW;gBAAC2J,GAAG,EAAE,CAAE;gBAACjE,KAAK,EAAE;kBAAEyD,KAAK,EAAE;gBAAO;cAAE;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAACjB,GAAG;UAACwI,MAAM,EAAE,EAAG;UAAArD,QAAA,gBACdlE,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,WAAW;cAChBgH,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsF,QAAA,eAEhDlE,OAAA,CAACtB,KAAK;gBAACmK,WAAW,EAAC;cAAa;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACXlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,UAAU;cACfgH,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsF,QAAA,eAEhDlE,OAAA,CAACZ,IAAI;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,CAAE;YAAAvD,QAAA,eACXlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,WAAW;cAChBgH,KAAK,EAAC,oBAAK;cACXC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhK,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAsF,QAAA,eAE/ClE,OAAA,CAACZ,IAAI;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAACvB,IAAI,CAACgK,IAAI;UACR/G,IAAI,EAAC,aAAa;UAClBgH,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEhK,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAsF,QAAA,eAEhDlE,OAAA,CAACI,QAAQ;YACP2I,IAAI,EAAE,CAAE;YACRF,WAAW,EAAC;UAAgB;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZlD,OAAA,CAACvB,IAAI,CAACgK,IAAI;UACR/G,IAAI,EAAC,SAAS;UACdgH,KAAK,EAAC,0BAAM;UAAAxE,QAAA,eAEZlE,OAAA,CAACI,QAAQ;YACP2I,IAAI,EAAE,CAAE;YACRF,WAAW,EAAC;UAAY;YAAA9F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZlD,OAAA,CAACjB,GAAG;UAACwI,MAAM,EAAE,EAAG;UAAArD,QAAA,gBACdlE,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,aAAa;cAClBgH,KAAK,EAAC,0BAAM;cACZM,OAAO,EAAC,sCAAQ;cAAA9E,QAAA,eAEhBlE,OAAA,CAACI,QAAQ;gBACP2I,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC;cAAoB;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,WAAW;cAChBgH,KAAK,EAAC,0BAAM;cACZM,OAAO,EAAC,sCAAQ;cAAA9E,QAAA,eAEhBlE,OAAA,CAACI,QAAQ;gBACP2I,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC;cAAqB;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENlD,OAAA,CAACjB,GAAG;UAACwI,MAAM,EAAE,EAAG;UAAArD,QAAA,gBACdlE,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,QAAQ;cACbgH,KAAK,EAAC,0BAAM;cACZM,OAAO,EAAC,sCAAQ;cAAA9E,QAAA,eAEhBlE,OAAA,CAACI,QAAQ;gBACP2I,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC;cAAe;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlD,OAAA,CAAChB,GAAG;YAACyI,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlE,OAAA,CAACvB,IAAI,CAACgK,IAAI;cACR/G,IAAI,EAAC,SAAS;cACdgH,KAAK,EAAC,0BAAM;cACZM,OAAO,EAAC,sCAAQ;cAAA9E,QAAA,eAEhBlE,OAAA,CAACI,QAAQ;gBACP2I,IAAI,EAAE,CAAE;gBACRF,WAAW,EAAC;cAAgB;gBAAA9F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRlD,OAAA,CAACxB,KAAK;MACJqF,KAAK,EAAC,0BAAM;MACZqE,IAAI,EAAEjH,kBAAmB;MACzBmH,QAAQ,EAAEA,CAAA,KAAMlH,qBAAqB,CAAC,KAAK,CAAE;MAC7C+H,MAAM,EAAE,cACNjJ,OAAA,CAAC5B,MAAM;QAAa4G,OAAO,EAAEA,CAAA,KAAM9D,qBAAqB,CAAC,KAAK,CAAE;QAAAgD,QAAA,EAAC;MAEjE,GAFY,OAAO;QAAAnB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFoF,KAAK,EAAE,GAAI;MAAApE,QAAA,EAEV7C,cAAc,iBACbrB,OAAA,CAACd,YAAY;QAACgK,QAAQ;QAACC,MAAM,EAAE,CAAE;QAAAjF,QAAA,gBAC/BlE,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACjB,IAAI,EAAE,CAAE;UAAAvD,QAAA,eACtClE,OAAA,CAAC1B,KAAK;YAAA4F,QAAA,GACHxB,UAAU,CAACrB,cAAc,CAACM,IAAI,CAAC,CAACmB,IAAI,eACrC9C,OAAA,CAACE,IAAI;cAACiE,MAAM;cAAAD,QAAA,EAAE7C,cAAc,CAACK;YAAI;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzClD,OAAA,CAACzB,GAAG;cAACqE,KAAK,EAAEF,UAAU,CAACrB,cAAc,CAACM,IAAI,CAAC,CAACiB,KAAM;cAAAsB,QAAA,EAC/CxB,UAAU,CAACrB,cAAc,CAACM,IAAI,CAAC,CAACkB;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACNlD,OAAA,CAACzB,GAAG;cAACqE,KAAK,EAAEW,WAAW,CAAClC,cAAc,CAACO,KAAK,CAAC,CAACgB,KAAM;cAAAsB,QAAA,EACjDX,WAAW,CAAClC,cAAc,CAACO,KAAK,CAAC,CAACiB;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eAEpBlD,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,cAAI;UAAAxE,QAAA,EAAE7C,cAAc,CAACS;QAAM;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACzElD,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAxE,QAAA,GAAA1D,qBAAA,GAAEa,cAAc,CAACU,WAAW,cAAAvB,qBAAA,uBAA1BA,qBAAA,CAA4BgE,cAAc,CAAC;QAAC;UAAAzB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAElGlD,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAxE,QAAA,EAAE7C,cAAc,CAACQ;QAAS;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAC9ElD,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAxE,QAAA,EAAE7C,cAAc,CAACoB;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAE9ElD,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAxE,QAAA,eAC7BlE,OAAA,CAACZ,IAAI;YAACwF,QAAQ;YAACP,KAAK,EAAEhD,cAAc,CAACW,QAAS;YAAC6C,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAG;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACpBlD,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,oBAAK;UAAAxE,QAAA,eAC5BlE,OAAA,CAACZ,IAAI;YAACwF,QAAQ;YAACP,KAAK,EAAEhD,cAAc,CAACY,SAAU;YAAC4C,KAAK,EAAE;cAAEC,QAAQ,EAAE;YAAG;UAAE;YAAA/B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eAEpBlD,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACjB,IAAI,EAAE,CAAE;UAAAvD,QAAA,eACtClE,OAAA,CAACG,SAAS;YAAA+D,QAAA,EAAE7C,cAAc,CAACa;UAAW;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,EAEnB7B,cAAc,CAACc,OAAO,iBACrBnC,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACjB,IAAI,EAAE,CAAE;UAAAvD,QAAA,eACtClE,OAAA,CAACG,SAAS;YAAA+D,QAAA,EAAE7C,cAAc,CAACc;UAAO;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CACpB,EAEA7B,cAAc,CAACe,WAAW,IAAIf,cAAc,CAACe,WAAW,CAAC8E,MAAM,GAAG,CAAC,iBAClElH,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACjB,IAAI,EAAE,CAAE;UAAAvD,QAAA,eACtClE,OAAA,CAAC1B,KAAK;YAAC8K,IAAI;YAAAlF,QAAA,EACR7C,cAAc,CAACe,WAAW,CAACwE,GAAG,CAAC,CAACyC,SAAS,EAAEC,KAAK,kBAC/CtJ,OAAA,CAACzB,GAAG;cAAaqE,KAAK,EAAC,MAAM;cAAAsB,QAAA,EAAEmF;YAAS,GAA9BC,KAAK;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA+B,CAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CACpB,EAEA7B,cAAc,CAACkB,SAAS,IAAIlB,cAAc,CAACkB,SAAS,CAAC2E,MAAM,GAAG,CAAC,iBAC9DlH,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACjB,IAAI,EAAE,CAAE;UAAAvD,QAAA,eACtClE,OAAA,CAAC1B,KAAK;YAAC8K,IAAI;YAAAlF,QAAA,EACR7C,cAAc,CAACkB,SAAS,CAACqE,GAAG,CAAC,CAAC2C,QAAQ,EAAED,KAAK,kBAC5CtJ,OAAA,CAACzB,GAAG;cAAaqE,KAAK,EAAC,OAAO;cAAAsB,QAAA,EAAEqF;YAAQ,GAA9BD,KAAK;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA+B,CAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CACpB,EAEA7B,cAAc,CAACgB,MAAM,IAAIhB,cAAc,CAACgB,MAAM,CAAC6E,MAAM,GAAG,CAAC,iBACxDlH,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACjB,IAAI,EAAE,CAAE;UAAAvD,QAAA,eACtClE,OAAA,CAAC1B,KAAK;YAAC8K,IAAI;YAAAlF,QAAA,EACR7C,cAAc,CAACgB,MAAM,CAACuE,GAAG,CAAC,CAAC4C,IAAI,EAAEF,KAAK,kBACrCtJ,OAAA,CAACzB,GAAG;cAAaqE,KAAK,EAAC,MAAM;cAAAsB,QAAA,EAAEsF;YAAI,GAAzBF,KAAK;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA0B,CAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CACpB,EAEA7B,cAAc,CAACiB,OAAO,IAAIjB,cAAc,CAACiB,OAAO,CAAC4E,MAAM,GAAG,CAAC,iBAC1DlH,OAAA,CAACd,YAAY,CAACuJ,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACjB,IAAI,EAAE,CAAE;UAAAvD,QAAA,eACtClE,OAAA,CAAC1B,KAAK;YAAC8K,IAAI;YAAAlF,QAAA,EACR7C,cAAc,CAACiB,OAAO,CAACsE,GAAG,CAAC,CAAC6C,KAAK,EAAEH,KAAK,kBACvCtJ,OAAA,CAACzB,GAAG;cAAaqE,KAAK,EAAC,KAAK;cAAAsB,QAAA,EAAEuF;YAAK,GAAzBH,KAAK;cAAAvG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAA0B,CAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CACpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAjpBID,WAAW;EAAA,QACWtC,SAAS,EAOpBS,IAAI,CAAC+C,OAAO;AAAA;AAAAkI,EAAA,GARvBpJ,WAAW;AAmpBjB,eAAeA,WAAW;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}