{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\ProjectDetail.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { Card, Button, Space, Typography, Row, Col, Statistic, Progress, Tag, Descriptions, message } from 'antd';\nimport { EditOutlined, SettingOutlined, ExportOutlined, BackwardOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst ProjectDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [project, setProject] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // 模拟项目数据\n  const mockProject = {\n    id: 1,\n    name: '仙侠传说',\n    title: '九天仙缘录',\n    author: '作者A',\n    type: 'xianxia',\n    status: 'writing',\n    summary: '这是一个关于修仙者在九天之上寻找仙缘的故事...',\n    description: '详细的项目描述...',\n    wordCount: 89000,\n    chapterCount: 45,\n    characterCount: 12,\n    factionCount: 8,\n    plotCount: 15,\n    progress: 65,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-15'\n  };\n  const loadProject = useCallback(async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setProject(mockProject);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      message.error('加载项目详情失败');\n      setLoading(false);\n    }\n  }, [mockProject]);\n  useEffect(() => {\n    loadProject();\n  }, [id, loadProject]);\n  const getStatusColor = status => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n  const getStatusText = status => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n  const getTypeText = type => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n  if (loading || !project) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-container\",\n      children: \"\\u52A0\\u8F7D\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(BackwardOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate('/projects'),\n          children: \"\\u8FD4\\u56DE\\u9879\\u76EE\\u5217\\u8868\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          className: \"page-title\",\n          children: project.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: getStatusColor(project.status),\n          children: getStatusText(project.status)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          children: getTypeText(project.type)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 111,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        style: {\n          marginTop: 16\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 40\n          }, this),\n          children: \"\\u7F16\\u8F91\\u9879\\u76EE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 25\n          }, this),\n          children: \"\\u9879\\u76EE\\u8BBE\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 25\n          }, this),\n          children: \"\\u5BFC\\u51FA\\u9879\\u76EE\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5B57\\u6570\",\n            value: project.wordCount,\n            formatter: value => `${(value / 10000).toFixed(1)}万`,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 140,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7AE0\\u8282\\u6570\",\n            value: project.chapterCount,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4EBA\\u7269\\u6570\",\n            value: project.characterCount,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5B8C\\u6210\\u8FDB\\u5EA6\",\n            value: project.progress,\n            suffix: \"%\",\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Progress, {\n            percent: project.progress,\n            size: \"small\",\n            status: project.progress === 100 ? 'success' : 'active',\n            style: {\n              marginTop: 8\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9879\\u76EE\\u6982\\u89C8\",\n      children: /*#__PURE__*/_jsxDEV(Descriptions, {\n        bordered: true,\n        column: 2,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9879\\u76EE\\u540D\\u79F0\",\n          children: project.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5C0F\\u8BF4\\u6807\\u9898\",\n          children: project.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4F5C\\u8005\",\n          children: project.author\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9879\\u76EE\\u7C7B\\u578B\",\n          children: getTypeText(project.type)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: project.createdAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u6700\\u540E\\u4FEE\\u6539\",\n          children: project.updatedAt\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9879\\u76EE\\u7B80\\u4ECB\",\n          span: 2,\n          children: project.summary\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8BE6\\u7EC6\\u63CF\\u8FF0\",\n          span: 2,\n          children: project.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u5FEB\\u901F\\u8BBF\\u95EE\",\n      style: {\n        marginTop: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: [16, 16],\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u5377\\u5B97\\u7BA1\\u7406\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u8FDB\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate(`/projects/${id}/volumes`),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              value: project.volumeCount || 0,\n              suffix: \"\\u4E2A\\u5377\\u5B97\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u7BA1\\u7406\\u5C0F\\u8BF4\\u5377\\u5B97\\u548C\\u7AE0\\u8282\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 216,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u5185\\u5BB9\\u7BA1\\u7406\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u8FDB\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate(`/projects/${id}/content`),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Statistic, {\n              value: project.characterCount + project.factionCount,\n              suffix: \"\\u4E2A\\u5185\\u5BB9\\u9879\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u7BA1\\u7406\\u89D2\\u8272\\u3001\\u52BF\\u529B\\u3001\\u5267\\u60C5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u8BBE\\u5B9A\\u7BA1\\u7406\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u8FDB\\u5165\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate(`/projects/${id}/settings`),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u4F53\\u7CFB\\u8BBE\\u5B9A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u7BA1\\u7406\\u4E16\\u754C\\u89C2\\u548C\\u5404\\u7C7B\\u4F53\\u7CFB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"AI\\u52A9\\u624B\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u4F7F\\u7528\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate('/ai-assistant'),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"AI\\u8F85\\u52A9\\u521B\\u4F5C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u667A\\u80FD\\u751F\\u6210\\u3001\\u7EED\\u5199\\u3001\\u5206\\u6790\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u65F6\\u95F4\\u7EBF\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u67E5\\u770B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate(`/projects/${id}/timeline`),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u9879\\u76EE\\u65F6\\u95F4\\u7EBF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u4E8B\\u4EF6\\u3001\\u53D1\\u5C55\\u3001\\u5386\\u53F2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          sm: 12,\n          md: 8,\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            title: \"\\u5173\\u7CFB\\u7F51\\u7EDC\",\n            extra: /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              children: \"\\u67E5\\u770B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 22\n            }, this),\n            onClick: () => navigate(`/projects/${id}/relations`),\n            style: {\n              cursor: 'pointer'\n            },\n            hoverable: true,\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u4EBA\\u7269\\u5173\\u7CFB\\u56FE\\u8C31\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u4EBA\\u7269\\u3001\\u52BF\\u529B\\u3001\\u5173\\u7CFB\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 5\n  }, this);\n};\n_s(ProjectDetail, \"9e4jNjzfeXeQ8QUEkcs089NjU1o=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = ProjectDetail;\nexport default ProjectDetail;\nvar _c;\n$RefreshReg$(_c, \"ProjectDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "Card", "<PERSON><PERSON>", "Space", "Typography", "Row", "Col", "Statistic", "Progress", "Tag", "Descriptions", "message", "EditOutlined", "SettingOutlined", "ExportOutlined", "BackwardOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "ProjectDetail", "_s", "id", "navigate", "project", "setProject", "loading", "setLoading", "mockProject", "name", "title", "author", "type", "status", "summary", "description", "wordCount", "chapterCount", "characterCount", "factionCount", "plotCount", "progress", "createdAt", "updatedAt", "loadProject", "setTimeout", "error", "getStatusColor", "colors", "planning", "writing", "reviewing", "completed", "published", "getStatusText", "texts", "getTypeText", "fantasy", "xianxia", "wuxia", "scifi", "modern", "historical", "romance", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "onClick", "level", "color", "style", "marginTop", "gutter", "marginBottom", "xs", "sm", "lg", "value", "formatter", "toFixed", "valueStyle", "suffix", "percent", "size", "bordered", "column", "<PERSON><PERSON>", "label", "span", "md", "extra", "cursor", "hoverable", "volumeCount", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/ProjectDetail.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport {\n  Card,\n  Button,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Statistic,\n  Progress,\n  Tag,\n  Descriptions,\n  message\n} from 'antd';\nimport {\n  EditOutlined,\n  SettingOutlined,\n  ExportOutlined,\n  BackwardOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\n\nconst ProjectDetail = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const [project, setProject] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // 模拟项目数据\n  const mockProject = {\n    id: 1,\n    name: '仙侠传说',\n    title: '九天仙缘录',\n    author: '作者A',\n    type: 'xianxia',\n    status: 'writing',\n    summary: '这是一个关于修仙者在九天之上寻找仙缘的故事...',\n    description: '详细的项目描述...',\n    wordCount: 89000,\n    chapterCount: 45,\n    characterCount: 12,\n    factionCount: 8,\n    plotCount: 15,\n    progress: 65,\n    createdAt: '2024-01-01',\n    updatedAt: '2024-01-15'\n  };\n\n  const loadProject = useCallback(async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setProject(mockProject);\n        setLoading(false);\n      }, 1000);\n    } catch (error) {\n      message.error('加载项目详情失败');\n      setLoading(false);\n    }\n  }, [mockProject]);\n\n  useEffect(() => {\n    loadProject();\n  }, [id, loadProject]);\n\n  const getStatusColor = (status) => {\n    const colors = {\n      planning: 'blue',\n      writing: 'green',\n      reviewing: 'orange',\n      completed: 'purple',\n      published: 'gold'\n    };\n    return colors[status] || 'default';\n  };\n\n  const getStatusText = (status) => {\n    const texts = {\n      planning: '规划中',\n      writing: '写作中',\n      reviewing: '审阅中',\n      completed: '已完成',\n      published: '已发布'\n    };\n    return texts[status] || status;\n  };\n\n  const getTypeText = (type) => {\n    const texts = {\n      fantasy: '奇幻',\n      xianxia: '仙侠',\n      wuxia: '武侠',\n      scifi: '科幻',\n      modern: '现代',\n      historical: '历史',\n      romance: '言情'\n    };\n    return texts[type] || type;\n  };\n\n  if (loading || !project) {\n    return <div className=\"loading-container\">加载中...</div>;\n  }\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Space>\n          <Button\n            icon={<BackwardOutlined />}\n            onClick={() => navigate('/projects')}\n          >\n            返回项目列表\n          </Button>\n          <Title level={2} className=\"page-title\">{project.name}</Title>\n          <Tag color={getStatusColor(project.status)}>\n            {getStatusText(project.status)}\n          </Tag>\n          <Tag>{getTypeText(project.type)}</Tag>\n        </Space>\n\n        <Space style={{ marginTop: 16 }}>\n          <Button type=\"primary\" icon={<EditOutlined />}>\n            编辑项目\n          </Button>\n          <Button icon={<SettingOutlined />}>\n            项目设置\n          </Button>\n          <Button icon={<ExportOutlined />}>\n            导出项目\n          </Button>\n        </Space>\n      </div>\n\n      {/* 项目统计 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总字数\"\n              value={project.wordCount}\n              formatter={(value) => `${(value / 10000).toFixed(1)}万`}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"章节数\"\n              value={project.chapterCount}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"人物数\"\n              value={project.characterCount}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"完成进度\"\n              value={project.progress}\n              suffix=\"%\"\n              valueStyle={{ color: '#fa8c16' }}\n            />\n            <Progress\n              percent={project.progress}\n              size=\"small\"\n              status={project.progress === 100 ? 'success' : 'active'}\n              style={{ marginTop: 8 }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 项目概览 */}\n      <Card title=\"项目概览\">\n        <Descriptions bordered column={2}>\n          <Descriptions.Item label=\"项目名称\">{project.name}</Descriptions.Item>\n          <Descriptions.Item label=\"小说标题\">{project.title}</Descriptions.Item>\n          <Descriptions.Item label=\"作者\">{project.author}</Descriptions.Item>\n          <Descriptions.Item label=\"项目类型\">{getTypeText(project.type)}</Descriptions.Item>\n          <Descriptions.Item label=\"创建时间\">{project.createdAt}</Descriptions.Item>\n          <Descriptions.Item label=\"最后修改\">{project.updatedAt}</Descriptions.Item>\n          <Descriptions.Item label=\"项目简介\" span={2}>\n            {project.summary}\n          </Descriptions.Item>\n          <Descriptions.Item label=\"详细描述\" span={2}>\n            {project.description}\n          </Descriptions.Item>\n        </Descriptions>\n      </Card>\n\n      {/* 快速访问 */}\n      <Card title=\"快速访问\" style={{ marginTop: 24 }}>\n        <Row gutter={[16, 16]}>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"卷宗管理\"\n              extra={<Button type=\"link\">进入</Button>}\n              onClick={() => navigate(`/projects/${id}/volumes`)}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Statistic value={project.volumeCount || 0} suffix=\"个卷宗\" />\n              <Text type=\"secondary\">管理小说卷宗和章节</Text>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"内容管理\"\n              extra={<Button type=\"link\">进入</Button>}\n              onClick={() => navigate(`/projects/${id}/content`)}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Statistic value={project.characterCount + project.factionCount} suffix=\"个内容项\" />\n              <Text type=\"secondary\">管理角色、势力、剧情</Text>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"设定管理\"\n              extra={<Button type=\"link\">进入</Button>}\n              onClick={() => navigate(`/projects/${id}/settings`)}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Text>体系设定</Text>\n              <br />\n              <Text type=\"secondary\">管理世界观和各类体系</Text>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"AI助手\"\n              extra={<Button type=\"link\">使用</Button>}\n              onClick={() => navigate('/ai-assistant')}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Text>AI辅助创作</Text>\n              <br />\n              <Text type=\"secondary\">智能生成、续写、分析</Text>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"时间线\"\n              extra={<Button type=\"link\">查看</Button>}\n              onClick={() => navigate(`/projects/${id}/timeline`)}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Text>项目时间线</Text>\n              <br />\n              <Text type=\"secondary\">事件、发展、历史</Text>\n            </Card>\n          </Col>\n          <Col xs={24} sm={12} md={8}>\n            <Card\n              title=\"关系网络\"\n              extra={<Button type=\"link\">查看</Button>}\n              onClick={() => navigate(`/projects/${id}/relations`)}\n              style={{ cursor: 'pointer' }}\n              hoverable\n            >\n              <Text>人物关系图谱</Text>\n              <br />\n              <Text type=\"secondary\">人物、势力、关系</Text>\n            </Card>\n          </Col>\n        </Row>\n      </Card>\n    </div>\n  );\n};\n\nexport default ProjectDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SACEC,IAAI,EACJC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,QAAQ,EACRC,GAAG,EACHC,YAAY,EACZC,OAAO,QACF,MAAM;AACb,SACEC,YAAY,EACZC,eAAe,EACfC,cAAc,EACdC,gBAAgB,QACX,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGf,UAAU;AAElC,MAAMgB,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC;EAAG,CAAC,GAAGvB,SAAS,CAAC,CAAC;EAC1B,MAAMwB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMgC,WAAW,GAAG;IAClBN,EAAE,EAAE,CAAC;IACLO,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,YAAY;IACzBC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,EAAE;IAChBC,cAAc,EAAE,EAAE;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,EAAE;IACbC,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,WAAW,GAAG9C,WAAW,CAAC,YAAY;IAC1C6B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACAkB,UAAU,CAAC,MAAM;QACfpB,UAAU,CAACG,WAAW,CAAC;QACvBD,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdnC,OAAO,CAACmC,KAAK,CAAC,UAAU,CAAC;MACzBnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACC,WAAW,CAAC,CAAC;EAEjB/B,SAAS,CAAC,MAAM;IACd+C,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACtB,EAAE,EAAEsB,WAAW,CAAC,CAAC;EAErB,MAAMG,cAAc,GAAId,MAAM,IAAK;IACjC,MAAMe,MAAM,GAAG;MACbC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,OAAO;MAChBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOL,MAAM,CAACf,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMqB,aAAa,GAAIrB,MAAM,IAAK;IAChC,MAAMsB,KAAK,GAAG;MACZN,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE,KAAK;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE;IACb,CAAC;IACD,OAAOE,KAAK,CAACtB,MAAM,CAAC,IAAIA,MAAM;EAChC,CAAC;EAED,MAAMuB,WAAW,GAAIxB,IAAI,IAAK;IAC5B,MAAMuB,KAAK,GAAG;MACZE,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,IAAI;MACbC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,OAAO,EAAE;IACX,CAAC;IACD,OAAOR,KAAK,CAACvB,IAAI,CAAC,IAAIA,IAAI;EAC5B,CAAC;EAED,IAAIN,OAAO,IAAI,CAACF,OAAO,EAAE;IACvB,oBAAOP,OAAA;MAAK+C,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAAC;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACxD;EAEA,oBACEpD,OAAA;IAAK+C,SAAS,EAAC,SAAS;IAAAC,QAAA,gBACtBhD,OAAA;MAAK+C,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BhD,OAAA,CAACd,KAAK;QAAA8D,QAAA,gBACJhD,OAAA,CAACf,MAAM;UACLoE,IAAI,eAAErD,OAAA,CAACF,gBAAgB;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,WAAW,CAAE;UAAA0C,QAAA,EACtC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpD,OAAA,CAACC,KAAK;UAACsD,KAAK,EAAE,CAAE;UAACR,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEzC,OAAO,CAACK;QAAI;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC9DpD,OAAA,CAACR,GAAG;UAACgE,KAAK,EAAE1B,cAAc,CAACvB,OAAO,CAACS,MAAM,CAAE;UAAAgC,QAAA,EACxCX,aAAa,CAAC9B,OAAO,CAACS,MAAM;QAAC;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACNpD,OAAA,CAACR,GAAG;UAAAwD,QAAA,EAAET,WAAW,CAAChC,OAAO,CAACQ,IAAI;QAAC;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,eAERpD,OAAA,CAACd,KAAK;QAACuE,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAG,CAAE;QAAAV,QAAA,gBAC9BhD,OAAA,CAACf,MAAM;UAAC8B,IAAI,EAAC,SAAS;UAACsC,IAAI,eAAErD,OAAA,CAACL,YAAY;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAE/C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpD,OAAA,CAACf,MAAM;UAACoE,IAAI,eAAErD,OAAA,CAACJ,eAAe;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAEnC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpD,OAAA,CAACf,MAAM;UAACoE,IAAI,eAAErD,OAAA,CAACH,cAAc;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNpD,OAAA,CAACZ,GAAG;MAACuE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACF,KAAK,EAAE;QAAEG,YAAY,EAAE;MAAG,CAAE;MAAAZ,QAAA,gBACjDhD,OAAA,CAACX,GAAG;QAACwE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBhD,OAAA,CAAChB,IAAI;UAAAgE,QAAA,eACHhD,OAAA,CAACV,SAAS;YACRuB,KAAK,EAAC,oBAAK;YACXmD,KAAK,EAAEzD,OAAO,CAACY,SAAU;YACzB8C,SAAS,EAAGD,KAAK,IAAK,GAAG,CAACA,KAAK,GAAG,KAAK,EAAEE,OAAO,CAAC,CAAC,CAAC,GAAI;YACvDC,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpD,OAAA,CAACX,GAAG;QAACwE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBhD,OAAA,CAAChB,IAAI;UAAAgE,QAAA,eACHhD,OAAA,CAACV,SAAS;YACRuB,KAAK,EAAC,oBAAK;YACXmD,KAAK,EAAEzD,OAAO,CAACa,YAAa;YAC5B+C,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpD,OAAA,CAACX,GAAG;QAACwE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBhD,OAAA,CAAChB,IAAI;UAAAgE,QAAA,eACHhD,OAAA,CAACV,SAAS;YACRuB,KAAK,EAAC,oBAAK;YACXmD,KAAK,EAAEzD,OAAO,CAACc,cAAe;YAC9B8C,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpD,OAAA,CAACX,GAAG;QAACwE,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAf,QAAA,eACzBhD,OAAA,CAAChB,IAAI;UAAAgE,QAAA,gBACHhD,OAAA,CAACV,SAAS;YACRuB,KAAK,EAAC,0BAAM;YACZmD,KAAK,EAAEzD,OAAO,CAACiB,QAAS;YACxB4C,MAAM,EAAC,GAAG;YACVD,UAAU,EAAE;cAAEX,KAAK,EAAE;YAAU;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFpD,OAAA,CAACT,QAAQ;YACP8E,OAAO,EAAE9D,OAAO,CAACiB,QAAS;YAC1B8C,IAAI,EAAC,OAAO;YACZtD,MAAM,EAAET,OAAO,CAACiB,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG,QAAS;YACxDiC,KAAK,EAAE;cAAEC,SAAS,EAAE;YAAE;UAAE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpD,OAAA,CAAChB,IAAI;MAAC6B,KAAK,EAAC,0BAAM;MAAAmC,QAAA,eAChBhD,OAAA,CAACP,YAAY;QAAC8E,QAAQ;QAACC,MAAM,EAAE,CAAE;QAAAxB,QAAA,gBAC/BhD,OAAA,CAACP,YAAY,CAACgF,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAA1B,QAAA,EAAEzC,OAAO,CAACK;QAAI;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAClEpD,OAAA,CAACP,YAAY,CAACgF,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAA1B,QAAA,EAAEzC,OAAO,CAACM;QAAK;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACnEpD,OAAA,CAACP,YAAY,CAACgF,IAAI;UAACC,KAAK,EAAC,cAAI;UAAA1B,QAAA,EAAEzC,OAAO,CAACO;QAAM;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAClEpD,OAAA,CAACP,YAAY,CAACgF,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAA1B,QAAA,EAAET,WAAW,CAAChC,OAAO,CAACQ,IAAI;QAAC;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAC/EpD,OAAA,CAACP,YAAY,CAACgF,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAA1B,QAAA,EAAEzC,OAAO,CAACkB;QAAS;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACvEpD,OAAA,CAACP,YAAY,CAACgF,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAA1B,QAAA,EAAEzC,OAAO,CAACmB;QAAS;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACvEpD,OAAA,CAACP,YAAY,CAACgF,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACC,IAAI,EAAE,CAAE;UAAA3B,QAAA,EACrCzC,OAAO,CAACU;QAAO;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACpBpD,OAAA,CAACP,YAAY,CAACgF,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACC,IAAI,EAAE,CAAE;UAAA3B,QAAA,EACrCzC,OAAO,CAACW;QAAW;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eAGPpD,OAAA,CAAChB,IAAI;MAAC6B,KAAK,EAAC,0BAAM;MAAC4C,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAG,CAAE;MAAAV,QAAA,eAC1ChD,OAAA,CAACZ,GAAG;QAACuE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;QAAAX,QAAA,gBACpBhD,OAAA,CAACX,GAAG;UAACwE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACzBhD,OAAA,CAAChB,IAAI;YACH6B,KAAK,EAAC,0BAAM;YACZgE,KAAK,eAAE7E,OAAA,CAACf,MAAM;cAAC8B,IAAI,EAAC,MAAM;cAAAiC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,UAAU,CAAE;YACnDoD,KAAK,EAAE;cAAEqB,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA/B,QAAA,gBAEThD,OAAA,CAACV,SAAS;cAAC0E,KAAK,EAAEzD,OAAO,CAACyE,WAAW,IAAI,CAAE;cAACZ,MAAM,EAAC;YAAK;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3DpD,OAAA,CAACE,IAAI;cAACa,IAAI,EAAC,WAAW;cAAAiC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpD,OAAA,CAACX,GAAG;UAACwE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACzBhD,OAAA,CAAChB,IAAI;YACH6B,KAAK,EAAC,0BAAM;YACZgE,KAAK,eAAE7E,OAAA,CAACf,MAAM;cAAC8B,IAAI,EAAC,MAAM;cAAAiC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,UAAU,CAAE;YACnDoD,KAAK,EAAE;cAAEqB,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA/B,QAAA,gBAEThD,OAAA,CAACV,SAAS;cAAC0E,KAAK,EAAEzD,OAAO,CAACc,cAAc,GAAGd,OAAO,CAACe,YAAa;cAAC8C,MAAM,EAAC;YAAM;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjFpD,OAAA,CAACE,IAAI;cAACa,IAAI,EAAC,WAAW;cAAAiC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpD,OAAA,CAACX,GAAG;UAACwE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACzBhD,OAAA,CAAChB,IAAI;YACH6B,KAAK,EAAC,0BAAM;YACZgE,KAAK,eAAE7E,OAAA,CAACf,MAAM;cAAC8B,IAAI,EAAC,MAAM;cAAAiC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,WAAW,CAAE;YACpDoD,KAAK,EAAE;cAAEqB,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA/B,QAAA,gBAEThD,OAAA,CAACE,IAAI;cAAA8C,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjBpD,OAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNpD,OAAA,CAACE,IAAI;cAACa,IAAI,EAAC,WAAW;cAAAiC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpD,OAAA,CAACX,GAAG;UAACwE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACzBhD,OAAA,CAAChB,IAAI;YACH6B,KAAK,EAAC,gBAAM;YACZgE,KAAK,eAAE7E,OAAA,CAACf,MAAM;cAAC8B,IAAI,EAAC,MAAM;cAAAiC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,eAAe,CAAE;YACzCmD,KAAK,EAAE;cAAEqB,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA/B,QAAA,gBAEThD,OAAA,CAACE,IAAI;cAAA8C,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnBpD,OAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNpD,OAAA,CAACE,IAAI;cAACa,IAAI,EAAC,WAAW;cAAAiC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpD,OAAA,CAACX,GAAG;UAACwE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACzBhD,OAAA,CAAChB,IAAI;YACH6B,KAAK,EAAC,oBAAK;YACXgE,KAAK,eAAE7E,OAAA,CAACf,MAAM;cAAC8B,IAAI,EAAC,MAAM;cAAAiC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,WAAW,CAAE;YACpDoD,KAAK,EAAE;cAAEqB,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA/B,QAAA,gBAEThD,OAAA,CAACE,IAAI;cAAA8C,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBpD,OAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNpD,OAAA,CAACE,IAAI;cAACa,IAAI,EAAC,WAAW;cAAAiC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACNpD,OAAA,CAACX,GAAG;UAACwE,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,EAAG;UAACc,EAAE,EAAE,CAAE;UAAA5B,QAAA,eACzBhD,OAAA,CAAChB,IAAI;YACH6B,KAAK,EAAC,0BAAM;YACZgE,KAAK,eAAE7E,OAAA,CAACf,MAAM;cAAC8B,IAAI,EAAC,MAAM;cAAAiC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAE;YACvCE,OAAO,EAAEA,CAAA,KAAMhD,QAAQ,CAAC,aAAaD,EAAE,YAAY,CAAE;YACrDoD,KAAK,EAAE;cAAEqB,MAAM,EAAE;YAAU,CAAE;YAC7BC,SAAS;YAAA/B,QAAA,gBAEThD,OAAA,CAACE,IAAI;cAAA8C,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnBpD,OAAA;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNpD,OAAA,CAACE,IAAI;cAACa,IAAI,EAAC,WAAW;cAAAiC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChD,EAAA,CAtQID,aAAa;EAAA,QACFrB,SAAS,EACPC,WAAW;AAAA;AAAAkG,EAAA,GAFxB9E,aAAa;AAwQnB,eAAeA,aAAa;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}