{"ast": null, "code": "var _jsxFileName = \"D:\\\\AI_project\\\\\\u5C0F\\u8BF4\\u67B6\\u6784\\u7BA1\\u7406\\u53CAAI\\u751F\\u6210\\u8F6F\\u4EF6\\\\frontend\\\\src\\\\pages\\\\DimensionStructure.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport { Card, Table, Button, Modal, Form, Input, Select, InputNumber, Space, Typography, Row, Col, Tag, Popconfirm, message, Tooltip, Statistic, Rate } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, GatewayOutlined, StarOutlined, ThunderboltOutlined, EyeOutlined, ClockCircleOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst DimensionStructure = () => {\n  _s();\n  const {\n    id: projectId\n  } = useParams();\n  const [dimensions, setDimensions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingDimension, setEditingDimension] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockDimensions = [{\n    id: 1,\n    name: '主物质界',\n    type: 'material',\n    level: 1,\n    stability: 5,\n    timeFlow: 1.0,\n    spaceSize: 'infinite',\n    lawStrength: 5,\n    energyDensity: 3,\n    accessMethods: ['自然存在'],\n    restrictions: ['无'],\n    inhabitants: ['人族', '妖族', '魔族'],\n    uniqueFeatures: ['完整的天地法则', '稳定的时空结构'],\n    description: '修仙世界的主要维度，所有生灵的家园',\n    connectedDimensions: ['灵界', '魔界'],\n    dangerLevel: 2,\n    discoveredBy: '天然存在',\n    status: 'stable'\n  }, {\n    id: 2,\n    name: '灵界',\n    type: 'spiritual',\n    level: 2,\n    stability: 4,\n    timeFlow: 0.5,\n    spaceSize: 'vast',\n    lawStrength: 4,\n    energyDensity: 5,\n    accessMethods: ['渡劫飞升', '空间裂缝'],\n    restrictions: ['需要元婴期以上修为'],\n    inhabitants: ['仙人', '灵兽', '天使'],\n    uniqueFeatures: ['灵气极度浓郁', '时间流速缓慢'],\n    description: '修仙者向往的高等维度，灵气充沛',\n    connectedDimensions: ['主物质界', '仙界'],\n    dangerLevel: 3,\n    discoveredBy: '古代仙人',\n    status: 'stable'\n  }, {\n    id: 3,\n    name: '虚空乱流',\n    type: 'chaotic',\n    level: 0,\n    stability: 1,\n    timeFlow: 'variable',\n    spaceSize: 'unknown',\n    lawStrength: 1,\n    energyDensity: 2,\n    accessMethods: ['空间撕裂', '意外传送'],\n    restrictions: ['极度危险'],\n    inhabitants: ['虚空生物', '迷失者'],\n    uniqueFeatures: ['时空混乱', '法则不稳定'],\n    description: '危险的混沌维度，充满未知的威胁',\n    connectedDimensions: ['所有维度'],\n    dangerLevel: 5,\n    discoveredBy: '意外发现',\n    status: 'chaotic'\n  }];\n  useEffect(() => {\n    loadDimensions();\n  }, [projectId]);\n  const loadDimensions = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setDimensions(mockDimensions);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载维度结构失败');\n      setLoading(false);\n    }\n  };\n  const handleAdd = () => {\n    setEditingDimension(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n  const handleEdit = dimension => {\n    var _dimension$accessMeth, _dimension$restrictio, _dimension$inhabitant, _dimension$uniqueFeat, _dimension$connectedD;\n    setEditingDimension(dimension);\n    form.setFieldsValue({\n      ...dimension,\n      accessMethods: (_dimension$accessMeth = dimension.accessMethods) === null || _dimension$accessMeth === void 0 ? void 0 : _dimension$accessMeth.join(', '),\n      restrictions: (_dimension$restrictio = dimension.restrictions) === null || _dimension$restrictio === void 0 ? void 0 : _dimension$restrictio.join(', '),\n      inhabitants: (_dimension$inhabitant = dimension.inhabitants) === null || _dimension$inhabitant === void 0 ? void 0 : _dimension$inhabitant.join(', '),\n      uniqueFeatures: (_dimension$uniqueFeat = dimension.uniqueFeatures) === null || _dimension$uniqueFeat === void 0 ? void 0 : _dimension$uniqueFeat.join(', '),\n      connectedDimensions: (_dimension$connectedD = dimension.connectedDimensions) === null || _dimension$connectedD === void 0 ? void 0 : _dimension$connectedD.join(', ')\n    });\n    setModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      // 调用后端API删除数据\n      await axios.delete(`/api/project-data/projects/${projectId}/data/dimension_structure/${id}`);\n\n      // 删除成功后从列表中移除\n      setDimensions(dimensions.filter(d => d.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      console.error('删除失败:', error);\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      var _values$accessMethods, _values$restrictions, _values$inhabitants, _values$uniqueFeature, _values$connectedDime;\n      const processedValues = {\n        name: values.name,\n        type: values.type,\n        level: values.level,\n        stability: values.stability,\n        timeFlow: values.timeFlow,\n        spaceSize: values.spaceSize,\n        lawStrength: values.lawStrength,\n        energyDensity: values.energyDensity,\n        accessMethods: ((_values$accessMethods = values.accessMethods) === null || _values$accessMethods === void 0 ? void 0 : _values$accessMethods.split(',').map(m => m.trim()).filter(m => m)) || [],\n        restrictions: ((_values$restrictions = values.restrictions) === null || _values$restrictions === void 0 ? void 0 : _values$restrictions.split(',').map(r => r.trim()).filter(r => r)) || [],\n        inhabitants: ((_values$inhabitants = values.inhabitants) === null || _values$inhabitants === void 0 ? void 0 : _values$inhabitants.split(',').map(i => i.trim()).filter(i => i)) || [],\n        uniqueFeatures: ((_values$uniqueFeature = values.uniqueFeatures) === null || _values$uniqueFeature === void 0 ? void 0 : _values$uniqueFeature.split(',').map(f => f.trim()).filter(f => f)) || [],\n        description: values.description,\n        connectedDimensions: ((_values$connectedDime = values.connectedDimensions) === null || _values$connectedDime === void 0 ? void 0 : _values$connectedDime.split(',').map(d => d.trim()).filter(d => d)) || [],\n        dangerLevel: values.dangerLevel,\n        discoveredBy: values.discoveredBy,\n        status: values.status\n      };\n      if (editingDimension) {\n        // 更新\n        setDimensions(dimensions.map(d => d.id === editingDimension.id ? {\n          ...d,\n          ...processedValues\n        } : d));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newDimension = {\n          id: Date.now(),\n          ...processedValues\n        };\n        setDimensions([...dimensions, newDimension]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n  const getTypeColor = type => {\n    const colors = {\n      material: 'blue',\n      spiritual: 'purple',\n      elemental: 'orange',\n      chaotic: 'red',\n      divine: 'gold',\n      shadow: 'black'\n    };\n    return colors[type] || 'default';\n  };\n  const getStatusColor = status => {\n    const colors = {\n      stable: 'green',\n      unstable: 'orange',\n      chaotic: 'red',\n      sealed: 'gray'\n    };\n    return colors[status] || 'default';\n  };\n  const columns = [{\n    title: '维度名称',\n    dataIndex: 'name',\n    key: 'name',\n    render: (text, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Text, {\n        strong: true,\n        children: text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tag, {\n        color: getTypeColor(record.type),\n        children: record.type === 'material' ? '物质' : record.type === 'spiritual' ? '灵界' : record.type === 'elemental' ? '元素' : record.type === 'chaotic' ? '混沌' : record.type === 'divine' ? '神界' : '阴影'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '维度等级',\n    dataIndex: 'level',\n    key: 'level',\n    render: level => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(StarOutlined, {\n        style: {\n          color: '#faad14'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: level\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.level - b.level\n  }, {\n    title: '稳定性',\n    dataIndex: 'stability',\n    key: 'stability',\n    render: stability => /*#__PURE__*/_jsxDEV(Rate, {\n      disabled: true,\n      value: stability,\n      style: {\n        fontSize: 16\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.stability - b.stability\n  }, {\n    title: '时间流速',\n    dataIndex: 'timeFlow',\n    key: 'timeFlow',\n    render: flow => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Text, {\n        children: typeof flow === 'number' ? `${flow}x` : flow\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '法则强度',\n    dataIndex: 'lawStrength',\n    key: 'lawStrength',\n    render: strength => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(ThunderboltOutlined, {\n        style: {\n          color: '#722ed1'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Rate, {\n        disabled: true,\n        value: strength,\n        style: {\n          fontSize: 16\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 282,\n      columnNumber: 9\n    }, this),\n    sorter: (a, b) => a.lawStrength - b.lawStrength\n  }, {\n    title: '危险等级',\n    dataIndex: 'dangerLevel',\n    key: 'dangerLevel',\n    render: level => {\n      const color = level >= 4 ? '#f5222d' : level >= 3 ? '#fa8c16' : level >= 2 ? '#faad14' : '#52c41a';\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: color,\n        children: [level, \"/5\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 16\n      }, this);\n    },\n    sorter: (a, b) => a.dangerLevel - b.dangerLevel\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: status === 'stable' ? '稳定' : status === 'unstable' ? '不稳定' : status === 'chaotic' ? '混沌' : '封印'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"text\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u5220\\u9664\\u8FD9\\u4E2A\\u7EF4\\u5EA6\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(Title, {\n        level: 2,\n        className: \"page-title\",\n        children: [/*#__PURE__*/_jsxDEV(GatewayOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), \" \\u7EF4\\u5EA6\\u7ED3\\u6784\\u7BA1\\u7406\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 17\n        }, this),\n        onClick: handleAdd,\n        children: \"\\u6DFB\\u52A0\\u7EF4\\u5EA6\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginBottom: 16\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7EF4\\u5EA6\\u603B\\u6570\",\n            value: dimensions.length,\n            prefix: /*#__PURE__*/_jsxDEV(GatewayOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u7A33\\u5B9A\\u7EF4\\u5EA6\",\n            value: dimensions.filter(d => d.status === 'stable').length,\n            prefix: /*#__PURE__*/_jsxDEV(StarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 372,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6DF7\\u6C8C\\u7EF4\\u5EA6\",\n            value: dimensions.filter(d => d.status === 'chaotic').length,\n            prefix: /*#__PURE__*/_jsxDEV(ThunderboltOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#f5222d'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        span: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5E73\\u5747\\u5371\\u9669\\u7B49\\u7EA7\",\n            value: dimensions.length > 0 ? (dimensions.reduce((sum, d) => sum + d.dangerLevel, 0) / dimensions.length).toFixed(1) : 0,\n            prefix: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#fa8c16'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: dimensions,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: total => `共 ${total} 个维度`\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingDimension ? '编辑维度' : '添加维度',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      confirmLoading: loading,\n      width: 900,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          type: 'material',\n          level: 1,\n          stability: 3,\n          timeFlow: 1.0,\n          spaceSize: 'large',\n          lawStrength: 3,\n          energyDensity: 3,\n          dangerLevel: 1,\n          status: 'stable'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u7EF4\\u5EA6\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入维度名称'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7EF4\\u5EA6\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"type\",\n              label: \"\\u7EF4\\u5EA6\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择维度类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"material\",\n                  children: \"\\u7269\\u8D28\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"spiritual\",\n                  children: \"\\u7075\\u754C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"elemental\",\n                  children: \"\\u5143\\u7D20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"chaotic\",\n                  children: \"\\u6DF7\\u6C8C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"divine\",\n                  children: \"\\u795E\\u754C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"shadow\",\n                  children: \"\\u9634\\u5F71\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 438,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"level\",\n              label: \"\\u7EF4\\u5EA6\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请输入维度等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                max: 10,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"stability\",\n              label: \"\\u7A33\\u5B9A\\u6027\",\n              rules: [{\n                required: true,\n                message: '请选择稳定性'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dangerLevel\",\n              label: \"\\u5371\\u9669\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请选择危险等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"timeFlow\",\n              label: \"\\u65F6\\u95F4\\u6D41\\u901F\",\n              rules: [{\n                required: true,\n                message: '请输入时间流速'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 0,\n                step: 0.1,\n                style: {\n                  width: '100%'\n                },\n                addonAfter: \"x\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"lawStrength\",\n              label: \"\\u6CD5\\u5219\\u5F3A\\u5EA6\",\n              rules: [{\n                required: true,\n                message: '请选择法则强度'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"energyDensity\",\n              label: \"\\u80FD\\u91CF\\u5BC6\\u5EA6\",\n              rules: [{\n                required: true,\n                message: '请选择能量密度'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                min: 1,\n                max: 5,\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 521,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"spaceSize\",\n              label: \"\\u7A7A\\u95F4\\u5927\\u5C0F\",\n              rules: [{\n                required: true,\n                message: '请选择空间大小'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"tiny\",\n                  children: \"\\u5FAE\\u5C0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 534,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"small\",\n                  children: \"\\u5C0F\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"medium\",\n                  children: \"\\u4E2D\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 536,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"large\",\n                  children: \"\\u5927\\u578B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 537,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"vast\",\n                  children: \"\\u5E7F\\u9614\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 538,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"infinite\",\n                  children: \"\\u65E0\\u9650\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"unknown\",\n                  children: \"\\u672A\\u77E5\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 540,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              rules: [{\n                required: true,\n                message: '请选择状态'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"stable\",\n                  children: \"\\u7A33\\u5B9A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 551,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"unstable\",\n                  children: \"\\u4E0D\\u7A33\\u5B9A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 552,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"chaotic\",\n                  children: \"\\u6DF7\\u6C8C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 553,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"sealed\",\n                  children: \"\\u5C01\\u5370\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 554,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 550,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 545,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"discoveredBy\",\n          label: \"\\u53D1\\u73B0\\u8005\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u53D1\\u73B0\\u8005\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"accessMethods\",\n          label: \"\\u8FDB\\u5165\\u65B9\\u6CD5\",\n          extra: \"\\u591A\\u4E2A\\u65B9\\u6CD5\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u6E21\\u52AB\\u98DE\\u5347, \\u7A7A\\u95F4\\u88C2\\u7F1D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"restrictions\",\n          label: \"\\u8FDB\\u5165\\u9650\\u5236\",\n          extra: \"\\u591A\\u4E2A\\u9650\\u5236\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u9700\\u8981\\u5143\\u5A74\\u671F\\u4EE5\\u4E0A\\u4FEE\\u4E3A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 569,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 568,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"inhabitants\",\n          label: \"\\u5C45\\u4F4F\\u8005\",\n          extra: \"\\u591A\\u4E2A\\u79CD\\u65CF\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u4ED9\\u4EBA, \\u7075\\u517D, \\u5929\\u4F7F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 573,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 572,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"uniqueFeatures\",\n          label: \"\\u72EC\\u7279\\u7279\\u5F81\",\n          extra: \"\\u591A\\u4E2A\\u7279\\u5F81\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u7075\\u6C14\\u6781\\u5EA6\\u6D53\\u90C1, \\u65F6\\u95F4\\u6D41\\u901F\\u7F13\\u6162\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 576,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"connectedDimensions\",\n          label: \"\\u8FDE\\u63A5\\u7EF4\\u5EA6\",\n          extra: \"\\u591A\\u4E2A\\u7EF4\\u5EA6\\u8BF7\\u7528\\u9017\\u53F7\\u5206\\u9694\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u5982\\uFF1A\\u4E3B\\u7269\\u8D28\\u754C, \\u4ED9\\u754C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u63CF\\u8FF0\",\n          rules: [{\n            required: true,\n            message: '请输入描述'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 3,\n            placeholder: \"\\u8BF7\\u63CF\\u8FF0\\u7EF4\\u5EA6\\u7684\\u7279\\u70B9\\u3001\\u73AF\\u5883\\u3001\\u5386\\u53F2\\u7B49\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 584,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 414,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 343,\n    columnNumber: 5\n  }, this);\n};\n_s(DimensionStructure, \"2YnPAO5CZehUJle6xfmxcVnOxCQ=\", false, function () {\n  return [useParams, Form.useForm];\n});\n_c = DimensionStructure;\nexport default DimensionStructure;\nvar _c;\n$RefreshReg$(_c, \"DimensionStructure\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "axios", "Card", "Table", "<PERSON><PERSON>", "Modal", "Form", "Input", "Select", "InputNumber", "Space", "Typography", "Row", "Col", "Tag", "Popconfirm", "message", "<PERSON><PERSON><PERSON>", "Statistic", "Rate", "PlusOutlined", "EditOutlined", "DeleteOutlined", "GatewayOutlined", "StarOutlined", "ThunderboltOutlined", "EyeOutlined", "ClockCircleOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "Option", "DimensionStructure", "_s", "id", "projectId", "dimensions", "setDimensions", "loading", "setLoading", "modalVisible", "setModalVisible", "editingDimension", "setEditingDimension", "form", "useForm", "mockDimensions", "name", "type", "level", "stability", "timeFlow", "spaceSize", "lawStrength", "energyDensity", "accessMethods", "restrictions", "inhabitants", "uniqueFeatures", "description", "connectedDimensions", "dangerLevel", "discoveredBy", "status", "loadDimensions", "setTimeout", "error", "handleAdd", "resetFields", "handleEdit", "dimension", "_dimension$accessMeth", "_dimension$restrictio", "_dimension$inhabitant", "_dimension$uniqueFeat", "_dimension$connectedD", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "handleDelete", "delete", "filter", "d", "success", "console", "handleSubmit", "values", "_values$accessMethods", "_values$restrictions", "_values$inhabitants", "_values$uniqueFeature", "_values$connectedDime", "processedValues", "split", "map", "m", "trim", "r", "i", "f", "newDimension", "Date", "now", "getTypeColor", "colors", "material", "spiritual", "elemental", "chaotic", "divine", "shadow", "getStatusColor", "stable", "unstable", "sealed", "columns", "title", "dataIndex", "key", "render", "text", "record", "children", "strong", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "style", "sorter", "a", "b", "disabled", "value", "fontSize", "flow", "strength", "_", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "className", "gutter", "marginBottom", "span", "size", "length", "prefix", "valueStyle", "reduce", "sum", "toFixed", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "open", "onCancel", "onOk", "submit", "confirmLoading", "width", "layout", "onFinish", "initialValues", "<PERSON><PERSON>", "label", "rules", "required", "placeholder", "min", "max", "step", "addonAfter", "extra", "rows", "_c", "$RefreshReg$"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/src/pages/DimensionStructure.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport axios from 'axios';\nimport {\n  Card,\n  Table,\n  Button,\n  Modal,\n  Form,\n  Input,\n  Select,\n  InputNumber,\n  Space,\n  Typography,\n  Row,\n  Col,\n  Tag,\n  Popconfirm,\n  message,\n  Tooltip,\n  Statistic,\n  Rate\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  GatewayOutlined,\n  StarOutlined,\n  ThunderboltOutlined,\n  EyeOutlined,\n  ClockCircleOutlined\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst DimensionStructure = () => {\n  const { id: projectId } = useParams();\n  const [dimensions, setDimensions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [editingDimension, setEditingDimension] = useState(null);\n  const [form] = Form.useForm();\n\n  // 模拟数据\n  const mockDimensions = [\n    {\n      id: 1,\n      name: '主物质界',\n      type: 'material',\n      level: 1,\n      stability: 5,\n      timeFlow: 1.0,\n      spaceSize: 'infinite',\n      lawStrength: 5,\n      energyDensity: 3,\n      accessMethods: ['自然存在'],\n      restrictions: ['无'],\n      inhabitants: ['人族', '妖族', '魔族'],\n      uniqueFeatures: ['完整的天地法则', '稳定的时空结构'],\n      description: '修仙世界的主要维度，所有生灵的家园',\n      connectedDimensions: ['灵界', '魔界'],\n      dangerLevel: 2,\n      discoveredBy: '天然存在',\n      status: 'stable'\n    },\n    {\n      id: 2,\n      name: '灵界',\n      type: 'spiritual',\n      level: 2,\n      stability: 4,\n      timeFlow: 0.5,\n      spaceSize: 'vast',\n      lawStrength: 4,\n      energyDensity: 5,\n      accessMethods: ['渡劫飞升', '空间裂缝'],\n      restrictions: ['需要元婴期以上修为'],\n      inhabitants: ['仙人', '灵兽', '天使'],\n      uniqueFeatures: ['灵气极度浓郁', '时间流速缓慢'],\n      description: '修仙者向往的高等维度，灵气充沛',\n      connectedDimensions: ['主物质界', '仙界'],\n      dangerLevel: 3,\n      discoveredBy: '古代仙人',\n      status: 'stable'\n    },\n    {\n      id: 3,\n      name: '虚空乱流',\n      type: 'chaotic',\n      level: 0,\n      stability: 1,\n      timeFlow: 'variable',\n      spaceSize: 'unknown',\n      lawStrength: 1,\n      energyDensity: 2,\n      accessMethods: ['空间撕裂', '意外传送'],\n      restrictions: ['极度危险'],\n      inhabitants: ['虚空生物', '迷失者'],\n      uniqueFeatures: ['时空混乱', '法则不稳定'],\n      description: '危险的混沌维度，充满未知的威胁',\n      connectedDimensions: ['所有维度'],\n      dangerLevel: 5,\n      discoveredBy: '意外发现',\n      status: 'chaotic'\n    }\n  ];\n\n  useEffect(() => {\n    loadDimensions();\n  }, [projectId]);\n\n  const loadDimensions = async () => {\n    setLoading(true);\n    try {\n      // 模拟API调用\n      setTimeout(() => {\n        setDimensions(mockDimensions);\n        setLoading(false);\n      }, 500);\n    } catch (error) {\n      message.error('加载维度结构失败');\n      setLoading(false);\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingDimension(null);\n    form.resetFields();\n    setModalVisible(true);\n  };\n\n  const handleEdit = (dimension) => {\n    setEditingDimension(dimension);\n    form.setFieldsValue({\n      ...dimension,\n      accessMethods: dimension.accessMethods?.join(', '),\n      restrictions: dimension.restrictions?.join(', '),\n      inhabitants: dimension.inhabitants?.join(', '),\n      uniqueFeatures: dimension.uniqueFeatures?.join(', '),\n      connectedDimensions: dimension.connectedDimensions?.join(', ')\n    });\n    setModalVisible(true);\n  };\n\n  const handleDelete = async (id) => {\n    try {\n      // 调用后端API删除数据\n      await axios.delete(`/api/project-data/projects/${projectId}/data/dimension_structure/${id}`);\n\n      // 删除成功后从列表中移除\n      setDimensions(dimensions.filter(d => d.id !== id));\n      message.success('删除成功');\n    } catch (error) {\n      console.error('删除失败:', error);\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    try {\n      const processedValues = {\n        name: values.name,\n        type: values.type,\n        level: values.level,\n        stability: values.stability,\n        timeFlow: values.timeFlow,\n        spaceSize: values.spaceSize,\n        lawStrength: values.lawStrength,\n        energyDensity: values.energyDensity,\n        accessMethods: values.accessMethods?.split(',').map(m => m.trim()).filter(m => m) || [],\n        restrictions: values.restrictions?.split(',').map(r => r.trim()).filter(r => r) || [],\n        inhabitants: values.inhabitants?.split(',').map(i => i.trim()).filter(i => i) || [],\n        uniqueFeatures: values.uniqueFeatures?.split(',').map(f => f.trim()).filter(f => f) || [],\n        description: values.description,\n        connectedDimensions: values.connectedDimensions?.split(',').map(d => d.trim()).filter(d => d) || [],\n        dangerLevel: values.dangerLevel,\n        discoveredBy: values.discoveredBy,\n        status: values.status\n      };\n\n      if (editingDimension) {\n        // 更新\n        setDimensions(dimensions.map(d =>\n          d.id === editingDimension.id ? { ...d, ...processedValues } : d\n        ));\n        message.success('更新成功');\n      } else {\n        // 新增\n        const newDimension = {\n          id: Date.now(),\n          ...processedValues\n        };\n        setDimensions([...dimensions, newDimension]);\n        message.success('添加成功');\n      }\n      setModalVisible(false);\n    } catch (error) {\n      message.error('保存失败');\n    }\n  };\n\n  const getTypeColor = (type) => {\n    const colors = {\n      material: 'blue',\n      spiritual: 'purple',\n      elemental: 'orange',\n      chaotic: 'red',\n      divine: 'gold',\n      shadow: 'black'\n    };\n    return colors[type] || 'default';\n  };\n\n  const getStatusColor = (status) => {\n    const colors = {\n      stable: 'green',\n      unstable: 'orange',\n      chaotic: 'red',\n      sealed: 'gray'\n    };\n    return colors[status] || 'default';\n  };\n\n  const columns = [\n    {\n      title: '维度名称',\n      dataIndex: 'name',\n      key: 'name',\n      render: (text, record) => (\n        <Space>\n          <Text strong>{text}</Text>\n          <Tag color={getTypeColor(record.type)}>\n            {record.type === 'material' ? '物质' :\n             record.type === 'spiritual' ? '灵界' :\n             record.type === 'elemental' ? '元素' :\n             record.type === 'chaotic' ? '混沌' :\n             record.type === 'divine' ? '神界' : '阴影'}\n          </Tag>\n        </Space>\n      )\n    },\n    {\n      title: '维度等级',\n      dataIndex: 'level',\n      key: 'level',\n      render: (level) => (\n        <Space>\n          <StarOutlined style={{ color: '#faad14' }} />\n          <Text>{level}</Text>\n        </Space>\n      ),\n      sorter: (a, b) => a.level - b.level\n    },\n    {\n      title: '稳定性',\n      dataIndex: 'stability',\n      key: 'stability',\n      render: (stability) => (\n        <Rate disabled value={stability} style={{ fontSize: 16 }} />\n      ),\n      sorter: (a, b) => a.stability - b.stability\n    },\n    {\n      title: '时间流速',\n      dataIndex: 'timeFlow',\n      key: 'timeFlow',\n      render: (flow) => (\n        <Space>\n          <ClockCircleOutlined />\n          <Text>{typeof flow === 'number' ? `${flow}x` : flow}</Text>\n        </Space>\n      )\n    },\n    {\n      title: '法则强度',\n      dataIndex: 'lawStrength',\n      key: 'lawStrength',\n      render: (strength) => (\n        <Space>\n          <ThunderboltOutlined style={{ color: '#722ed1' }} />\n          <Rate disabled value={strength} style={{ fontSize: 16 }} />\n        </Space>\n      ),\n      sorter: (a, b) => a.lawStrength - b.lawStrength\n    },\n    {\n      title: '危险等级',\n      dataIndex: 'dangerLevel',\n      key: 'dangerLevel',\n      render: (level) => {\n        const color = level >= 4 ? '#f5222d' : level >= 3 ? '#fa8c16' : level >= 2 ? '#faad14' : '#52c41a';\n        return <Tag color={color}>{level}/5</Tag>;\n      },\n      sorter: (a, b) => a.dangerLevel - b.dangerLevel\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      render: (status) => (\n        <Tag color={getStatusColor(status)}>\n          {status === 'stable' ? '稳定' :\n           status === 'unstable' ? '不稳定' :\n           status === 'chaotic' ? '混沌' : '封印'}\n        </Tag>\n      )\n    },\n    {\n      title: '操作',\n      key: 'action',\n      render: (_, record) => (\n        <Space>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"text\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定删除这个维度吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"text\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      )\n    }\n  ];\n\n  return (\n    <div className=\"fade-in\">\n      <div className=\"page-header\">\n        <Title level={2} className=\"page-title\">\n          <GatewayOutlined /> 维度结构管理\n        </Title>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleAdd}\n        >\n          添加维度\n        </Button>\n      </div>\n\n      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"维度总数\"\n              value={dimensions.length}\n              prefix={<GatewayOutlined />}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"稳定维度\"\n              value={dimensions.filter(d => d.status === 'stable').length}\n              prefix={<StarOutlined />}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"混沌维度\"\n              value={dimensions.filter(d => d.status === 'chaotic').length}\n              prefix={<ThunderboltOutlined />}\n              valueStyle={{ color: '#f5222d' }}\n            />\n          </Card>\n        </Col>\n        <Col span={6}>\n          <Card size=\"small\">\n            <Statistic\n              title=\"平均危险等级\"\n              value={dimensions.length > 0 ? (dimensions.reduce((sum, d) => sum + d.dangerLevel, 0) / dimensions.length).toFixed(1) : 0}\n              prefix={<EyeOutlined />}\n              valueStyle={{ color: '#fa8c16' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={dimensions}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total) => `共 ${total} 个维度`\n          }}\n        />\n      </Card>\n\n      <Modal\n        title={editingDimension ? '编辑维度' : '添加维度'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        confirmLoading={loading}\n        width={900}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            type: 'material',\n            level: 1,\n            stability: 3,\n            timeFlow: 1.0,\n            spaceSize: 'large',\n            lawStrength: 3,\n            energyDensity: 3,\n            dangerLevel: 1,\n            status: 'stable'\n          }}\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"维度名称\"\n                rules={[{ required: true, message: '请输入维度名称' }]}\n              >\n                <Input placeholder=\"请输入维度名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"type\"\n                label=\"维度类型\"\n                rules={[{ required: true, message: '请选择维度类型' }]}\n              >\n                <Select>\n                  <Option value=\"material\">物质</Option>\n                  <Option value=\"spiritual\">灵界</Option>\n                  <Option value=\"elemental\">元素</Option>\n                  <Option value=\"chaotic\">混沌</Option>\n                  <Option value=\"divine\">神界</Option>\n                  <Option value=\"shadow\">阴影</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"level\"\n                label=\"维度等级\"\n                rules={[{ required: true, message: '请输入维度等级' }]}\n              >\n                <InputNumber min={0} max={10} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"stability\"\n                label=\"稳定性\"\n                rules={[{ required: true, message: '请选择稳定性' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"dangerLevel\"\n                label=\"危险等级\"\n                rules={[{ required: true, message: '请选择危险等级' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={8}>\n              <Form.Item\n                name=\"timeFlow\"\n                label=\"时间流速\"\n                rules={[{ required: true, message: '请输入时间流速' }]}\n              >\n                <InputNumber min={0} step={0.1} style={{ width: '100%' }} addonAfter=\"x\" />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"lawStrength\"\n                label=\"法则强度\"\n                rules={[{ required: true, message: '请选择法则强度' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col span={8}>\n              <Form.Item\n                name=\"energyDensity\"\n                label=\"能量密度\"\n                rules={[{ required: true, message: '请选择能量密度' }]}\n              >\n                <InputNumber min={1} max={5} style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"spaceSize\"\n                label=\"空间大小\"\n                rules={[{ required: true, message: '请选择空间大小' }]}\n              >\n                <Select>\n                  <Option value=\"tiny\">微小</Option>\n                  <Option value=\"small\">小型</Option>\n                  <Option value=\"medium\">中型</Option>\n                  <Option value=\"large\">大型</Option>\n                  <Option value=\"vast\">广阔</Option>\n                  <Option value=\"infinite\">无限</Option>\n                  <Option value=\"unknown\">未知</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                rules={[{ required: true, message: '请选择状态' }]}\n              >\n                <Select>\n                  <Option value=\"stable\">稳定</Option>\n                  <Option value=\"unstable\">不稳定</Option>\n                  <Option value=\"chaotic\">混沌</Option>\n                  <Option value=\"sealed\">封印</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item name=\"discoveredBy\" label=\"发现者\">\n            <Input placeholder=\"请输入发现者\" />\n          </Form.Item>\n\n          <Form.Item name=\"accessMethods\" label=\"进入方法\" extra=\"多个方法请用逗号分隔\">\n            <Input placeholder=\"如：渡劫飞升, 空间裂缝\" />\n          </Form.Item>\n\n          <Form.Item name=\"restrictions\" label=\"进入限制\" extra=\"多个限制请用逗号分隔\">\n            <Input placeholder=\"如：需要元婴期以上修为\" />\n          </Form.Item>\n\n          <Form.Item name=\"inhabitants\" label=\"居住者\" extra=\"多个种族请用逗号分隔\">\n            <Input placeholder=\"如：仙人, 灵兽, 天使\" />\n          </Form.Item>\n\n          <Form.Item name=\"uniqueFeatures\" label=\"独特特征\" extra=\"多个特征请用逗号分隔\">\n            <Input placeholder=\"如：灵气极度浓郁, 时间流速缓慢\" />\n          </Form.Item>\n\n          <Form.Item name=\"connectedDimensions\" label=\"连接维度\" extra=\"多个维度请用逗号分隔\">\n            <Input placeholder=\"如：主物质界, 仙界\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"描述\"\n            rules={[{ required: true, message: '请输入描述' }]}\n          >\n            <TextArea rows={3} placeholder=\"请描述维度的特点、环境、历史等\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default DimensionStructure;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,OAAO,EACPC,OAAO,EACPC,SAAS,EACTC,IAAI,QACC,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACXC,mBAAmB,QACd,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAS,CAAC,GAAGzB,KAAK;AAC1B,MAAM;EAAE0B;AAAO,CAAC,GAAGzB,MAAM;AAEzB,MAAM0B,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM;IAAEC,EAAE,EAAEC;EAAU,CAAC,GAAGrC,SAAS,CAAC,CAAC;EACrC,MAAM,CAACsC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4C,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACgD,IAAI,CAAC,GAAGxC,IAAI,CAACyC,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,cAAc,GAAG,CACrB;IACEZ,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,UAAU;IAChBC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,UAAU;IACrBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC,MAAM,CAAC;IACvBC,YAAY,EAAE,CAAC,GAAG,CAAC;IACnBC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/BC,cAAc,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;IACtCC,WAAW,EAAE,mBAAmB;IAChCC,mBAAmB,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;IACjCC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,MAAM;IACpBC,MAAM,EAAE;EACV,CAAC,EACD;IACE7B,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,IAAI;IACVC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,GAAG;IACbC,SAAS,EAAE,MAAM;IACjBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAC/BC,YAAY,EAAE,CAAC,WAAW,CAAC;IAC3BC,WAAW,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC/BC,cAAc,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IACpCC,WAAW,EAAE,iBAAiB;IAC9BC,mBAAmB,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;IACnCC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,MAAM;IACpBC,MAAM,EAAE;EACV,CAAC,EACD;IACE7B,EAAE,EAAE,CAAC;IACLa,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,CAAC;IACRC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,SAAS;IACpBC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,aAAa,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;IAC/BC,YAAY,EAAE,CAAC,MAAM,CAAC;IACtBC,WAAW,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;IAC5BC,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;IACjCC,WAAW,EAAE,iBAAiB;IAC9BC,mBAAmB,EAAE,CAAC,MAAM,CAAC;IAC7BC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,MAAM;IACpBC,MAAM,EAAE;EACV,CAAC,CACF;EAEDlE,SAAS,CAAC,MAAM;IACdmE,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC7B,SAAS,CAAC,CAAC;EAEf,MAAM6B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCzB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA0B,UAAU,CAAC,MAAM;QACf5B,aAAa,CAACS,cAAc,CAAC;QAC7BP,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC,OAAO2B,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,UAAU,CAAC;MACzB3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,SAAS,GAAGA,CAAA,KAAM;IACtBxB,mBAAmB,CAAC,IAAI,CAAC;IACzBC,IAAI,CAACwB,WAAW,CAAC,CAAC;IAClB3B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAM4B,UAAU,GAAIC,SAAS,IAAK;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IAChChC,mBAAmB,CAAC2B,SAAS,CAAC;IAC9B1B,IAAI,CAACgC,cAAc,CAAC;MAClB,GAAGN,SAAS;MACZf,aAAa,GAAAgB,qBAAA,GAAED,SAAS,CAACf,aAAa,cAAAgB,qBAAA,uBAAvBA,qBAAA,CAAyBM,IAAI,CAAC,IAAI,CAAC;MAClDrB,YAAY,GAAAgB,qBAAA,GAAEF,SAAS,CAACd,YAAY,cAAAgB,qBAAA,uBAAtBA,qBAAA,CAAwBK,IAAI,CAAC,IAAI,CAAC;MAChDpB,WAAW,GAAAgB,qBAAA,GAAEH,SAAS,CAACb,WAAW,cAAAgB,qBAAA,uBAArBA,qBAAA,CAAuBI,IAAI,CAAC,IAAI,CAAC;MAC9CnB,cAAc,GAAAgB,qBAAA,GAAEJ,SAAS,CAACZ,cAAc,cAAAgB,qBAAA,uBAAxBA,qBAAA,CAA0BG,IAAI,CAAC,IAAI,CAAC;MACpDjB,mBAAmB,GAAAe,qBAAA,GAAEL,SAAS,CAACV,mBAAmB,cAAAe,qBAAA,uBAA7BA,qBAAA,CAA+BE,IAAI,CAAC,IAAI;IAC/D,CAAC,CAAC;IACFpC,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMqC,YAAY,GAAG,MAAO5C,EAAE,IAAK;IACjC,IAAI;MACF;MACA,MAAMnC,KAAK,CAACgF,MAAM,CAAC,8BAA8B5C,SAAS,6BAA6BD,EAAE,EAAE,CAAC;;MAE5F;MACAG,aAAa,CAACD,UAAU,CAAC4C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC/C,EAAE,KAAKA,EAAE,CAAC,CAAC;MAClDpB,OAAO,CAACoE,OAAO,CAAC,MAAM,CAAC;IACzB,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdiB,OAAO,CAACjB,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BpD,OAAO,CAACoD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAOC,MAAM,IAAK;IACrC,IAAI;MAAA,IAAAC,qBAAA,EAAAC,oBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA;MACF,MAAMC,eAAe,GAAG;QACtB5C,IAAI,EAAEsC,MAAM,CAACtC,IAAI;QACjBC,IAAI,EAAEqC,MAAM,CAACrC,IAAI;QACjBC,KAAK,EAAEoC,MAAM,CAACpC,KAAK;QACnBC,SAAS,EAAEmC,MAAM,CAACnC,SAAS;QAC3BC,QAAQ,EAAEkC,MAAM,CAAClC,QAAQ;QACzBC,SAAS,EAAEiC,MAAM,CAACjC,SAAS;QAC3BC,WAAW,EAAEgC,MAAM,CAAChC,WAAW;QAC/BC,aAAa,EAAE+B,MAAM,CAAC/B,aAAa;QACnCC,aAAa,EAAE,EAAA+B,qBAAA,GAAAD,MAAM,CAAC9B,aAAa,cAAA+B,qBAAA,uBAApBA,qBAAA,CAAsBM,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACf,MAAM,CAACc,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QACvFtC,YAAY,EAAE,EAAA+B,oBAAA,GAAAF,MAAM,CAAC7B,YAAY,cAAA+B,oBAAA,uBAAnBA,oBAAA,CAAqBK,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACG,CAAC,IAAIA,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAACf,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QACrFvC,WAAW,EAAE,EAAA+B,mBAAA,GAAAH,MAAM,CAAC5B,WAAW,cAAA+B,mBAAA,uBAAlBA,mBAAA,CAAoBI,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACI,CAAC,IAAIA,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAACf,MAAM,CAACiB,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QACnFvC,cAAc,EAAE,EAAA+B,qBAAA,GAAAJ,MAAM,CAAC3B,cAAc,cAAA+B,qBAAA,uBAArBA,qBAAA,CAAuBG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACK,CAAC,IAAIA,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC,CAACf,MAAM,CAACkB,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QACzFvC,WAAW,EAAE0B,MAAM,CAAC1B,WAAW;QAC/BC,mBAAmB,EAAE,EAAA8B,qBAAA,GAAAL,MAAM,CAACzB,mBAAmB,cAAA8B,qBAAA,uBAA1BA,qBAAA,CAA4BE,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACZ,CAAC,IAAIA,CAAC,CAACc,IAAI,CAAC,CAAC,CAAC,CAACf,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC,KAAI,EAAE;QACnGpB,WAAW,EAAEwB,MAAM,CAACxB,WAAW;QAC/BC,YAAY,EAAEuB,MAAM,CAACvB,YAAY;QACjCC,MAAM,EAAEsB,MAAM,CAACtB;MACjB,CAAC;MAED,IAAIrB,gBAAgB,EAAE;QACpB;QACAL,aAAa,CAACD,UAAU,CAACyD,GAAG,CAACZ,CAAC,IAC5BA,CAAC,CAAC/C,EAAE,KAAKQ,gBAAgB,CAACR,EAAE,GAAG;UAAE,GAAG+C,CAAC;UAAE,GAAGU;QAAgB,CAAC,GAAGV,CAChE,CAAC,CAAC;QACFnE,OAAO,CAACoE,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL;QACA,MAAMiB,YAAY,GAAG;UACnBjE,EAAE,EAAEkE,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,GAAGV;QACL,CAAC;QACDtD,aAAa,CAAC,CAAC,GAAGD,UAAU,EAAE+D,YAAY,CAAC,CAAC;QAC5CrF,OAAO,CAACoE,OAAO,CAAC,MAAM,CAAC;MACzB;MACAzC,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdpD,OAAO,CAACoD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMoC,YAAY,GAAItD,IAAI,IAAK;IAC7B,MAAMuD,MAAM,GAAG;MACbC,QAAQ,EAAE,MAAM;MAChBC,SAAS,EAAE,QAAQ;MACnBC,SAAS,EAAE,QAAQ;MACnBC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,MAAM;MACdC,MAAM,EAAE;IACV,CAAC;IACD,OAAON,MAAM,CAACvD,IAAI,CAAC,IAAI,SAAS;EAClC,CAAC;EAED,MAAM8D,cAAc,GAAI/C,MAAM,IAAK;IACjC,MAAMwC,MAAM,GAAG;MACbQ,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE,QAAQ;MAClBL,OAAO,EAAE,KAAK;MACdM,MAAM,EAAE;IACV,CAAC;IACD,OAAOV,MAAM,CAACxC,MAAM,CAAC,IAAI,SAAS;EACpC,CAAC;EAED,MAAMmD,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,MAAM,EAAEA,CAACC,IAAI,EAAEC,MAAM,kBACnB7F,OAAA,CAACnB,KAAK;MAAAiH,QAAA,gBACJ9F,OAAA,CAACE,IAAI;QAAC6F,MAAM;QAAAD,QAAA,EAAEF;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC1BnG,OAAA,CAACf,GAAG;QAACmH,KAAK,EAAEzB,YAAY,CAACkB,MAAM,CAACxE,IAAI,CAAE;QAAAyE,QAAA,EACnCD,MAAM,CAACxE,IAAI,KAAK,UAAU,GAAG,IAAI,GACjCwE,MAAM,CAACxE,IAAI,KAAK,WAAW,GAAG,IAAI,GAClCwE,MAAM,CAACxE,IAAI,KAAK,WAAW,GAAG,IAAI,GAClCwE,MAAM,CAACxE,IAAI,KAAK,SAAS,GAAG,IAAI,GAChCwE,MAAM,CAACxE,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG;MAAI;QAAA2E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,MAAM,EAAGrE,KAAK,iBACZtB,OAAA,CAACnB,KAAK;MAAAiH,QAAA,gBACJ9F,OAAA,CAACL,YAAY;QAAC0G,KAAK,EAAE;UAAED,KAAK,EAAE;QAAU;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7CnG,OAAA,CAACE,IAAI;QAAA4F,QAAA,EAAExE;MAAK;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CACR;IACDG,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACjF,KAAK,GAAGkF,CAAC,CAAClF;EAChC,CAAC,EACD;IACEkE,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAGpE,SAAS,iBAChBvB,OAAA,CAACV,IAAI;MAACmH,QAAQ;MAACC,KAAK,EAAEnF,SAAU;MAAC8E,KAAK,EAAE;QAAEM,QAAQ,EAAE;MAAG;IAAE;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC5D;IACDG,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAChF,SAAS,GAAGiF,CAAC,CAACjF;EACpC,CAAC,EACD;IACEiE,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,MAAM,EAAGiB,IAAI,iBACX5G,OAAA,CAACnB,KAAK;MAAAiH,QAAA,gBACJ9F,OAAA,CAACF,mBAAmB;QAAAkG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACvBnG,OAAA,CAACE,IAAI;QAAA4F,QAAA,EAAE,OAAOc,IAAI,KAAK,QAAQ,GAAG,GAAGA,IAAI,GAAG,GAAGA;MAAI;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD;EAEX,CAAC,EACD;IACEX,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGkB,QAAQ,iBACf7G,OAAA,CAACnB,KAAK;MAAAiH,QAAA,gBACJ9F,OAAA,CAACJ,mBAAmB;QAACyG,KAAK,EAAE;UAAED,KAAK,EAAE;QAAU;MAAE;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACpDnG,OAAA,CAACV,IAAI;QAACmH,QAAQ;QAACC,KAAK,EAAEG,QAAS;QAACR,KAAK,EAAE;UAAEM,QAAQ,EAAE;QAAG;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CACR;IACDG,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC7E,WAAW,GAAG8E,CAAC,CAAC9E;EACtC,CAAC,EACD;IACE8D,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,MAAM,EAAGrE,KAAK,IAAK;MACjB,MAAM8E,KAAK,GAAG9E,KAAK,IAAI,CAAC,GAAG,SAAS,GAAGA,KAAK,IAAI,CAAC,GAAG,SAAS,GAAGA,KAAK,IAAI,CAAC,GAAG,SAAS,GAAG,SAAS;MAClG,oBAAOtB,OAAA,CAACf,GAAG;QAACmH,KAAK,EAAEA,KAAM;QAAAN,QAAA,GAAExE,KAAK,EAAC,IAAE;MAAA;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAC3C,CAAC;IACDG,MAAM,EAAEA,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrE,WAAW,GAAGsE,CAAC,CAACtE;EACtC,CAAC,EACD;IACEsD,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAGvD,MAAM,iBACbpC,OAAA,CAACf,GAAG;MAACmH,KAAK,EAAEjB,cAAc,CAAC/C,MAAM,CAAE;MAAA0D,QAAA,EAChC1D,MAAM,KAAK,QAAQ,GAAG,IAAI,GAC1BA,MAAM,KAAK,UAAU,GAAG,KAAK,GAC7BA,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG;IAAI;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC;EAET,CAAC,EACD;IACEX,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,MAAM,EAAEA,CAACmB,CAAC,EAAEjB,MAAM,kBAChB7F,OAAA,CAACnB,KAAK;MAAAiH,QAAA,gBACJ9F,OAAA,CAACZ,OAAO;QAACoG,KAAK,EAAC,cAAI;QAAAM,QAAA,eACjB9F,OAAA,CAACzB,MAAM;UACL8C,IAAI,EAAC,MAAM;UACX0F,IAAI,eAAE/G,OAAA,CAACR,YAAY;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBa,OAAO,EAAEA,CAAA,KAAMtE,UAAU,CAACmD,MAAM;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACVnG,OAAA,CAACd,UAAU;QACTsG,KAAK,EAAC,8DAAY;QAClByB,SAAS,EAAEA,CAAA,KAAM9D,YAAY,CAAC0C,MAAM,CAACtF,EAAE,CAAE;QACzC2G,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAArB,QAAA,eAEf9F,OAAA,CAACZ,OAAO;UAACoG,KAAK,EAAC,cAAI;UAAAM,QAAA,eACjB9F,OAAA,CAACzB,MAAM;YACL8C,IAAI,EAAC,MAAM;YACX+F,MAAM;YACNL,IAAI,eAAE/G,OAAA,CAACP,cAAc;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEnG,OAAA;IAAKqH,SAAS,EAAC,SAAS;IAAAvB,QAAA,gBACtB9F,OAAA;MAAKqH,SAAS,EAAC,aAAa;MAAAvB,QAAA,gBAC1B9F,OAAA,CAACC,KAAK;QAACqB,KAAK,EAAE,CAAE;QAAC+F,SAAS,EAAC,YAAY;QAAAvB,QAAA,gBACrC9F,OAAA,CAACN,eAAe;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,yCACrB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRnG,OAAA,CAACzB,MAAM;QACL8C,IAAI,EAAC,SAAS;QACd0F,IAAI,eAAE/G,OAAA,CAACT,YAAY;UAAAyG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBa,OAAO,EAAExE,SAAU;QAAAsD,QAAA,EACpB;MAED;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENnG,OAAA,CAACjB,GAAG;MAACuI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACjB,KAAK,EAAE;QAAEkB,YAAY,EAAE;MAAG,CAAE;MAAAzB,QAAA,gBACjD9F,OAAA,CAAChB,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAA1B,QAAA,eACX9F,OAAA,CAAC3B,IAAI;UAACoJ,IAAI,EAAC,OAAO;UAAA3B,QAAA,eAChB9F,OAAA,CAACX,SAAS;YACRmG,KAAK,EAAC,0BAAM;YACZkB,KAAK,EAAEjG,UAAU,CAACiH,MAAO;YACzBC,MAAM,eAAE3H,OAAA,CAACN,eAAe;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnG,OAAA,CAAChB,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAA1B,QAAA,eACX9F,OAAA,CAAC3B,IAAI;UAACoJ,IAAI,EAAC,OAAO;UAAA3B,QAAA,eAChB9F,OAAA,CAACX,SAAS;YACRmG,KAAK,EAAC,0BAAM;YACZkB,KAAK,EAAEjG,UAAU,CAAC4C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,MAAM,KAAK,QAAQ,CAAC,CAACsF,MAAO;YAC5DC,MAAM,eAAE3H,OAAA,CAACL,YAAY;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzByB,UAAU,EAAE;cAAExB,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnG,OAAA,CAAChB,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAA1B,QAAA,eACX9F,OAAA,CAAC3B,IAAI;UAACoJ,IAAI,EAAC,OAAO;UAAA3B,QAAA,eAChB9F,OAAA,CAACX,SAAS;YACRmG,KAAK,EAAC,0BAAM;YACZkB,KAAK,EAAEjG,UAAU,CAAC4C,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClB,MAAM,KAAK,SAAS,CAAC,CAACsF,MAAO;YAC7DC,MAAM,eAAE3H,OAAA,CAACJ,mBAAmB;cAAAoG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCyB,UAAU,EAAE;cAAExB,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnG,OAAA,CAAChB,GAAG;QAACwI,IAAI,EAAE,CAAE;QAAA1B,QAAA,eACX9F,OAAA,CAAC3B,IAAI;UAACoJ,IAAI,EAAC,OAAO;UAAA3B,QAAA,eAChB9F,OAAA,CAACX,SAAS;YACRmG,KAAK,EAAC,sCAAQ;YACdkB,KAAK,EAAEjG,UAAU,CAACiH,MAAM,GAAG,CAAC,GAAG,CAACjH,UAAU,CAACoH,MAAM,CAAC,CAACC,GAAG,EAAExE,CAAC,KAAKwE,GAAG,GAAGxE,CAAC,CAACpB,WAAW,EAAE,CAAC,CAAC,GAAGzB,UAAU,CAACiH,MAAM,EAAEK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAE;YAC1HJ,MAAM,eAAE3H,OAAA,CAACH,WAAW;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxByB,UAAU,EAAE;cAAExB,KAAK,EAAE;YAAU;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnG,OAAA,CAAC3B,IAAI;MAAAyH,QAAA,eACH9F,OAAA,CAAC1B,KAAK;QACJiH,OAAO,EAAEA,OAAQ;QACjByC,UAAU,EAAEvH,UAAW;QACvBwH,MAAM,EAAC,IAAI;QACXtH,OAAO,EAAEA,OAAQ;QACjBuH,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAGC,KAAK,IAAK,KAAKA,KAAK;QAClC;MAAE;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEPnG,OAAA,CAACxB,KAAK;MACJgH,KAAK,EAAEzE,gBAAgB,GAAG,MAAM,GAAG,MAAO;MAC1CyH,IAAI,EAAE3H,YAAa;MACnB4H,QAAQ,EAAEA,CAAA,KAAM3H,eAAe,CAAC,KAAK,CAAE;MACvC4H,IAAI,EAAEA,CAAA,KAAMzH,IAAI,CAAC0H,MAAM,CAAC,CAAE;MAC1BC,cAAc,EAAEjI,OAAQ;MACxBkI,KAAK,EAAE,GAAI;MAAA/C,QAAA,eAEX9F,OAAA,CAACvB,IAAI;QACHwC,IAAI,EAAEA,IAAK;QACX6H,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEtF,YAAa;QACvBuF,aAAa,EAAE;UACb3H,IAAI,EAAE,UAAU;UAChBC,KAAK,EAAE,CAAC;UACRC,SAAS,EAAE,CAAC;UACZC,QAAQ,EAAE,GAAG;UACbC,SAAS,EAAE,OAAO;UAClBC,WAAW,EAAE,CAAC;UACdC,aAAa,EAAE,CAAC;UAChBO,WAAW,EAAE,CAAC;UACdE,MAAM,EAAE;QACV,CAAE;QAAA0D,QAAA,gBAEF9F,OAAA,CAACjB,GAAG;UAACuI,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd9F,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZ9F,OAAA,CAACvB,IAAI,CAACwK,IAAI;cACR7H,IAAI,EAAC,MAAM;cACX8H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2G,QAAA,eAEhD9F,OAAA,CAACtB,KAAK;gBAAC2K,WAAW,EAAC;cAAS;gBAAArD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZ9F,OAAA,CAACvB,IAAI,CAACwK,IAAI;cACR7H,IAAI,EAAC,MAAM;cACX8H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2G,QAAA,eAEhD9F,OAAA,CAACrB,MAAM;gBAAAmH,QAAA,gBACL9F,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,UAAU;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,WAAW;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,WAAW;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,SAAS;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA,CAACjB,GAAG;UAACuI,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd9F,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX9F,OAAA,CAACvB,IAAI,CAACwK,IAAI;cACR7H,IAAI,EAAC,OAAO;cACZ8H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2G,QAAA,eAEhD9F,OAAA,CAACpB,WAAW;gBAAC0K,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,EAAG;gBAAClD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX9F,OAAA,CAACvB,IAAI,CAACwK,IAAI;cACR7H,IAAI,EAAC,WAAW;cAChB8H,KAAK,EAAC,oBAAK;cACXC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjK,OAAO,EAAE;cAAS,CAAC,CAAE;cAAA2G,QAAA,eAE/C9F,OAAA,CAACpB,WAAW;gBAAC0K,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAClD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX9F,OAAA,CAACvB,IAAI,CAACwK,IAAI;cACR7H,IAAI,EAAC,aAAa;cAClB8H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2G,QAAA,eAEhD9F,OAAA,CAACpB,WAAW;gBAAC0K,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAClD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA,CAACjB,GAAG;UAACuI,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd9F,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX9F,OAAA,CAACvB,IAAI,CAACwK,IAAI;cACR7H,IAAI,EAAC,UAAU;cACf8H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2G,QAAA,eAEhD9F,OAAA,CAACpB,WAAW;gBAAC0K,GAAG,EAAE,CAAE;gBAACE,IAAI,EAAE,GAAI;gBAACnD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO,CAAE;gBAACY,UAAU,EAAC;cAAG;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX9F,OAAA,CAACvB,IAAI,CAACwK,IAAI;cACR7H,IAAI,EAAC,aAAa;cAClB8H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2G,QAAA,eAEhD9F,OAAA,CAACpB,WAAW;gBAAC0K,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAClD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,CAAE;YAAA1B,QAAA,eACX9F,OAAA,CAACvB,IAAI,CAACwK,IAAI;cACR7H,IAAI,EAAC,eAAe;cACpB8H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2G,QAAA,eAEhD9F,OAAA,CAACpB,WAAW;gBAAC0K,GAAG,EAAE,CAAE;gBAACC,GAAG,EAAE,CAAE;gBAAClD,KAAK,EAAE;kBAAEwC,KAAK,EAAE;gBAAO;cAAE;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA,CAACjB,GAAG;UAACuI,MAAM,EAAE,EAAG;UAAAxB,QAAA,gBACd9F,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZ9F,OAAA,CAACvB,IAAI,CAACwK,IAAI;cACR7H,IAAI,EAAC,WAAW;cAChB8H,KAAK,EAAC,0BAAM;cACZC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA2G,QAAA,eAEhD9F,OAAA,CAACrB,MAAM;gBAAAmH,QAAA,gBACL9F,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,MAAM;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,OAAO;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,OAAO;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,MAAM;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,UAAU;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,SAAS;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAAChB,GAAG;YAACwI,IAAI,EAAE,EAAG;YAAA1B,QAAA,eACZ9F,OAAA,CAACvB,IAAI,CAACwK,IAAI;cACR7H,IAAI,EAAC,QAAQ;cACb8H,KAAK,EAAC,cAAI;cACVC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjK,OAAO,EAAE;cAAQ,CAAC,CAAE;cAAA2G,QAAA,eAE9C9F,OAAA,CAACrB,MAAM;gBAAAmH,QAAA,gBACL9F,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,UAAU;kBAAAZ,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACrCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,SAAS;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnCnG,OAAA,CAACI,MAAM;kBAACsG,KAAK,EAAC,QAAQ;kBAAAZ,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA,CAACvB,IAAI,CAACwK,IAAI;UAAC7H,IAAI,EAAC,cAAc;UAAC8H,KAAK,EAAC,oBAAK;UAAApD,QAAA,eACxC9F,OAAA,CAACtB,KAAK;YAAC2K,WAAW,EAAC;UAAQ;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZnG,OAAA,CAACvB,IAAI,CAACwK,IAAI;UAAC7H,IAAI,EAAC,eAAe;UAAC8H,KAAK,EAAC,0BAAM;UAACQ,KAAK,EAAC,8DAAY;UAAA5D,QAAA,eAC7D9F,OAAA,CAACtB,KAAK;YAAC2K,WAAW,EAAC;UAAc;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEZnG,OAAA,CAACvB,IAAI,CAACwK,IAAI;UAAC7H,IAAI,EAAC,cAAc;UAAC8H,KAAK,EAAC,0BAAM;UAACQ,KAAK,EAAC,8DAAY;UAAA5D,QAAA,eAC5D9F,OAAA,CAACtB,KAAK;YAAC2K,WAAW,EAAC;UAAa;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eAEZnG,OAAA,CAACvB,IAAI,CAACwK,IAAI;UAAC7H,IAAI,EAAC,aAAa;UAAC8H,KAAK,EAAC,oBAAK;UAACQ,KAAK,EAAC,8DAAY;UAAA5D,QAAA,eAC1D9F,OAAA,CAACtB,KAAK;YAAC2K,WAAW,EAAC;UAAc;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAEZnG,OAAA,CAACvB,IAAI,CAACwK,IAAI;UAAC7H,IAAI,EAAC,gBAAgB;UAAC8H,KAAK,EAAC,0BAAM;UAACQ,KAAK,EAAC,8DAAY;UAAA5D,QAAA,eAC9D9F,OAAA,CAACtB,KAAK;YAAC2K,WAAW,EAAC;UAAkB;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAEZnG,OAAA,CAACvB,IAAI,CAACwK,IAAI;UAAC7H,IAAI,EAAC,qBAAqB;UAAC8H,KAAK,EAAC,0BAAM;UAACQ,KAAK,EAAC,8DAAY;UAAA5D,QAAA,eACnE9F,OAAA,CAACtB,KAAK;YAAC2K,WAAW,EAAC;UAAY;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC,eAEZnG,OAAA,CAACvB,IAAI,CAACwK,IAAI;UACR7H,IAAI,EAAC,aAAa;UAClB8H,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEjK,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA2G,QAAA,eAE9C9F,OAAA,CAACG,QAAQ;YAACwJ,IAAI,EAAE,CAAE;YAACN,WAAW,EAAC;UAAiB;YAAArD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC7F,EAAA,CA5iBID,kBAAkB;EAAA,QACIlC,SAAS,EAKpBM,IAAI,CAACyC,OAAO;AAAA;AAAA0I,EAAA,GANvBvJ,kBAAkB;AA8iBxB,eAAeA,kBAAkB;AAAC,IAAAuJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}