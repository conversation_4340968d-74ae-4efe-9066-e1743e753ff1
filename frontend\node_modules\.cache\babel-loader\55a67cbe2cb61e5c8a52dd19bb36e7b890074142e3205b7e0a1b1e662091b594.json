{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nvar Mask = function Mask(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    visible = props.visible,\n    maskProps = props.maskProps,\n    motionName = props.motionName,\n    className = props.className;\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    key: \"mask\",\n    visible: visible,\n    motionName: motionName,\n    leavedClassName: \"\".concat(prefixCls, \"-mask-hidden\")\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: ref,\n      style: _objectSpread(_objectSpread({}, motionStyle), style),\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), motionClassName, className)\n    }, maskProps));\n  });\n};\nexport default Mask;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "React", "classNames", "CSSMotion", "Mask", "props", "prefixCls", "style", "visible", "maskProps", "motionName", "className", "createElement", "key", "leavedClassName", "concat", "_ref", "ref", "motionClassName", "motionStyle"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/rc-dialog/es/Dialog/Mask.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport CSSMotion from 'rc-motion';\nvar Mask = function Mask(props) {\n  var prefixCls = props.prefixCls,\n    style = props.style,\n    visible = props.visible,\n    maskProps = props.maskProps,\n    motionName = props.motionName,\n    className = props.className;\n  return /*#__PURE__*/React.createElement(CSSMotion, {\n    key: \"mask\",\n    visible: visible,\n    motionName: motionName,\n    leavedClassName: \"\".concat(prefixCls, \"-mask-hidden\")\n  }, function (_ref, ref) {\n    var motionClassName = _ref.className,\n      motionStyle = _ref.style;\n    return /*#__PURE__*/React.createElement(\"div\", _extends({\n      ref: ref,\n      style: _objectSpread(_objectSpread({}, motionStyle), style),\n      className: classNames(\"\".concat(prefixCls, \"-mask\"), motionClassName, className)\n    }, maskProps));\n  });\n};\nexport default Mask;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,WAAW;AACjC,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,KAAK,EAAE;EAC9B,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,OAAO,GAAGH,KAAK,CAACG,OAAO;IACvBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,SAAS,GAAGN,KAAK,CAACM,SAAS;EAC7B,OAAO,aAAaV,KAAK,CAACW,aAAa,CAACT,SAAS,EAAE;IACjDU,GAAG,EAAE,MAAM;IACXL,OAAO,EAAEA,OAAO;IAChBE,UAAU,EAAEA,UAAU;IACtBI,eAAe,EAAE,EAAE,CAACC,MAAM,CAACT,SAAS,EAAE,cAAc;EACtD,CAAC,EAAE,UAAUU,IAAI,EAAEC,GAAG,EAAE;IACtB,IAAIC,eAAe,GAAGF,IAAI,CAACL,SAAS;MAClCQ,WAAW,GAAGH,IAAI,CAACT,KAAK;IAC1B,OAAO,aAAaN,KAAK,CAACW,aAAa,CAAC,KAAK,EAAEb,QAAQ,CAAC;MACtDkB,GAAG,EAAEA,GAAG;MACRV,KAAK,EAAEP,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmB,WAAW,CAAC,EAAEZ,KAAK,CAAC;MAC3DI,SAAS,EAAET,UAAU,CAAC,EAAE,CAACa,MAAM,CAACT,SAAS,EAAE,OAAO,CAAC,EAAEY,eAAe,EAAEP,SAAS;IACjF,CAAC,EAAEF,SAAS,CAAC,CAAC;EAChB,CAAC,CAAC;AACJ,CAAC;AACD,eAAeL,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}