{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { genDataNodeKey, getRemovable } from \"../util\";\nvar TabNode = function TabNode(props) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    active = props.active,\n    focus = props.focus,\n    _props$tab = props.tab,\n    key = _props$tab.key,\n    label = _props$tab.label,\n    disabled = _props$tab.disabled,\n    closeIcon = _props$tab.closeIcon,\n    icon = _props$tab.icon,\n    closable = props.closable,\n    renderWrapper = props.renderWrapper,\n    removeAriaLabel = props.removeAriaLabel,\n    editable = props.editable,\n    onClick = props.onClick,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onKeyDown = props.onKeyDown,\n    onMouseDown = props.onMouseDown,\n    onMouseUp = props.onMouseUp,\n    style = props.style,\n    tabCount = props.tabCount,\n    currentPosition = props.currentPosition;\n  var tabPrefix = \"\".concat(prefixCls, \"-tab\");\n  var removable = getRemovable(closable, closeIcon, editable, disabled);\n  function onInternalClick(e) {\n    if (disabled) {\n      return;\n    }\n    onClick(e);\n  }\n  function onRemoveTab(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var labelNode = React.useMemo(function () {\n    return icon && typeof label === 'string' ? /*#__PURE__*/React.createElement(\"span\", null, label) : label;\n  }, [label, icon]);\n  var btnRef = React.useRef(null);\n  React.useEffect(function () {\n    if (focus && btnRef.current) {\n      btnRef.current.focus();\n    }\n  }, [focus]);\n  var node = /*#__PURE__*/React.createElement(\"div\", {\n    key: key,\n    \"data-node-key\": genDataNodeKey(key),\n    className: classNames(tabPrefix, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(tabPrefix, \"-with-remove\"), removable), \"\".concat(tabPrefix, \"-active\"), active), \"\".concat(tabPrefix, \"-disabled\"), disabled), \"\".concat(tabPrefix, \"-focus\"), focus)),\n    style: style,\n    onClick: onInternalClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: btnRef,\n    role: \"tab\",\n    \"aria-selected\": active,\n    id: id && \"\".concat(id, \"-tab-\").concat(key),\n    className: \"\".concat(tabPrefix, \"-btn\"),\n    \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n    \"aria-disabled\": disabled,\n    tabIndex: disabled ? null : active ? 0 : -1,\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onInternalClick(e);\n    },\n    onKeyDown: onKeyDown,\n    onMouseDown: onMouseDown,\n    onMouseUp: onMouseUp,\n    onFocus: onFocus,\n    onBlur: onBlur\n  }, focus && /*#__PURE__*/React.createElement(\"div\", {\n    \"aria-live\": \"polite\",\n    style: {\n      width: 0,\n      height: 0,\n      position: 'absolute',\n      overflow: 'hidden',\n      opacity: 0\n    }\n  }, \"Tab \".concat(currentPosition, \" of \").concat(tabCount)), icon && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(tabPrefix, \"-icon\")\n  }, icon), label && labelNode), removable && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    role: \"tab\",\n    \"aria-label\": removeAriaLabel || 'remove',\n    tabIndex: active ? 0 : -1,\n    className: \"\".concat(tabPrefix, \"-remove\"),\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onRemoveTab(e);\n    }\n  }, closeIcon || editable.removeIcon || '×'));\n  return renderWrapper ? renderWrapper(node) : node;\n};\nexport default TabNode;", "map": {"version": 3, "names": ["_defineProperty", "classNames", "React", "genDataNodeKey", "getRemovable", "TabNode", "props", "prefixCls", "id", "active", "focus", "_props$tab", "tab", "key", "label", "disabled", "closeIcon", "icon", "closable", "renderWrapper", "removeAriaLabel", "editable", "onClick", "onFocus", "onBlur", "onKeyDown", "onMouseDown", "onMouseUp", "style", "tabCount", "currentPosition", "tabPrefix", "concat", "removable", "onInternalClick", "e", "onRemoveTab", "event", "preventDefault", "stopPropagation", "onEdit", "labelNode", "useMemo", "createElement", "btnRef", "useRef", "useEffect", "current", "node", "className", "ref", "role", "tabIndex", "width", "height", "position", "overflow", "opacity", "type", "removeIcon"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/rc-tabs/es/TabNavList/TabNode.js"], "sourcesContent": ["import _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { genDataNodeKey, getRemovable } from \"../util\";\nvar TabNode = function TabNode(props) {\n  var prefixCls = props.prefixCls,\n    id = props.id,\n    active = props.active,\n    focus = props.focus,\n    _props$tab = props.tab,\n    key = _props$tab.key,\n    label = _props$tab.label,\n    disabled = _props$tab.disabled,\n    closeIcon = _props$tab.closeIcon,\n    icon = _props$tab.icon,\n    closable = props.closable,\n    renderWrapper = props.renderWrapper,\n    removeAriaLabel = props.removeAriaLabel,\n    editable = props.editable,\n    onClick = props.onClick,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onKeyDown = props.onKeyDown,\n    onMouseDown = props.onMouseDown,\n    onMouseUp = props.onMouseUp,\n    style = props.style,\n    tabCount = props.tabCount,\n    currentPosition = props.currentPosition;\n  var tabPrefix = \"\".concat(prefixCls, \"-tab\");\n  var removable = getRemovable(closable, closeIcon, editable, disabled);\n  function onInternalClick(e) {\n    if (disabled) {\n      return;\n    }\n    onClick(e);\n  }\n  function onRemoveTab(event) {\n    event.preventDefault();\n    event.stopPropagation();\n    editable.onEdit('remove', {\n      key: key,\n      event: event\n    });\n  }\n  var labelNode = React.useMemo(function () {\n    return icon && typeof label === 'string' ? /*#__PURE__*/React.createElement(\"span\", null, label) : label;\n  }, [label, icon]);\n  var btnRef = React.useRef(null);\n  React.useEffect(function () {\n    if (focus && btnRef.current) {\n      btnRef.current.focus();\n    }\n  }, [focus]);\n  var node = /*#__PURE__*/React.createElement(\"div\", {\n    key: key,\n    \"data-node-key\": genDataNodeKey(key),\n    className: classNames(tabPrefix, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(tabPrefix, \"-with-remove\"), removable), \"\".concat(tabPrefix, \"-active\"), active), \"\".concat(tabPrefix, \"-disabled\"), disabled), \"\".concat(tabPrefix, \"-focus\"), focus)),\n    style: style,\n    onClick: onInternalClick\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: btnRef,\n    role: \"tab\",\n    \"aria-selected\": active,\n    id: id && \"\".concat(id, \"-tab-\").concat(key),\n    className: \"\".concat(tabPrefix, \"-btn\"),\n    \"aria-controls\": id && \"\".concat(id, \"-panel-\").concat(key),\n    \"aria-disabled\": disabled,\n    tabIndex: disabled ? null : active ? 0 : -1,\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onInternalClick(e);\n    },\n    onKeyDown: onKeyDown,\n    onMouseDown: onMouseDown,\n    onMouseUp: onMouseUp,\n    onFocus: onFocus,\n    onBlur: onBlur\n  }, focus && /*#__PURE__*/React.createElement(\"div\", {\n    \"aria-live\": \"polite\",\n    style: {\n      width: 0,\n      height: 0,\n      position: 'absolute',\n      overflow: 'hidden',\n      opacity: 0\n    }\n  }, \"Tab \".concat(currentPosition, \" of \").concat(tabCount)), icon && /*#__PURE__*/React.createElement(\"span\", {\n    className: \"\".concat(tabPrefix, \"-icon\")\n  }, icon), label && labelNode), removable && /*#__PURE__*/React.createElement(\"button\", {\n    type: \"button\",\n    role: \"tab\",\n    \"aria-label\": removeAriaLabel || 'remove',\n    tabIndex: active ? 0 : -1,\n    className: \"\".concat(tabPrefix, \"-remove\"),\n    onClick: function onClick(e) {\n      e.stopPropagation();\n      onRemoveTab(e);\n    }\n  }, closeIcon || editable.removeIcon || '×'));\n  return renderWrapper ? renderWrapper(node) : node;\n};\nexport default TabNode;"], "mappings": "AAAA,OAAOA,eAAe,MAAM,2CAA2C;AACvE,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,EAAEC,YAAY,QAAQ,SAAS;AACtD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,KAAK,EAAE;EACpC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,EAAE,GAAGF,KAAK,CAACE,EAAE;IACbC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;IACnBC,UAAU,GAAGL,KAAK,CAACM,GAAG;IACtBC,GAAG,GAAGF,UAAU,CAACE,GAAG;IACpBC,KAAK,GAAGH,UAAU,CAACG,KAAK;IACxBC,QAAQ,GAAGJ,UAAU,CAACI,QAAQ;IAC9BC,SAAS,GAAGL,UAAU,CAACK,SAAS;IAChCC,IAAI,GAAGN,UAAU,CAACM,IAAI;IACtBC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,aAAa,GAAGb,KAAK,CAACa,aAAa;IACnCC,eAAe,GAAGd,KAAK,CAACc,eAAe;IACvCC,QAAQ,GAAGf,KAAK,CAACe,QAAQ;IACzBC,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACvBC,OAAO,GAAGjB,KAAK,CAACiB,OAAO;IACvBC,MAAM,GAAGlB,KAAK,CAACkB,MAAM;IACrBC,SAAS,GAAGnB,KAAK,CAACmB,SAAS;IAC3BC,WAAW,GAAGpB,KAAK,CAACoB,WAAW;IAC/BC,SAAS,GAAGrB,KAAK,CAACqB,SAAS;IAC3BC,KAAK,GAAGtB,KAAK,CAACsB,KAAK;IACnBC,QAAQ,GAAGvB,KAAK,CAACuB,QAAQ;IACzBC,eAAe,GAAGxB,KAAK,CAACwB,eAAe;EACzC,IAAIC,SAAS,GAAG,EAAE,CAACC,MAAM,CAACzB,SAAS,EAAE,MAAM,CAAC;EAC5C,IAAI0B,SAAS,GAAG7B,YAAY,CAACc,QAAQ,EAAEF,SAAS,EAAEK,QAAQ,EAAEN,QAAQ,CAAC;EACrE,SAASmB,eAAeA,CAACC,CAAC,EAAE;IAC1B,IAAIpB,QAAQ,EAAE;MACZ;IACF;IACAO,OAAO,CAACa,CAAC,CAAC;EACZ;EACA,SAASC,WAAWA,CAACC,KAAK,EAAE;IAC1BA,KAAK,CAACC,cAAc,CAAC,CAAC;IACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;IACvBlB,QAAQ,CAACmB,MAAM,CAAC,QAAQ,EAAE;MACxB3B,GAAG,EAAEA,GAAG;MACRwB,KAAK,EAAEA;IACT,CAAC,CAAC;EACJ;EACA,IAAII,SAAS,GAAGvC,KAAK,CAACwC,OAAO,CAAC,YAAY;IACxC,OAAOzB,IAAI,IAAI,OAAOH,KAAK,KAAK,QAAQ,GAAG,aAAaZ,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE7B,KAAK,CAAC,GAAGA,KAAK;EAC1G,CAAC,EAAE,CAACA,KAAK,EAAEG,IAAI,CAAC,CAAC;EACjB,IAAI2B,MAAM,GAAG1C,KAAK,CAAC2C,MAAM,CAAC,IAAI,CAAC;EAC/B3C,KAAK,CAAC4C,SAAS,CAAC,YAAY;IAC1B,IAAIpC,KAAK,IAAIkC,MAAM,CAACG,OAAO,EAAE;MAC3BH,MAAM,CAACG,OAAO,CAACrC,KAAK,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACX,IAAIsC,IAAI,GAAG,aAAa9C,KAAK,CAACyC,aAAa,CAAC,KAAK,EAAE;IACjD9B,GAAG,EAAEA,GAAG;IACR,eAAe,EAAEV,cAAc,CAACU,GAAG,CAAC;IACpCoC,SAAS,EAAEhD,UAAU,CAAC8B,SAAS,EAAE/B,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAACgC,MAAM,CAACD,SAAS,EAAE,cAAc,CAAC,EAAEE,SAAS,CAAC,EAAE,EAAE,CAACD,MAAM,CAACD,SAAS,EAAE,SAAS,CAAC,EAAEtB,MAAM,CAAC,EAAE,EAAE,CAACuB,MAAM,CAACD,SAAS,EAAE,WAAW,CAAC,EAAEhB,QAAQ,CAAC,EAAE,EAAE,CAACiB,MAAM,CAACD,SAAS,EAAE,QAAQ,CAAC,EAAErB,KAAK,CAAC,CAAC;IACtRkB,KAAK,EAAEA,KAAK;IACZN,OAAO,EAAEY;EACX,CAAC,EAAE,aAAahC,KAAK,CAACyC,aAAa,CAAC,KAAK,EAAE;IACzCO,GAAG,EAAEN,MAAM;IACXO,IAAI,EAAE,KAAK;IACX,eAAe,EAAE1C,MAAM;IACvBD,EAAE,EAAEA,EAAE,IAAI,EAAE,CAACwB,MAAM,CAACxB,EAAE,EAAE,OAAO,CAAC,CAACwB,MAAM,CAACnB,GAAG,CAAC;IAC5CoC,SAAS,EAAE,EAAE,CAACjB,MAAM,CAACD,SAAS,EAAE,MAAM,CAAC;IACvC,eAAe,EAAEvB,EAAE,IAAI,EAAE,CAACwB,MAAM,CAACxB,EAAE,EAAE,SAAS,CAAC,CAACwB,MAAM,CAACnB,GAAG,CAAC;IAC3D,eAAe,EAAEE,QAAQ;IACzBqC,QAAQ,EAAErC,QAAQ,GAAG,IAAI,GAAGN,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3Ca,OAAO,EAAE,SAASA,OAAOA,CAACa,CAAC,EAAE;MAC3BA,CAAC,CAACI,eAAe,CAAC,CAAC;MACnBL,eAAe,CAACC,CAAC,CAAC;IACpB,CAAC;IACDV,SAAS,EAAEA,SAAS;IACpBC,WAAW,EAAEA,WAAW;IACxBC,SAAS,EAAEA,SAAS;IACpBJ,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA;EACV,CAAC,EAAEd,KAAK,IAAI,aAAaR,KAAK,CAACyC,aAAa,CAAC,KAAK,EAAE;IAClD,WAAW,EAAE,QAAQ;IACrBf,KAAK,EAAE;MACLyB,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,OAAO,EAAE;IACX;EACF,CAAC,EAAE,MAAM,CAACzB,MAAM,CAACF,eAAe,EAAE,MAAM,CAAC,CAACE,MAAM,CAACH,QAAQ,CAAC,CAAC,EAAEZ,IAAI,IAAI,aAAaf,KAAK,CAACyC,aAAa,CAAC,MAAM,EAAE;IAC5GM,SAAS,EAAE,EAAE,CAACjB,MAAM,CAACD,SAAS,EAAE,OAAO;EACzC,CAAC,EAAEd,IAAI,CAAC,EAAEH,KAAK,IAAI2B,SAAS,CAAC,EAAER,SAAS,IAAI,aAAa/B,KAAK,CAACyC,aAAa,CAAC,QAAQ,EAAE;IACrFe,IAAI,EAAE,QAAQ;IACdP,IAAI,EAAE,KAAK;IACX,YAAY,EAAE/B,eAAe,IAAI,QAAQ;IACzCgC,QAAQ,EAAE3C,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;IACzBwC,SAAS,EAAE,EAAE,CAACjB,MAAM,CAACD,SAAS,EAAE,SAAS,CAAC;IAC1CT,OAAO,EAAE,SAASA,OAAOA,CAACa,CAAC,EAAE;MAC3BA,CAAC,CAACI,eAAe,CAAC,CAAC;MACnBH,WAAW,CAACD,CAAC,CAAC;IAChB;EACF,CAAC,EAAEnB,SAAS,IAAIK,QAAQ,CAACsC,UAAU,IAAI,GAAG,CAAC,CAAC;EAC5C,OAAOxC,aAAa,GAAGA,aAAa,CAAC6B,IAAI,CAAC,GAAGA,IAAI;AACnD,CAAC;AACD,eAAe3C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}