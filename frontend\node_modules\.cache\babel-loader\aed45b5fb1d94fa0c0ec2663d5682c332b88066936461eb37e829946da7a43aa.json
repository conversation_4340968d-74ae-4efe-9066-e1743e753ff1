{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { collectScroller, getWin } from \"../util\";\nexport default function useWatch(open, target, popup, onAlign, onScroll) {\n  useLayoutEffect(function () {\n    if (open && target && popup) {\n      var targetElement = target;\n      var popupElement = popup;\n      var targetScrollList = collectScroller(targetElement);\n      var popupScrollList = collectScroller(popupElement);\n      var win = getWin(popupElement);\n      var mergedList = new Set([win].concat(_toConsumableArray(targetScrollList), _toConsumableArray(popupScrollList)));\n      function notifyScroll() {\n        onAlign();\n        onScroll();\n      }\n      mergedList.forEach(function (scroller) {\n        scroller.addEventListener('scroll', notifyScroll, {\n          passive: true\n        });\n      });\n      win.addEventListener('resize', notifyScroll, {\n        passive: true\n      });\n\n      // First time always do align\n      onAlign();\n      return function () {\n        mergedList.forEach(function (scroller) {\n          scroller.removeEventListener('scroll', notifyScroll);\n          win.removeEventListener('resize', notifyScroll);\n        });\n      };\n    }\n  }, [open, target, popup]);\n}", "map": {"version": 3, "names": ["_toConsumableArray", "useLayoutEffect", "collectScroller", "getWin", "useWatch", "open", "target", "popup", "onAlign", "onScroll", "targetElement", "popupElement", "targetScrollList", "popupScrollList", "win", "mergedList", "Set", "concat", "notifyScroll", "for<PERSON>ach", "scroller", "addEventListener", "passive", "removeEventListener"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/@rc-component/trigger/es/hooks/useWatch.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport { collectScroller, getWin } from \"../util\";\nexport default function useWatch(open, target, popup, onAlign, onScroll) {\n  useLayoutEffect(function () {\n    if (open && target && popup) {\n      var targetElement = target;\n      var popupElement = popup;\n      var targetScrollList = collectScroller(targetElement);\n      var popupScrollList = collectScroller(popupElement);\n      var win = getWin(popupElement);\n      var mergedList = new Set([win].concat(_toConsumableArray(targetScrollList), _toConsumableArray(popupScrollList)));\n      function notifyScroll() {\n        onAlign();\n        onScroll();\n      }\n      mergedList.forEach(function (scroller) {\n        scroller.addEventListener('scroll', notifyScroll, {\n          passive: true\n        });\n      });\n      win.addEventListener('resize', notifyScroll, {\n        passive: true\n      });\n\n      // First time always do align\n      onAlign();\n      return function () {\n        mergedList.forEach(function (scroller) {\n          scroller.removeEventListener('scroll', notifyScroll);\n          win.removeEventListener('resize', notifyScroll);\n        });\n      };\n    }\n  }, [open, target, popup]);\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,SAASC,eAAe,EAAEC,MAAM,QAAQ,SAAS;AACjD,eAAe,SAASC,QAAQA,CAACC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACvER,eAAe,CAAC,YAAY;IAC1B,IAAII,IAAI,IAAIC,MAAM,IAAIC,KAAK,EAAE;MAC3B,IAAIG,aAAa,GAAGJ,MAAM;MAC1B,IAAIK,YAAY,GAAGJ,KAAK;MACxB,IAAIK,gBAAgB,GAAGV,eAAe,CAACQ,aAAa,CAAC;MACrD,IAAIG,eAAe,GAAGX,eAAe,CAACS,YAAY,CAAC;MACnD,IAAIG,GAAG,GAAGX,MAAM,CAACQ,YAAY,CAAC;MAC9B,IAAII,UAAU,GAAG,IAAIC,GAAG,CAAC,CAACF,GAAG,CAAC,CAACG,MAAM,CAACjB,kBAAkB,CAACY,gBAAgB,CAAC,EAAEZ,kBAAkB,CAACa,eAAe,CAAC,CAAC,CAAC;MACjH,SAASK,YAAYA,CAAA,EAAG;QACtBV,OAAO,CAAC,CAAC;QACTC,QAAQ,CAAC,CAAC;MACZ;MACAM,UAAU,CAACI,OAAO,CAAC,UAAUC,QAAQ,EAAE;QACrCA,QAAQ,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,EAAE;UAChDI,OAAO,EAAE;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;MACFR,GAAG,CAACO,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,EAAE;QAC3CI,OAAO,EAAE;MACX,CAAC,CAAC;;MAEF;MACAd,OAAO,CAAC,CAAC;MACT,OAAO,YAAY;QACjBO,UAAU,CAACI,OAAO,CAAC,UAAUC,QAAQ,EAAE;UACrCA,QAAQ,CAACG,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;UACpDJ,GAAG,CAACS,mBAAmB,CAAC,QAAQ,EAAEL,YAAY,CAAC;QACjD,CAAC,CAAC;MACJ,CAAC;IACH;EACF,CAAC,EAAE,CAACb,IAAI,EAAEC,MAAM,EAAEC,KAAK,CAAC,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}