{"version": 3, "file": "static/css/main.5c9755c0.css", "mappings": "AAcA,MACE,YACF,CCfA,UAGE,WAAY,CADZ,UAEF,CACA,mCAEE,YACF,CACA,iBAGE,qBACF,CACA,KAGE,6BAA8B,CAC9B,yBAA0B,CAC1B,4BAA6B,CAC7B,yCAA6C,CAL7C,sBAAuB,CACvB,gBAKF,CAOA,sBACE,YACF,CACA,GACE,kBAAuB,CACvB,QAAS,CACT,gBACF,CACA,kBAQE,eAAgB,CADhB,kBAAoB,CADpB,YAGF,CACA,EAEE,iBAAkB,CADlB,YAEF,CACA,sCAKE,eAAgB,CAChB,WAAY,CAJZ,wCAAyC,CACzC,yBAA0B,CAC1B,gCAGF,CACA,QAEE,iBAAkB,CAClB,mBAAoB,CAFpB,iBAGF,CACA,kEAIE,uBACF,CACA,SAIE,iBAAkB,CADlB,YAEF,CACA,wBAIE,eACF,CACA,GACE,eACF,CACA,GACE,kBAAoB,CACpB,aACF,CACA,WACE,cACF,CACA,IACE,iBACF,CACA,SAEE,kBACF,CACA,MACE,aACF,CACA,QAGE,aAAc,CACd,aAAc,CAFd,iBAAkB,CAGlB,sBACF,CACA,IACE,aACF,CACA,IACE,SACF,CACA,kBAKE,2EAAqF,CADrF,aAEF,CACA,IAEE,iBAAkB,CADlB,YAAa,CAEb,aACF,CACA,OACE,cACF,CACA,IAEE,iBAAkB,CADlB,qBAEF,CACA,kFASE,yBACF,CACA,MACE,wBACF,CACA,QAIE,mBAAoB,CAFpB,mBAAqB,CADrB,iBAAmB,CAEnB,eAEF,CACA,sCAME,aAAc,CAEd,mBAAoB,CADpB,iBAAkB,CAElB,mBAAoB,CAJpB,QAKF,CACA,aAEE,gBACF,CACA,cAEE,mBACF,CACA,qDAIE,yBACF,CACA,wHAKE,iBAAkB,CADlB,SAEF,CACA,uCAEE,qBAAsB,CACtB,SACF,CACA,+EAIE,0BACF,CACA,SACE,aAAc,CACd,eACF,CACA,SAIE,QAAS,CAFT,QAAS,CADT,WAAY,CAEZ,SAEF,CACA,OAME,aAAc,CALd,aAAc,CAMd,eAAgB,CAChB,mBAAoB,CAJpB,kBAAoB,CADpB,cAAe,CAEf,SAAU,CAIV,kBAAmB,CAPnB,UAQF,CACA,SACE,sBACF,CACA,kFAEE,WACF,CACA,cAEE,uBAAwB,CADxB,mBAEF,CACA,qFAEE,uBACF,CACA,6BAEE,yBAA0B,CAD1B,YAEF,CACA,OACE,oBACF,CACA,QACE,iBACF,CACA,SACE,YACF,CACA,SACE,sBACF,CACA,KAEE,wBAAyB,CADzB,YAEF,CC1PA,KACE,YAAa,CACb,eACF,CAGA,EACE,qBACF,CAEA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,wBAAyB,CALzB,mIAEY,CAHZ,QAOF,CAEA,KACE,uEAEF,CAGA,kBAEE,YAAa,CACb,qBAAsB,CAFtB,YAGF,CAEA,eACE,eAAgB,CAEhB,8BAAwC,CADxC,cAAe,CAEf,YACF,CAEA,gBAEE,YAAa,CADb,QAAO,CAEP,eACF,CAEA,cACE,eAAgB,CAChB,8BACF,CAEA,aAIE,kBAAmB,CAHnB,QAAO,CAEP,eAAgB,CADhB,YAGF,CAGA,aAGE,+BAAgC,CAFhC,kBAAmB,CACnB,cAEF,CAEA,YAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,QACF,CAEA,kBACE,aAAc,CAEd,eAAgB,CADhB,cAEF,CAGA,cACE,eAAgB,CAChB,iBAAkB,CAClB,8BAAwC,CACxC,kBACF,CAEA,aAKE,kBAAmB,CAHnB,+BAAgC,CAChC,YAAa,CACb,6BAA8B,CAH9B,iBAKF,CAEA,YAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,QACF,CAEA,cACE,YACF,CAGA,SAME,eAAgB,CAChB,iBAAkB,CAClB,8BAAwC,CANxC,6BAA8B,CAE9B,kBAAmB,CACnB,YAIF,CAEA,uBARE,kBAAmB,CAFnB,YAcF,CAJA,cAGE,QACF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAGA,YAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,YACE,eAAgB,CAEhB,iBAAkB,CAClB,8BAAwC,CAFxC,YAAa,CAGb,iBACF,CAEA,aAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,iBACF,CAEA,aACE,aAAc,CACd,cACF,CAGA,iBACE,eAAgB,CAChB,iBAAkB,CAClB,8BACF,CAGA,kBAEE,wBAAyB,CACzB,iBAAkB,CAFlB,YAAa,CAGb,eACF,CAEA,gBAKE,kBAAmB,CAJnB,kBAAmB,CACnB,+BAAgC,CAEhC,YAAa,CAEb,OAAQ,CAHR,gBAIF,CAEA,gBACE,wBACF,CAGA,iBACE,kBACF,CAEA,eAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CACnB,cACF,CAEA,cAEE,cAAe,CADf,gBAAiB,CAEjB,+BACF,CAEA,oBACE,wBACF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAGA,yBACE,aACE,YACF,CAEA,YAEE,QAAS,CADT,wDAEF,CAEA,SAGE,mBAAoB,CAFpB,qBAAsB,CACtB,QAEF,CAEA,6BAEE,sBACF,CACF,CAGA,mBAGE,kBAAmB,CAFnB,YAAa,CAGb,YAAa,CAFb,sBAGF,CAGA,iBAGE,aAAc,CADd,iBAAkB,CADlB,iBAGF,CAEA,YACE,cAAe,CACf,kBACF,CAEA,YACE,cAAe,CACf,kBACF,CAGA,SACE,gCACF,CAEA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAGA,oBAEE,UAAW,CADX,SAEF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,eAKE,kBAAmB,CAHnB,iBAAkB,CAElB,YAAa,CADb,YAAa,CAGb,sBAAuB,CALvB,uBAMF,CAEA,qBAEE,+BAAyC,CADzC,0BAEF,CAEA,sBAEE,+BAA8C,CAD9C,0BAEF,CAEA,8BACE,iBACF,CAGA,uCACE,WACF,CAEA,4DAEE,gBAAuB,CACvB,WAAY,CAEZ,aAAc,CADd,eAAgB,CAHhB,gBAKF,CAEA,kEACE,wBACF,CAEA,uFACE,SACF,CAEA,wCAEE,aAAc,CADd,mBAEF,CAGA,gCAEE,eAAgB,CAChB,WAAY,CACZ,gBAAiB,CAHjB,QAIF,CAEA,sCACE,wBACF,CAEA,yCACE,wBAAyB,CACzB,8BAA+B,CAC/B,aACF,CAEA,+CACE,8BACF,CAGA,wFACE,gBAAiB,CACjB,iBACF,CAEA,oEACE,YACF,CAEA,4DACE,mBAAqB,CACrB,iBACF,CAGA,eACE,4BAA6B,CAC7B,YAAa,CACb,eACF,CAEA,aAGE,UAAW,CADX,cAAe,CAEf,eAAgB,CAEhB,mBAAqB,CALrB,gBAAiB,CAIjB,wBAEF", "sources": ["index.css", "../node_modules/antd/dist/reset.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n#root {\n  height: 100vh;\n}\n", "/* stylelint-disable */\nhtml,\nbody {\n  width: 100%;\n  height: 100%;\n}\ninput::-ms-clear,\ninput::-ms-reveal {\n  display: none;\n}\n*,\n*::before,\n*::after {\n  box-sizing: border-box;\n}\nhtml {\n  font-family: sans-serif;\n  line-height: 1.15;\n  -webkit-text-size-adjust: 100%;\n  -ms-text-size-adjust: 100%;\n  -ms-overflow-style: scrollbar;\n  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n}\n@-ms-viewport {\n  width: device-width;\n}\nbody {\n  margin: 0;\n}\n[tabindex='-1']:focus {\n  outline: none;\n}\nhr {\n  box-sizing: content-box;\n  height: 0;\n  overflow: visible;\n}\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  margin-top: 0;\n  margin-bottom: 0.5em;\n  font-weight: 500;\n}\np {\n  margin-top: 0;\n  margin-bottom: 1em;\n}\nabbr[title],\nabbr[data-original-title] {\n  -webkit-text-decoration: underline dotted;\n  text-decoration: underline;\n  text-decoration: underline dotted;\n  border-bottom: 0;\n  cursor: help;\n}\naddress {\n  margin-bottom: 1em;\n  font-style: normal;\n  line-height: inherit;\n}\ninput[type='text'],\ninput[type='password'],\ninput[type='number'],\ntextarea {\n  -webkit-appearance: none;\n}\nol,\nul,\ndl {\n  margin-top: 0;\n  margin-bottom: 1em;\n}\nol ol,\nul ul,\nol ul,\nul ol {\n  margin-bottom: 0;\n}\ndt {\n  font-weight: 500;\n}\ndd {\n  margin-bottom: 0.5em;\n  margin-left: 0;\n}\nblockquote {\n  margin: 0 0 1em;\n}\ndfn {\n  font-style: italic;\n}\nb,\nstrong {\n  font-weight: bolder;\n}\nsmall {\n  font-size: 80%;\n}\nsub,\nsup {\n  position: relative;\n  font-size: 75%;\n  line-height: 0;\n  vertical-align: baseline;\n}\nsub {\n  bottom: -0.25em;\n}\nsup {\n  top: -0.5em;\n}\npre,\ncode,\nkbd,\nsamp {\n  font-size: 1em;\n  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;\n}\npre {\n  margin-top: 0;\n  margin-bottom: 1em;\n  overflow: auto;\n}\nfigure {\n  margin: 0 0 1em;\n}\nimg {\n  vertical-align: middle;\n  border-style: none;\n}\na,\narea,\nbutton,\n[role='button'],\ninput:not([type='range']),\nlabel,\nselect,\nsummary,\ntextarea {\n  touch-action: manipulation;\n}\ntable {\n  border-collapse: collapse;\n}\ncaption {\n  padding-top: 0.75em;\n  padding-bottom: 0.3em;\n  text-align: left;\n  caption-side: bottom;\n}\ninput,\nbutton,\nselect,\noptgroup,\ntextarea {\n  margin: 0;\n  color: inherit;\n  font-size: inherit;\n  font-family: inherit;\n  line-height: inherit;\n}\nbutton,\ninput {\n  overflow: visible;\n}\nbutton,\nselect {\n  text-transform: none;\n}\nbutton,\nhtml [type='button'],\n[type='reset'],\n[type='submit'] {\n  -webkit-appearance: button;\n}\nbutton::-moz-focus-inner,\n[type='button']::-moz-focus-inner,\n[type='reset']::-moz-focus-inner,\n[type='submit']::-moz-focus-inner {\n  padding: 0;\n  border-style: none;\n}\ninput[type='radio'],\ninput[type='checkbox'] {\n  box-sizing: border-box;\n  padding: 0;\n}\ninput[type='date'],\ninput[type='time'],\ninput[type='datetime-local'],\ninput[type='month'] {\n  -webkit-appearance: listbox;\n}\ntextarea {\n  overflow: auto;\n  resize: vertical;\n}\nfieldset {\n  min-width: 0;\n  margin: 0;\n  padding: 0;\n  border: 0;\n}\nlegend {\n  display: block;\n  width: 100%;\n  max-width: 100%;\n  margin-bottom: 0.5em;\n  padding: 0;\n  color: inherit;\n  font-size: 1.5em;\n  line-height: inherit;\n  white-space: normal;\n}\nprogress {\n  vertical-align: baseline;\n}\n[type='number']::-webkit-inner-spin-button,\n[type='number']::-webkit-outer-spin-button {\n  height: auto;\n}\n[type='search'] {\n  outline-offset: -2px;\n  -webkit-appearance: none;\n}\n[type='search']::-webkit-search-cancel-button,\n[type='search']::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n::-webkit-file-upload-button {\n  font: inherit;\n  -webkit-appearance: button;\n}\noutput {\n  display: inline-block;\n}\nsummary {\n  display: list-item;\n}\ntemplate {\n  display: none;\n}\n[hidden] {\n  display: none !important;\n}\nmark {\n  padding: 0.2em;\n  background-color: #feffe6;\n}\n", "/* NovelCraft 应用样式 */\n\n.App {\n  height: 100vh;\n  overflow: hidden;\n}\n\n/* 全局样式重置 */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',\n    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background-color: #f5f5f5;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* 布局样式 */\n.layout-container {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.layout-header {\n  background: #fff;\n  padding: 0 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n}\n\n.layout-content {\n  flex: 1;\n  display: flex;\n  overflow: hidden;\n}\n\n.layout-sider {\n  background: #fff;\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);\n}\n\n.layout-main {\n  flex: 1;\n  padding: 24px;\n  overflow-y: auto;\n  background: #f5f5f5;\n}\n\n/* 页面标题 */\n.page-header {\n  margin-bottom: 24px;\n  padding: 16px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.page-title {\n  font-size: 24px;\n  font-weight: 600;\n  color: #262626;\n  margin: 0;\n}\n\n.page-description {\n  color: #8c8c8c;\n  margin-top: 8px;\n  margin-bottom: 0;\n}\n\n/* 卡片样式 */\n.content-card {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  margin-bottom: 24px;\n}\n\n.card-header {\n  padding: 16px 24px;\n  border-bottom: 1px solid #f0f0f0;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-title {\n  font-size: 16px;\n  font-weight: 600;\n  color: #262626;\n  margin: 0;\n}\n\n.card-content {\n  padding: 24px;\n}\n\n/* 工具栏样式 */\n.toolbar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 16px;\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n.toolbar-left {\n  display: flex;\n  align-items: center;\n  gap: 16px;\n}\n\n.toolbar-right {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n/* 统计卡片 */\n.stats-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 16px;\n  margin-bottom: 24px;\n}\n\n.stats-card {\n  background: #fff;\n  padding: 24px;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  text-align: center;\n}\n\n.stats-value {\n  font-size: 32px;\n  font-weight: 600;\n  color: #1890ff;\n  margin-bottom: 8px;\n}\n\n.stats-label {\n  color: #8c8c8c;\n  font-size: 14px;\n}\n\n/* 表格样式 */\n.table-container {\n  background: #fff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n}\n\n/* 编辑器样式 */\n.editor-container {\n  height: 500px;\n  border: 1px solid #d9d9d9;\n  border-radius: 6px;\n  overflow: hidden;\n}\n\n.editor-toolbar {\n  background: #fafafa;\n  border-bottom: 1px solid #d9d9d9;\n  padding: 8px 16px;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.editor-content {\n  height: calc(100% - 41px);\n}\n\n/* 侧边栏样式 */\n.sidebar-section {\n  margin-bottom: 24px;\n}\n\n.sidebar-title {\n  font-size: 14px;\n  font-weight: 600;\n  color: #262626;\n  margin-bottom: 12px;\n  padding: 0 16px;\n}\n\n.sidebar-item {\n  padding: 8px 16px;\n  cursor: pointer;\n  transition: background-color 0.2s;\n}\n\n.sidebar-item:hover {\n  background-color: #f5f5f5;\n}\n\n.sidebar-item.active {\n  background-color: #e6f7ff;\n  color: #1890ff;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .layout-main {\n    padding: 16px;\n  }\n\n  .stats-grid {\n    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n    gap: 12px;\n  }\n\n  .toolbar {\n    flex-direction: column;\n    gap: 12px;\n    align-items: stretch;\n  }\n\n  .toolbar-left,\n  .toolbar-right {\n    justify-content: center;\n  }\n}\n\n/* 加载状态 */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 200px;\n}\n\n/* 空状态 */\n.empty-container {\n  text-align: center;\n  padding: 48px 24px;\n  color: #8c8c8c;\n}\n\n.empty-icon {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-text {\n  font-size: 16px;\n  margin-bottom: 16px;\n}\n\n/* 动画效果 */\n.fade-in {\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* 滚动条样式 */\n::-webkit-scrollbar {\n  width: 8px;\n  height: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f1f1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #c1c1c1;\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #a8a8a8;\n}\n\n/* AI配置面板样式 */\n.provider-card {\n  transition: all 0.3s ease;\n  border-radius: 8px;\n  height: 100px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.provider-card:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.provider-card.active {\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);\n}\n\n.provider-card .ant-card-body {\n  padding: 16px 12px;\n}\n\n/* 可折叠菜单样式 */\n.ant-collapse-ghost > .ant-collapse-item {\n  border: none;\n}\n\n.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header {\n  padding: 8px 16px;\n  background: transparent;\n  border: none;\n  font-weight: 500;\n  color: #595959;\n}\n\n.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header:hover {\n  background-color: #f5f5f5;\n}\n\n.ant-collapse-ghost > .ant-collapse-item > .ant-collapse-content > .ant-collapse-content-box {\n  padding: 0;\n}\n\n.ant-collapse-ghost .ant-collapse-arrow {\n  right: 8px !important;\n  color: #8c8c8c;\n}\n\n/* 菜单项样式优化 */\n.ant-menu-inline .ant-menu-item {\n  margin: 0;\n  border-radius: 0;\n  height: 40px;\n  line-height: 40px;\n}\n\n.ant-menu-inline .ant-menu-item:hover {\n  background-color: #f0f0f0;\n}\n\n.ant-menu-inline .ant-menu-item-selected {\n  background-color: #e6f7ff;\n  border-right: 3px solid #1890ff;\n  color: #1890ff;\n}\n\n.ant-menu-inline .ant-menu-item-selected::after {\n  border-right: 3px solid #1890ff;\n}\n\n/* 侧边栏折叠状态下的样式 */\n.ant-layout-sider-collapsed .ant-collapse-ghost > .ant-collapse-item > .ant-collapse-header {\n  padding: 8px 12px;\n  text-align: center;\n}\n\n.ant-layout-sider-collapsed .ant-collapse-ghost .ant-collapse-arrow {\n  display: none;\n}\n\n.ant-layout-sider-collapsed .ant-menu-inline .ant-menu-item {\n  padding: 0 !important;\n  text-align: center;\n}\n\n/* 工具菜单分隔线 */\n.tools-divider {\n  border-top: 1px solid #f0f0f0;\n  margin: 8px 0;\n  padding-top: 8px;\n}\n\n.tools-title {\n  padding: 8px 16px;\n  font-size: 12px;\n  color: #999;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n"], "names": [], "sourceRoot": ""}