{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genSelectionStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSizeIcon,\n    padding,\n    paddingXS,\n    headerIconColor,\n    headerIconHoverColor,\n    tableSelectionColumnWidth,\n    tableSelectedRowBg,\n    tableSelectedRowHoverBg,\n    tableRowHoverBg,\n    tablePaddingHorizontal,\n    calc\n  } = token;\n  return {\n    [\"\".concat(componentCls, \"-wrapper\")]: {\n      // ========================== Selections ==========================\n      [\"\".concat(componentCls, \"-selection-col\")]: {\n        width: tableSelectionColumnWidth,\n        [\"&\".concat(componentCls, \"-selection-col-with-dropdown\")]: {\n          width: calc(tableSelectionColumnWidth).add(fontSizeIcon).add(calc(padding).div(4)).equal()\n        }\n      },\n      [\"\".concat(componentCls, \"-bordered \").concat(componentCls, \"-selection-col\")]: {\n        width: calc(tableSelectionColumnWidth).add(calc(paddingXS).mul(2)).equal(),\n        [\"&\".concat(componentCls, \"-selection-col-with-dropdown\")]: {\n          width: calc(tableSelectionColumnWidth).add(fontSizeIcon).add(calc(padding).div(4)).add(calc(paddingXS).mul(2)).equal()\n        }\n      },\n      [\"\\n        table tr th\".concat(componentCls, \"-selection-column,\\n        table tr td\").concat(componentCls, \"-selection-column,\\n        \").concat(componentCls, \"-selection-column\\n      \")]: {\n        paddingInlineEnd: token.paddingXS,\n        paddingInlineStart: token.paddingXS,\n        textAlign: 'center',\n        [\"\".concat(antCls, \"-radio-wrapper\")]: {\n          marginInlineEnd: 0\n        }\n      },\n      [\"table tr th\".concat(componentCls, \"-selection-column\").concat(componentCls, \"-cell-fix-left\")]: {\n        zIndex: calc(token.zIndexTableFixed).add(1).equal({\n          unit: false\n        })\n      },\n      [\"table tr th\".concat(componentCls, \"-selection-column::after\")]: {\n        backgroundColor: 'transparent !important'\n      },\n      [\"\".concat(componentCls, \"-selection\")]: {\n        position: 'relative',\n        display: 'inline-flex',\n        flexDirection: 'column'\n      },\n      [\"\".concat(componentCls, \"-selection-extra\")]: {\n        position: 'absolute',\n        top: 0,\n        zIndex: 1,\n        cursor: 'pointer',\n        transition: \"all \".concat(token.motionDurationSlow),\n        marginInlineStart: '100%',\n        paddingInlineStart: unit(calc(tablePaddingHorizontal).div(4).equal()),\n        [iconCls]: {\n          color: headerIconColor,\n          fontSize: fontSizeIcon,\n          verticalAlign: 'baseline',\n          '&:hover': {\n            color: headerIconHoverColor\n          }\n        }\n      },\n      // ============================= Rows =============================\n      [\"\".concat(componentCls, \"-tbody\")]: {\n        [\"\".concat(componentCls, \"-row\")]: {\n          [\"&\".concat(componentCls, \"-row-selected\")]: {\n            [\"> \".concat(componentCls, \"-cell\")]: {\n              background: tableSelectedRowBg,\n              '&-row-hover': {\n                background: tableSelectedRowHoverBg\n              }\n            }\n          },\n          [\"> \".concat(componentCls, \"-cell-row-hover\")]: {\n            background: tableRowHoverBg\n          }\n        }\n      }\n    }\n  };\n};\nexport default genSelectionStyle;", "map": {"version": 3, "names": ["unit", "genSelectionStyle", "token", "componentCls", "antCls", "iconCls", "fontSizeIcon", "padding", "paddingXS", "headerIconColor", "headerIconHoverColor", "tableSelectionColumnWidth", "tableSelectedRowBg", "tableSelectedRowHoverBg", "tableRowHoverBg", "tablePaddingHorizontal", "calc", "concat", "width", "add", "div", "equal", "mul", "paddingInlineEnd", "paddingInlineStart", "textAlign", "marginInlineEnd", "zIndex", "zIndexTableFixed", "backgroundColor", "position", "display", "flexDirection", "top", "cursor", "transition", "motionDurationSlow", "marginInlineStart", "color", "fontSize", "verticalAlign", "background"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/antd/es/table/style/selection.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genSelectionStyle = token => {\n  const {\n    componentCls,\n    antCls,\n    iconCls,\n    fontSizeIcon,\n    padding,\n    paddingXS,\n    headerIconColor,\n    headerIconHoverColor,\n    tableSelectionColumnWidth,\n    tableSelectedRowBg,\n    tableSelectedRowHoverBg,\n    tableRowHoverBg,\n    tablePaddingHorizontal,\n    calc\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      // ========================== Selections ==========================\n      [`${componentCls}-selection-col`]: {\n        width: tableSelectionColumnWidth,\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: calc(tableSelectionColumnWidth).add(fontSizeIcon).add(calc(padding).div(4)).equal()\n        }\n      },\n      [`${componentCls}-bordered ${componentCls}-selection-col`]: {\n        width: calc(tableSelectionColumnWidth).add(calc(paddingXS).mul(2)).equal(),\n        [`&${componentCls}-selection-col-with-dropdown`]: {\n          width: calc(tableSelectionColumnWidth).add(fontSizeIcon).add(calc(padding).div(4)).add(calc(paddingXS).mul(2)).equal()\n        }\n      },\n      [`\n        table tr th${componentCls}-selection-column,\n        table tr td${componentCls}-selection-column,\n        ${componentCls}-selection-column\n      `]: {\n        paddingInlineEnd: token.paddingXS,\n        paddingInlineStart: token.paddingXS,\n        textAlign: 'center',\n        [`${antCls}-radio-wrapper`]: {\n          marginInlineEnd: 0\n        }\n      },\n      [`table tr th${componentCls}-selection-column${componentCls}-cell-fix-left`]: {\n        zIndex: calc(token.zIndexTableFixed).add(1).equal({\n          unit: false\n        })\n      },\n      [`table tr th${componentCls}-selection-column::after`]: {\n        backgroundColor: 'transparent !important'\n      },\n      [`${componentCls}-selection`]: {\n        position: 'relative',\n        display: 'inline-flex',\n        flexDirection: 'column'\n      },\n      [`${componentCls}-selection-extra`]: {\n        position: 'absolute',\n        top: 0,\n        zIndex: 1,\n        cursor: 'pointer',\n        transition: `all ${token.motionDurationSlow}`,\n        marginInlineStart: '100%',\n        paddingInlineStart: unit(calc(tablePaddingHorizontal).div(4).equal()),\n        [iconCls]: {\n          color: headerIconColor,\n          fontSize: fontSizeIcon,\n          verticalAlign: 'baseline',\n          '&:hover': {\n            color: headerIconHoverColor\n          }\n        }\n      },\n      // ============================= Rows =============================\n      [`${componentCls}-tbody`]: {\n        [`${componentCls}-row`]: {\n          [`&${componentCls}-row-selected`]: {\n            [`> ${componentCls}-cell`]: {\n              background: tableSelectedRowBg,\n              '&-row-hover': {\n                background: tableSelectedRowHoverBg\n              }\n            }\n          },\n          [`> ${componentCls}-cell-row-hover`]: {\n            background: tableRowHoverBg\n          }\n        }\n      }\n    }\n  };\n};\nexport default genSelectionStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,iBAAiB,GAAGC,KAAK,IAAI;EACjC,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,OAAO;IACPC,YAAY;IACZC,OAAO;IACPC,SAAS;IACTC,eAAe;IACfC,oBAAoB;IACpBC,yBAAyB;IACzBC,kBAAkB;IAClBC,uBAAuB;IACvBC,eAAe;IACfC,sBAAsB;IACtBC;EACF,CAAC,GAAGd,KAAK;EACT,OAAO;IACL,IAAAe,MAAA,CAAId,YAAY,gBAAa;MAC3B;MACA,IAAAc,MAAA,CAAId,YAAY,sBAAmB;QACjCe,KAAK,EAAEP,yBAAyB;QAChC,KAAAM,MAAA,CAAKd,YAAY,oCAAiC;UAChDe,KAAK,EAAEF,IAAI,CAACL,yBAAyB,CAAC,CAACQ,GAAG,CAACb,YAAY,CAAC,CAACa,GAAG,CAACH,IAAI,CAACT,OAAO,CAAC,CAACa,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC;QAC3F;MACF,CAAC;MACD,IAAAJ,MAAA,CAAId,YAAY,gBAAAc,MAAA,CAAad,YAAY,sBAAmB;QAC1De,KAAK,EAAEF,IAAI,CAACL,yBAAyB,CAAC,CAACQ,GAAG,CAACH,IAAI,CAACR,SAAS,CAAC,CAACc,GAAG,CAAC,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;QAC1E,KAAAJ,MAAA,CAAKd,YAAY,oCAAiC;UAChDe,KAAK,EAAEF,IAAI,CAACL,yBAAyB,CAAC,CAACQ,GAAG,CAACb,YAAY,CAAC,CAACa,GAAG,CAACH,IAAI,CAACT,OAAO,CAAC,CAACa,GAAG,CAAC,CAAC,CAAC,CAAC,CAACD,GAAG,CAACH,IAAI,CAACR,SAAS,CAAC,CAACc,GAAG,CAAC,CAAC,CAAC,CAAC,CAACD,KAAK,CAAC;QACvH;MACF,CAAC;MACD,yBAAAJ,MAAA,CACed,YAAY,6CAAAc,MAAA,CACZd,YAAY,kCAAAc,MAAA,CACvBd,YAAY,iCACZ;QACFoB,gBAAgB,EAAErB,KAAK,CAACM,SAAS;QACjCgB,kBAAkB,EAAEtB,KAAK,CAACM,SAAS;QACnCiB,SAAS,EAAE,QAAQ;QACnB,IAAAR,MAAA,CAAIb,MAAM,sBAAmB;UAC3BsB,eAAe,EAAE;QACnB;MACF,CAAC;MACD,eAAAT,MAAA,CAAed,YAAY,uBAAAc,MAAA,CAAoBd,YAAY,sBAAmB;QAC5EwB,MAAM,EAAEX,IAAI,CAACd,KAAK,CAAC0B,gBAAgB,CAAC,CAACT,GAAG,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC;UAChDrB,IAAI,EAAE;QACR,CAAC;MACH,CAAC;MACD,eAAAiB,MAAA,CAAed,YAAY,gCAA6B;QACtD0B,eAAe,EAAE;MACnB,CAAC;MACD,IAAAZ,MAAA,CAAId,YAAY,kBAAe;QAC7B2B,QAAQ,EAAE,UAAU;QACpBC,OAAO,EAAE,aAAa;QACtBC,aAAa,EAAE;MACjB,CAAC;MACD,IAAAf,MAAA,CAAId,YAAY,wBAAqB;QACnC2B,QAAQ,EAAE,UAAU;QACpBG,GAAG,EAAE,CAAC;QACNN,MAAM,EAAE,CAAC;QACTO,MAAM,EAAE,SAAS;QACjBC,UAAU,SAAAlB,MAAA,CAASf,KAAK,CAACkC,kBAAkB,CAAE;QAC7CC,iBAAiB,EAAE,MAAM;QACzBb,kBAAkB,EAAExB,IAAI,CAACgB,IAAI,CAACD,sBAAsB,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;QACrE,CAAChB,OAAO,GAAG;UACTiC,KAAK,EAAE7B,eAAe;UACtB8B,QAAQ,EAAEjC,YAAY;UACtBkC,aAAa,EAAE,UAAU;UACzB,SAAS,EAAE;YACTF,KAAK,EAAE5B;UACT;QACF;MACF,CAAC;MACD;MACA,IAAAO,MAAA,CAAId,YAAY,cAAW;QACzB,IAAAc,MAAA,CAAId,YAAY,YAAS;UACvB,KAAAc,MAAA,CAAKd,YAAY,qBAAkB;YACjC,MAAAc,MAAA,CAAMd,YAAY,aAAU;cAC1BsC,UAAU,EAAE7B,kBAAkB;cAC9B,aAAa,EAAE;gBACb6B,UAAU,EAAE5B;cACd;YACF;UACF,CAAC;UACD,MAAAI,MAAA,CAAMd,YAAY,uBAAoB;YACpCsC,UAAU,EAAE3B;UACd;QACF;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeb,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}