{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { format } from \"../util\";\nimport required from \"./required\";\nimport getUrlRegex from \"./url\";\n/* eslint max-len:0 */\n\nvar pattern = {\n  // http://emailregex.com/\n  email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]+\\.)+[a-zA-Z\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{2,}))$/,\n  // url: new RegExp(\n  //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$',\n  //   'i',\n  // ),\n  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i\n};\nvar types = {\n  integer: function integer(value) {\n    return types.number(value) && parseInt(value, 10) === value;\n  },\n  float: function float(value) {\n    return types.number(value) && !types.integer(value);\n  },\n  array: function array(value) {\n    return Array.isArray(value);\n  },\n  regexp: function regexp(value) {\n    if (value instanceof RegExp) {\n      return true;\n    }\n    try {\n      return !!new RegExp(value);\n    } catch (e) {\n      return false;\n    }\n  },\n  date: function date(value) {\n    return typeof value.getTime === 'function' && typeof value.getMonth === 'function' && typeof value.getYear === 'function' && !isNaN(value.getTime());\n  },\n  number: function number(value) {\n    if (isNaN(value)) {\n      return false;\n    }\n    return typeof value === 'number';\n  },\n  object: function object(value) {\n    return _typeof(value) === 'object' && !types.array(value);\n  },\n  method: function method(value) {\n    return typeof value === 'function';\n  },\n  email: function email(value) {\n    return typeof value === 'string' && value.length <= 320 && !!value.match(pattern.email);\n  },\n  url: function url(value) {\n    return typeof value === 'string' && value.length <= 2048 && !!value.match(getUrlRegex());\n  },\n  hex: function hex(value) {\n    return typeof value === 'string' && !!value.match(pattern.hex);\n  }\n};\nvar type = function type(rule, value, source, errors, options) {\n  if (rule.required && value === undefined) {\n    required(rule, value, source, errors, options);\n    return;\n  }\n  var custom = ['integer', 'float', 'array', 'regexp', 'object', 'method', 'email', 'number', 'date', 'url', 'hex'];\n  var ruleType = rule.type;\n  if (custom.indexOf(ruleType) > -1) {\n    if (!types[ruleType](value)) {\n      errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));\n    }\n    // straight typeof check\n  } else if (ruleType && _typeof(value) !== rule.type) {\n    errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));\n  }\n};\nexport default type;", "map": {"version": 3, "names": ["_typeof", "format", "required", "getUrlRegex", "pattern", "email", "hex", "types", "integer", "value", "number", "parseInt", "float", "array", "Array", "isArray", "regexp", "RegExp", "e", "date", "getTime", "getMonth", "getYear", "isNaN", "object", "method", "length", "match", "url", "type", "rule", "source", "errors", "options", "undefined", "custom", "ruleType", "indexOf", "push", "messages", "fullField"], "sources": ["D:/AI_project/小说架构管理及AI生成软件/frontend/node_modules/@rc-component/async-validator/es/rule/type.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { format } from \"../util\";\nimport required from \"./required\";\nimport getUrlRegex from \"./url\";\n/* eslint max-len:0 */\n\nvar pattern = {\n  // http://emailregex.com/\n  email: /^(([^<>()\\[\\]\\\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]+\\.)+[a-zA-Z\\u00A0-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{2,}))$/,\n  // url: new RegExp(\n  //   '^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\\\S+(?::\\\\S*)?@)?(?:(?:(?:[1-9]\\\\d?|1\\\\d\\\\d|2[01]\\\\d|22[0-3])(?:\\\\.(?:1?\\\\d{1,2}|2[0-4]\\\\d|25[0-5])){2}(?:\\\\.(?:[0-9]\\\\d?|1\\\\d\\\\d|2[0-4]\\\\d|25[0-4]))|(?:(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff0-9]+-*)*[a-z\\\\u00a1-\\\\uffff0-9]+)*(?:\\\\.(?:[a-z\\\\u00a1-\\\\uffff]{2,})))|localhost)(?::\\\\d{2,5})?(?:(/|\\\\?|#)[^\\\\s]*)?$',\n  //   'i',\n  // ),\n  hex: /^#?([a-f0-9]{6}|[a-f0-9]{3})$/i\n};\nvar types = {\n  integer: function integer(value) {\n    return types.number(value) && parseInt(value, 10) === value;\n  },\n  float: function float(value) {\n    return types.number(value) && !types.integer(value);\n  },\n  array: function array(value) {\n    return Array.isArray(value);\n  },\n  regexp: function regexp(value) {\n    if (value instanceof RegExp) {\n      return true;\n    }\n    try {\n      return !!new RegExp(value);\n    } catch (e) {\n      return false;\n    }\n  },\n  date: function date(value) {\n    return typeof value.getTime === 'function' && typeof value.getMonth === 'function' && typeof value.getYear === 'function' && !isNaN(value.getTime());\n  },\n  number: function number(value) {\n    if (isNaN(value)) {\n      return false;\n    }\n    return typeof value === 'number';\n  },\n  object: function object(value) {\n    return _typeof(value) === 'object' && !types.array(value);\n  },\n  method: function method(value) {\n    return typeof value === 'function';\n  },\n  email: function email(value) {\n    return typeof value === 'string' && value.length <= 320 && !!value.match(pattern.email);\n  },\n  url: function url(value) {\n    return typeof value === 'string' && value.length <= 2048 && !!value.match(getUrlRegex());\n  },\n  hex: function hex(value) {\n    return typeof value === 'string' && !!value.match(pattern.hex);\n  }\n};\nvar type = function type(rule, value, source, errors, options) {\n  if (rule.required && value === undefined) {\n    required(rule, value, source, errors, options);\n    return;\n  }\n  var custom = ['integer', 'float', 'array', 'regexp', 'object', 'method', 'email', 'number', 'date', 'url', 'hex'];\n  var ruleType = rule.type;\n  if (custom.indexOf(ruleType) > -1) {\n    if (!types[ruleType](value)) {\n      errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));\n    }\n    // straight typeof check\n  } else if (ruleType && _typeof(value) !== rule.type) {\n    errors.push(format(options.messages.types[ruleType], rule.fullField, rule.type));\n  }\n};\nexport default type;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,SAASC,MAAM,QAAQ,SAAS;AAChC,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,WAAW,MAAM,OAAO;AAC/B;;AAEA,IAAIC,OAAO,GAAG;EACZ;EACAC,KAAK,EAAE,sOAAsO;EAC7O;EACA;EACA;EACA;EACAC,GAAG,EAAE;AACP,CAAC;AACD,IAAIC,KAAK,GAAG;EACVC,OAAO,EAAE,SAASA,OAAOA,CAACC,KAAK,EAAE;IAC/B,OAAOF,KAAK,CAACG,MAAM,CAACD,KAAK,CAAC,IAAIE,QAAQ,CAACF,KAAK,EAAE,EAAE,CAAC,KAAKA,KAAK;EAC7D,CAAC;EACDG,KAAK,EAAE,SAASA,KAAKA,CAACH,KAAK,EAAE;IAC3B,OAAOF,KAAK,CAACG,MAAM,CAACD,KAAK,CAAC,IAAI,CAACF,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC;EACrD,CAAC;EACDI,KAAK,EAAE,SAASA,KAAKA,CAACJ,KAAK,EAAE;IAC3B,OAAOK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC;EAC7B,CAAC;EACDO,MAAM,EAAE,SAASA,MAAMA,CAACP,KAAK,EAAE;IAC7B,IAAIA,KAAK,YAAYQ,MAAM,EAAE;MAC3B,OAAO,IAAI;IACb;IACA,IAAI;MACF,OAAO,CAAC,CAAC,IAAIA,MAAM,CAACR,KAAK,CAAC;IAC5B,CAAC,CAAC,OAAOS,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF,CAAC;EACDC,IAAI,EAAE,SAASA,IAAIA,CAACV,KAAK,EAAE;IACzB,OAAO,OAAOA,KAAK,CAACW,OAAO,KAAK,UAAU,IAAI,OAAOX,KAAK,CAACY,QAAQ,KAAK,UAAU,IAAI,OAAOZ,KAAK,CAACa,OAAO,KAAK,UAAU,IAAI,CAACC,KAAK,CAACd,KAAK,CAACW,OAAO,CAAC,CAAC,CAAC;EACtJ,CAAC;EACDV,MAAM,EAAE,SAASA,MAAMA,CAACD,KAAK,EAAE;IAC7B,IAAIc,KAAK,CAACd,KAAK,CAAC,EAAE;MAChB,OAAO,KAAK;IACd;IACA,OAAO,OAAOA,KAAK,KAAK,QAAQ;EAClC,CAAC;EACDe,MAAM,EAAE,SAASA,MAAMA,CAACf,KAAK,EAAE;IAC7B,OAAOT,OAAO,CAACS,KAAK,CAAC,KAAK,QAAQ,IAAI,CAACF,KAAK,CAACM,KAAK,CAACJ,KAAK,CAAC;EAC3D,CAAC;EACDgB,MAAM,EAAE,SAASA,MAAMA,CAAChB,KAAK,EAAE;IAC7B,OAAO,OAAOA,KAAK,KAAK,UAAU;EACpC,CAAC;EACDJ,KAAK,EAAE,SAASA,KAAKA,CAACI,KAAK,EAAE;IAC3B,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACiB,MAAM,IAAI,GAAG,IAAI,CAAC,CAACjB,KAAK,CAACkB,KAAK,CAACvB,OAAO,CAACC,KAAK,CAAC;EACzF,CAAC;EACDuB,GAAG,EAAE,SAASA,GAAGA,CAACnB,KAAK,EAAE;IACvB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACiB,MAAM,IAAI,IAAI,IAAI,CAAC,CAACjB,KAAK,CAACkB,KAAK,CAACxB,WAAW,CAAC,CAAC,CAAC;EAC1F,CAAC;EACDG,GAAG,EAAE,SAASA,GAAGA,CAACG,KAAK,EAAE;IACvB,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC,CAACA,KAAK,CAACkB,KAAK,CAACvB,OAAO,CAACE,GAAG,CAAC;EAChE;AACF,CAAC;AACD,IAAIuB,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAErB,KAAK,EAAEsB,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC7D,IAAIH,IAAI,CAAC5B,QAAQ,IAAIO,KAAK,KAAKyB,SAAS,EAAE;IACxChC,QAAQ,CAAC4B,IAAI,EAAErB,KAAK,EAAEsB,MAAM,EAAEC,MAAM,EAAEC,OAAO,CAAC;IAC9C;EACF;EACA,IAAIE,MAAM,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;EACjH,IAAIC,QAAQ,GAAGN,IAAI,CAACD,IAAI;EACxB,IAAIM,MAAM,CAACE,OAAO,CAACD,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;IACjC,IAAI,CAAC7B,KAAK,CAAC6B,QAAQ,CAAC,CAAC3B,KAAK,CAAC,EAAE;MAC3BuB,MAAM,CAACM,IAAI,CAACrC,MAAM,CAACgC,OAAO,CAACM,QAAQ,CAAChC,KAAK,CAAC6B,QAAQ,CAAC,EAAEN,IAAI,CAACU,SAAS,EAAEV,IAAI,CAACD,IAAI,CAAC,CAAC;IAClF;IACA;EACF,CAAC,MAAM,IAAIO,QAAQ,IAAIpC,OAAO,CAACS,KAAK,CAAC,KAAKqB,IAAI,CAACD,IAAI,EAAE;IACnDG,MAAM,CAACM,IAAI,CAACrC,MAAM,CAACgC,OAAO,CAACM,QAAQ,CAAChC,KAAK,CAAC6B,QAAQ,CAAC,EAAEN,IAAI,CAACU,SAAS,EAAEV,IAAI,CAACD,IAAI,CAAC,CAAC;EAClF;AACF,CAAC;AACD,eAAeA,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}