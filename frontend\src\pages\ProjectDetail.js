import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  Tag,
  Descriptions,
  message
} from 'antd';
import {
  EditOutlined,
  SettingOutlined,
  ExportOutlined,
  BackwardOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

const ProjectDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);

  // 模拟项目数据
  const mockProject = {
    id: 1,
    name: '仙侠传说',
    title: '九天仙缘录',
    author: '作者A',
    type: 'xianxia',
    status: 'writing',
    summary: '这是一个关于修仙者在九天之上寻找仙缘的故事...',
    description: '详细的项目描述...',
    wordCount: 89000,
    chapterCount: 45,
    characterCount: 12,
    factionCount: 8,
    plotCount: 15,
    progress: 65,
    createdAt: '2024-01-01',
    updatedAt: '2024-01-15'
  };

  const loadProject = useCallback(async () => {
    setLoading(true);
    try {
      // 模拟API调用
      setTimeout(() => {
        setProject(mockProject);
        setLoading(false);
      }, 1000);
    } catch (error) {
      message.error('加载项目详情失败');
      setLoading(false);
    }
  }, [mockProject]);

  useEffect(() => {
    loadProject();
  }, [id, loadProject]);

  const getStatusColor = (status) => {
    const colors = {
      planning: 'blue',
      writing: 'green',
      reviewing: 'orange',
      completed: 'purple',
      published: 'gold'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      planning: '规划中',
      writing: '写作中',
      reviewing: '审阅中',
      completed: '已完成',
      published: '已发布'
    };
    return texts[status] || status;
  };

  const getTypeText = (type) => {
    const texts = {
      fantasy: '奇幻',
      xianxia: '仙侠',
      wuxia: '武侠',
      scifi: '科幻',
      modern: '现代',
      historical: '历史',
      romance: '言情'
    };
    return texts[type] || type;
  };

  if (loading || !project) {
    return <div className="loading-container">加载中...</div>;
  }

  return (
    <div className="fade-in">
      <div className="page-header">
        <Space>
          <Button
            icon={<BackwardOutlined />}
            onClick={() => navigate('/projects')}
          >
            返回项目列表
          </Button>
          <Title level={2} className="page-title">{project.name}</Title>
          <Tag color={getStatusColor(project.status)}>
            {getStatusText(project.status)}
          </Tag>
          <Tag>{getTypeText(project.type)}</Tag>
        </Space>

        <Space style={{ marginTop: 16 }}>
          <Button type="primary" icon={<EditOutlined />}>
            编辑项目
          </Button>
          <Button icon={<SettingOutlined />}>
            项目设置
          </Button>
          <Button icon={<ExportOutlined />}>
            导出项目
          </Button>
        </Space>
      </div>

      {/* 项目统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总字数"
              value={project.wordCount}
              formatter={(value) => `${(value / 10000).toFixed(1)}万`}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="章节数"
              value={project.chapterCount}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="人物数"
              value={project.characterCount}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="完成进度"
              value={project.progress}
              suffix="%"
              valueStyle={{ color: '#fa8c16' }}
            />
            <Progress
              percent={project.progress}
              size="small"
              status={project.progress === 100 ? 'success' : 'active'}
              style={{ marginTop: 8 }}
            />
          </Card>
        </Col>
      </Row>

      {/* 项目概览 */}
      <Card title="项目概览">
        <Descriptions bordered column={2}>
          <Descriptions.Item label="项目名称">{project.name}</Descriptions.Item>
          <Descriptions.Item label="小说标题">{project.title}</Descriptions.Item>
          <Descriptions.Item label="作者">{project.author}</Descriptions.Item>
          <Descriptions.Item label="项目类型">{getTypeText(project.type)}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{project.createdAt}</Descriptions.Item>
          <Descriptions.Item label="最后修改">{project.updatedAt}</Descriptions.Item>
          <Descriptions.Item label="项目简介" span={2}>
            {project.summary}
          </Descriptions.Item>
          <Descriptions.Item label="详细描述" span={2}>
            {project.description}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 快速访问 */}
      <Card title="快速访问" style={{ marginTop: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8}>
            <Card
              title="卷宗管理"
              extra={<Button type="link">进入</Button>}
              onClick={() => navigate(`/projects/${id}/volumes`)}
              style={{ cursor: 'pointer' }}
              hoverable
            >
              <Statistic value={project.volumeCount || 0} suffix="个卷宗" />
              <Text type="secondary">管理小说卷宗和章节</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card
              title="内容管理"
              extra={<Button type="link">进入</Button>}
              onClick={() => navigate(`/projects/${id}/content`)}
              style={{ cursor: 'pointer' }}
              hoverable
            >
              <Statistic value={project.characterCount + project.factionCount} suffix="个内容项" />
              <Text type="secondary">管理角色、势力、剧情</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card
              title="设定管理"
              extra={<Button type="link">进入</Button>}
              onClick={() => navigate(`/projects/${id}/settings`)}
              style={{ cursor: 'pointer' }}
              hoverable
            >
              <Text>体系设定</Text>
              <br />
              <Text type="secondary">管理世界观和各类体系</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card
              title="AI助手"
              extra={<Button type="link">使用</Button>}
              onClick={() => navigate('/ai-assistant')}
              style={{ cursor: 'pointer' }}
              hoverable
            >
              <Text>AI辅助创作</Text>
              <br />
              <Text type="secondary">智能生成、续写、分析</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card
              title="时间线"
              extra={<Button type="link">查看</Button>}
              onClick={() => navigate(`/projects/${id}/timeline`)}
              style={{ cursor: 'pointer' }}
              hoverable
            >
              <Text>项目时间线</Text>
              <br />
              <Text type="secondary">事件、发展、历史</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Card
              title="关系网络"
              extra={<Button type="link">查看</Button>}
              onClick={() => navigate(`/projects/${id}/relations`)}
              style={{ cursor: 'pointer' }}
              hoverable
            >
              <Text>人物关系图谱</Text>
              <br />
              <Text type="secondary">人物、势力、关系</Text>
            </Card>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default ProjectDetail;
